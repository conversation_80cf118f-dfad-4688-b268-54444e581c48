services:
  minio:
    image: minio/minio:latest
    container_name: conversational-bim-minio
    ports:
      - "${MINIO_PORT:-9000}:9000"
      - "${MINIO_CONSOLE_PORT:-9001}:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minioadmin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minioadmin123}
    command: server /data --console-address ":9001"
    volumes:
      - ./storage-data:/data/
    networks:
      - conversational-bim
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant
    container_name: conversational-bim-qdrant
    ports:
      - "${QDRANT_PORT:-6333}:6333"
    volumes:
      - ./qdrant-data:/qdrant/storage/
    networks:
      - conversational-bim
    restart: unless-stopped

  graphdb:
    build:
      context: ./graphdb-docker
      args:
        version: ${GRAPHDB_VERSION:-10.7.0}
    container_name: conversational-bim-graphdb
    ports:
      - "${GRAPHDB_HTTP_PORT:-7200}:7200"
      - "${GRAPHDB_RPC_PORT:-7300}:7300"
    environment:
      GDB_JAVA_OPTS: ${GDB_JAVA_OPTS:--Dgraphdb.workbench.importDirectory=/root/graphdb-import}
      graphdb.llm.api: openai-completions
      graphdb.llm.api-key: ${GRAPHDB_LLM_API_KEY:-sk-}
      graphdb.llm.model: ${GRAPHDB_LLM_MODEL:-gpt-4.1-mini}
    volumes:
      - graphdb-data:/opt/graphdb/home
      - graphdb-import:/root/graphdb-import
      # Uncomment the line below if you have a GraphDB license file
      # - ${GRAPHDB_LICENSE_PATH}:/opt/graphdb/home/<USER>/graphdb.license
    networks:
      - conversational-bim
    restart: unless-stopped

networks:
  conversational-bim:
    driver: bridge

volumes:
  graphdb-data:
    driver: local
  graphdb-import:
    driver: local