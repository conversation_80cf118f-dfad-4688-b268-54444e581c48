# %% [markdown]
# # Simple PDF Document Extractor with Docling
# 
# This notebook demonstrates how to extract text and content from PDF documents using the Docling library.

# %%
# Install docling - only need to run this once
!pip install docling

# %%
# Import necessary libraries
from docling.document_converter import DocumentConverter
import os
from pathlib import Path

# %%
# Define the path to the example PDF
pdf_path = "assets/example.pdf"

# Check if the file exists
if os.path.exists(pdf_path):
    print(f"✅ PDF file found: {pdf_path}")
    print(f"📄 File size: {os.path.getsize(pdf_path) / 1024:.2f} KB")
else:
    print(f"❌ PDF file not found: {pdf_path}")
    print("Current directory contents:")
    print(os.listdir("."))

# %%
# Initialize the DocumentConverter
converter = DocumentConverter()

print("📚 DocumentConverter initialized successfully!")
print("🔧 Ready to extract content from PDF documents")

# %%
# Extract content from the PDF
print("🔄 Processing PDF document...")

# Convert the PDF document
result = converter.convert(pdf_path)
document = result.document

print("✅ PDF processed successfully!")
print(f"📊 Document contains {len(document.pages)} page(s)")

# Display basic document information
print("\n📋 Document Information:")
# Check available attributes
print(f"• Document type: {type(document).__name__}")
print(f"• Pages: {len(document.pages)}")

# Get counts of different elements
text_count = len(document.texts) if hasattr(document, 'texts') else 0
table_count = len(document.tables) if hasattr(document, 'tables') else 0  
picture_count = len(document.pictures) if hasattr(document, 'pictures') else 0

print(f"• Total elements: {text_count + table_count + picture_count}")
print(f"• Text elements: {text_count}")
print(f"• Tables: {table_count}")
print(f"• Pictures: {picture_count}")

# %%
# Extract text content as markdown
markdown_content = document.export_to_markdown()

print("📝 Text Content (Markdown format):")
print("=" * 50)
print(markdown_content)
print("=" * 50)

# %%
# Extract and display individual text elements
print("📑 Individual Text Elements:")
print("=" * 50)

for i, text_element in enumerate(document.texts[:5]):  # Show first 5 text elements
    print(f"\n🔹 Text Element {i+1}:")
    print(f"   Content: {text_element.text[:100]}{'...' if len(text_element.text) > 100 else ''}")
    print(f"   Label: {text_element.label}")

if len(document.texts) > 5:
    print(f"\n... and {len(document.texts) - 5} more text elements")

print("=" * 50)

# %%
# Extract tables if any exist
if document.tables:
    print("📊 Tables Found:")
    print("=" * 50)
    
    for i, table in enumerate(document.tables):
        print(f"\n🔹 Table {i+1}:")
        
        # Check available attributes
        table_attrs = [attr for attr in dir(table) if not attr.startswith('_')]
        print(f"   Available attributes: {table_attrs[:5]}...")  # Show first 5 attributes
        
        # Try to get table text content
        if hasattr(table, 'text') and table.text:
            print(f"   Content preview: {table.text[:100]}{'...' if len(table.text) > 100 else ''}")
        
        # Try to export as markdown if available
        if hasattr(table, 'export_to_markdown'):
            try:
                table_md = table.export_to_markdown()
                print(f"   Markdown preview: {table_md[:200]}{'...' if len(table_md) > 200 else ''}")
            except Exception as e:
                print(f"   Could not export to markdown: {e}")
                
else:
    print("📊 No tables found in the document")

print("=" * 50)

# %%
# Save extracted content to files
output_dir = "extracted_content"
os.makedirs(output_dir, exist_ok=True)

# Save as markdown
markdown_file = os.path.join(output_dir, "extracted_content.md")
with open(markdown_file, 'w', encoding='utf-8') as f:
    f.write(markdown_content)

# Save as JSON for structured access
json_content = document.export_to_dict()
import json
json_file = os.path.join(output_dir, "extracted_content.json")
with open(json_file, 'w', encoding='utf-8') as f:
    json.dump(json_content, f, indent=2, ensure_ascii=False)

print(f"💾 Content saved to:")
print(f"   📄 Markdown: {markdown_file}")
print(f"   📄 JSON: {json_file}")
print(f"   📁 Output directory: {output_dir}")

# Display directory contents
print(f"\n📁 Files in {output_dir}:")
for file in os.listdir(output_dir):
    file_path = os.path.join(output_dir, file)
    size_kb = os.path.getsize(file_path) / 1024
    print(f"   • {file} ({size_kb:.2f} KB)")

# %% [markdown]
# ## Summary
# 
# This simple PDF document extractor demonstrates the core capabilities of Docling:
# 
# 1. **Easy Setup**: Just install docling with `pip install docling`
# 2. **Simple API**: Initialize `DocumentConverter()` and call `convert()`
# 3. **Multiple Formats**: Export to Markdown, JSON, or access structured data
# 4. **Rich Content**: Extracts text, tables, images, and preserves document structure
# 5. **Metadata**: Provides information about document elements and their types
# 
# ### Key Features Used:
# - **Text Extraction**: All text content with structure preservation
# - **Table Detection**: Automatic table extraction with structure
# - **Markdown Export**: Clean, readable markdown format
# - **JSON Export**: Structured data for programmatic access
# - **Element Analysis**: Individual text elements with labels and metadata
# 
# ### Next Steps:
# - Try with different PDF documents
# - Explore advanced options like OCR settings
# - Use table structure recognition for complex tables
# - Integrate with document processing pipelines


