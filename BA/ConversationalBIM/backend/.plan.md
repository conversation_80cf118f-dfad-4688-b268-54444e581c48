# FastAPI RDF Triplestore Service Architecture
General idea:
The service provides a way to interact with a rdf triplestore database using LLMs:
It can list available repositories (the naming of a triplestore database in graphdb) it allows adding a new repo.
When a new db is added, a special processing happen that adds to the tripple store, embed a document and save them to a vector store and minio instance. These are called a "use-case", so a use case has its graph in the graphdb, the graphdb links to minio using a field for the document path and based on a document path we can check the vector store to retrieve some information that potentially answer the user question.

It allows asking an AI agent a question about these. The agent has access to different tools and decides which one to use. A tool may query the graphdb, another tool may use RAG to answer a question from a document etc.. 

```
rdf_llm_service/
├── app/
│   ├── __init__.py
│   ├── main.py                     # FastAPI application entry point
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py         # FastAPI dependencies (auth, db connections)
│   │   └── v1/
│   │       ├── __init__.py
│   │       └── endpoints.py        # All API endpoints in one file
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py               # Settings and configuration loading
│   │   ├── security.py             # Authentication/authorization
│   │   └── exceptions.py           # Custom exception classes
│   ├── models/
│   │   ├── __init__.py
│   │   ├── database.py             # SQLAlchemy/database models
│   │   ├── schemas.py              # Pydantic request/response models
│   │   └── entities.py             # Domain entities/data classes
│   ├── services/
│   │   ├── __init__.py
│   │   ├── repository_service.py   # Repository management logic
│   │   ├── use_case_service.py     # Use case processing logic
│   │   ├── document_processor.py   # Document embedding & processing
│   │   └── ai_agent_service.py     # AI agent orchestration
│   ├── agents/
│   │   ├── __init__.py
│   │   ├── models.py               # Pydantic AI agent models
│   │   ├── chat_agent.py           # Main chat agent implementation
│   │   └── tools/
│   │       ├── __init__.py
│   │       ├── base_tool.py        # Base tool interface
│   │       ├── graphdb_tool.py     # GraphDB query tool
│   │       ├── rag_tool.py         # RAG/vector search tool
│   │       └── minio_tool.py       # MinIO document retrieval tool
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── graphdb.py              # GraphDB connection & operations
│   │   ├── vector_store.py         # Vector database operations
│   │   ├── minio_client.py         # MinIO client & operations
│   │   └── database.py             # SQL database operations
│   └── utils/
│       ├── __init__.py
│       ├── embeddings.py           # Text embedding utilities
│       ├── document_parser.py      # Document parsing utilities
│       └── logging.py              # Logging configuration
├── config/
│   ├── config.yaml                 # Main configuration file
│   ├── prompts.yaml                # LLM prompts and templates
│   └── models.yaml                 # LLM model configurations
├── tests/
│   ├── __init__.py
│   ├── conftest.py                 # Pytest configuration
│   ├── unit/
│   │   ├── test_services/
│   │   ├── test_agents/
│   │   └── test_storage/
│   └── integration/
│       ├── test_api/
│       └── test_workflows/
├── migrations/                     # Database migrations (if using SQL)
│   └── versions/
├── scripts/
│   ├── setup_db.py                 # Database initialization
│   └── seed_data.py                # Sample data loading
├── .env.example                    # Environment variables template
├── .env                            # Environment variables (gitignored)
├── .gitignore
├── requirements.txt                # Production dependencies
├── requirements-dev.txt            # Development dependencies
├── docker-compose.yml              # Local development setup
├── Dockerfile
└── README.md
```

## Architecture Explanation

### **Core Components**

#### **`app/main.py`**
- FastAPI application factory
- Middleware setup
- Route registration
- Startup/shutdown events

#### **`app/api/`**
- **`v1/endpoints.py`**: All REST API endpoints in one file
  - Repository CRUD operations
  - Use case creation and management  
  - AI agent interaction endpoints
- **`dependencies.py`**: FastAPI dependencies for authentication, database connections

#### **`app/core/`**
- **`config.py`**: Configuration loading from YAML and environment variables
- **`security.py`**: Authentication and authorization logic
- **`exceptions.py`**: Custom application exceptions

#### **`app/models/`**
- **`database.py`**: SQLAlchemy models for metadata storage
- **`schemas.py`**: Pydantic models for request/response validation
- **`entities.py`**: Domain entities and business objects

#### **`app/services/`**
Business logic layer with dependency injection coordination:
- **`repository_service.py`**: Repository management operations
- **`use_case_service.py`**: Use case processing workflow
- **`document_processor.py`**: Document embedding and vector storage
- **`ai_agent_service.py`**: Agent orchestration and dependency injection management

**AI Agent Service Architecture:**
The `ai_agent_service.py` acts as the orchestration layer that:
1. **Assembles Dependencies**: Creates and configures agent dependencies from various storage connections
2. **Manages Agent Lifecycle**: Initializes agents with proper dependency injection
3. **Coordinates Multi-Agent Workflows**: Handles agent delegation with shared dependencies

**Example Service Implementation:**
```python
from dataclasses import dataclass
from typing import Protocol

class AIAgentService:
    def __init__(
        self,
        graphdb_client: GraphDBClient,
        vector_store: VectorStore,
        minio_client: MinIOClient,
        config: AppConfig
    ):
        self.graphdb_client = graphdb_client
        self.vector_store = vector_store
        self.minio_client = minio_client
        self.config = config

    async def create_chat_dependencies(self) -> ChatAgentDeps:
        """Factory method to create agent dependencies"""
        return ChatAgentDeps(
            graphdb_conn=await self.graphdb_client.get_connection(),
            vector_store=self.vector_store,
            minio_client=self.minio_client,
            config=self.config
        )

    async def process_user_query(self, query: str, repository_id: str) -> str:
        """Main entry point for processing user queries"""
        # Create dependencies specific to this repository
        deps = await self.create_chat_dependencies()
        deps.repository_id = repository_id

        # Run the chat agent with injected dependencies
        result = await chat_agent.run(query, deps=deps)
        return result.output
```

**Dependency Sharing Across Agents:**
When multiple agents need to collaborate, dependencies can be shared efficiently:
```python
@dataclass
class SharedDeps:
    http_client: httpx.AsyncClient
    graphdb_conn: GraphDBConnection
    vector_store: VectorStore

# Both agents use the same dependency type
analysis_agent = Agent('openai:gpt-4o', deps_type=SharedDeps)
summary_agent = Agent('anthropic:claude-3-sonnet', deps_type=SharedDeps)

@analysis_agent.tool
async def delegate_to_summary(ctx: RunContext[SharedDeps], data: str) -> str:
    # Pass shared dependencies to delegated agent
    result = await summary_agent.run(
        f"Summarize this: {data}",
        deps=ctx.deps,  # Share the same dependencies
        usage=ctx.usage  # Track usage across agents
    )
    return result.output
```

#### **`app/agents/`**
AI agent implementation using Pydantic AI with dependency injection:
- **`models.py`**: Pydantic BaseModel classes for agent data structures and dependencies
- **`model_factory.py`**: Simple model creation and provider switching logic
- **`chat_agent.py`**: Main agent implementation (uses library's Agent class)
- **`tools/`**: Individual tools (GraphDB queries, RAG, MinIO access)

**Dependency Injection Architecture:**
Agents in Pydantic AI implement a clean dependency injection pattern where external resources (database connections, API clients, configuration) are injected into agents rather than being hardcoded. This promotes modularity, testability, and separation of concerns.

**Key Concepts:**
1. **Dependencies Definition**: Dependencies are defined as dataclasses or Pydantic models that contain all external resources an agent needs
2. **Agent Configuration**: Agents are configured with `deps_type` parameter to specify the dependency type
3. **Runtime Injection**: Dependencies are passed at runtime via the `deps` parameter when running the agent
4. **Tool Access**: Tools access dependencies through `RunContext[DepsType]` parameter

**Example Implementation:**
```python
from dataclasses import dataclass
from pydantic_ai import Agent, RunContext

@dataclass
class ChatAgentDeps:
    """Dependencies for the chat agent"""
    graphdb_conn: GraphDBConnection
    vector_store: VectorStore
    minio_client: MinIOClient
    config: AppConfig

# Agent definition with dependency type
chat_agent = Agent(
    'openai:gpt-4o',
    deps_type=ChatAgentDeps,
    system_prompt="You are an AI assistant for BIM data analysis..."
)

@chat_agent.tool
async def query_graphdb(ctx: RunContext[ChatAgentDeps], sparql_query: str) -> str:
    """Query the GraphDB triplestore"""
    return await ctx.deps.graphdb_conn.execute_query(sparql_query)

@chat_agent.tool
async def search_documents(ctx: RunContext[ChatAgentDeps], query: str) -> str:
    """Search documents using RAG"""
    embeddings = await ctx.deps.vector_store.similarity_search(query)
    return await ctx.deps.minio_client.get_document_content(embeddings[0].document_path)

# Usage in service layer
async def run_chat_agent(user_query: str, deps: ChatAgentDeps) -> str:
    result = await chat_agent.run(user_query, deps=deps)
    return result.output
```

**Benefits:**
- **Testability**: Easy to mock dependencies for unit testing
- **Modularity**: Clear separation between agent logic and external resources
- **Flexibility**: Different dependency configurations for different environments
- **Type Safety**: Full type checking for dependency access

### **Simple Model Provider Switching**

**Model Factory (`app/agents/model_factory.py`):**
A simple, clean approach to switch between LLM providers without complexity:

```python
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.models.openrouter import OpenRouterProvider

# CHANGE THIS LINE TO SWITCH PROVIDERS
# Current: OpenRouter (supports many models)
CURRENT_PROVIDER = "openrouter"

# Provider configurations
PROVIDERS = {
    "openrouter": lambda model_name: OpenAIChatModel(
        model_name,
        provider=OpenRouterProvider(api_key=os.getenv("OPENROUTER_API_KEY"))
    ),
    "openai": lambda model_name: OpenAIChatModel(
        model_name,
        api_key=os.getenv("OPENAI_API_KEY")
    ),
    "anthropic": lambda model_name: AnthropicModel(
        model_name,
        api_key=os.getenv("ANTHROPIC_API_KEY")
    )
}

def create_model(model_name: str):
    """Create a model instance using the current provider"""
    provider_factory = PROVIDERS[CURRENT_PROVIDER]
    return provider_factory(model_name)

def create_agent_model(agent_name: str, config: dict):
    """Create model for a specific agent from config"""
    model_name = config["agents"][agent_name]["model"]
    return create_model(model_name)
```

**Agent Creation (`app/agents/chat_agent.py`):**
```python
from pydantic_ai import Agent
from .model_factory import create_agent_model
from ..core.config import get_config

# Load config once
config = get_config()

# Create model using factory
chat_model = create_agent_model("chat_agent", config)

# Create agent with the model
chat_agent = Agent(
    chat_model,
    deps_type=ChatAgentDeps,
    system_prompt="You are an AI assistant for BIM data analysis..."
)
```

**To Switch Providers:**
1. Change `CURRENT_PROVIDER = "openai"` in `model_factory.py`
2. Restart the application
3. All agents automatically use the new provider

**Model Names in Config:**
- **OpenRouter**: `"openai/gpt-4o-mini"`, `"anthropic/claude-3.5-sonnet"`
- **OpenAI Direct**: `"gpt-4o-mini"`, `"gpt-4o"`
- **Anthropic Direct**: `"claude-3-5-sonnet-20241022"`

This approach is:
- **Simple**: One line change to switch providers
- **Clean**: No complex abstractions or dynamic loading
- **Configurable**: Model names come from YAML config
- **Maintainable**: Easy to add new providers

#### **`app/storage/`**
Data access layer:
- **`graphdb.py`**: GraphDB (triplestore) operations
- **`vector_store.py`**: Vector database operations (Chroma, Pinecone, etc.)
- **`minio_client.py`**: MinIO file storage operations
- **`database.py`**: SQL database operations for metadata

### **Configuration Structure**

#### **`config/config.yaml`**
```yaml
app:
  name: "RDF LLM Service"
  version: "1.0.0"
  debug: false

databases:
  graphdb:
    host: "localhost"
    port: 7200
  vector_store:
    provider: "chroma"
    host: "localhost"
    port: 8000
  minio:
    endpoint: "localhost:9000"
    secure: false

llm:
  default_model: "gpt-4.1-mini"
  temperature: 0.1
  max_tokens: 2000
```

#### **`config/prompts.yaml`**
```yaml
agent:
  system_prompt: |
    You are an AI assistant that helps users query knowledge graphs and documents.
    You have access to the following tools:
    - GraphDB queries for structured data
    - RAG search for document content
    - MinIO for document retrieval
  
  tool_selection_prompt: |
    Given the user question: "{question}"
    Which tool would be most appropriate?

rag:
  query_prompt: |
    Context: {context}
    Question: {question}
    Answer:
```

#### **`config/models.yaml`**
```yaml
# Simple model configuration - just specify the model name per agent
agents:
  chat_agent:
    model: "openai/gpt-4o-mini"  # Model name for OpenRouter
    temperature: 0.1
    max_tokens: 2000

  analysis_agent:
    model: "anthropic/claude-3.5-sonnet"  # Model name for OpenRouter
    temperature: 0.0
    max_tokens: 4000

  summary_agent:
    model: "meta-llama/llama-3.1-8b-instruct"  # Model name for OpenRouter
    temperature: 0.2
    max_tokens: 1000

# Embedding model (separate from LLM models)
embeddings:
  model: "all-MiniLM-L6-v2"

dependencies:
  # Connection pool settings for dependency injection
  graphdb:
    pool_size: 10
    timeout: 30
  vector_store:
    batch_size: 100
    cache_size: 1000
  minio:
    max_connections: 20
```

### **Key Design Principles**

1. **Separation of Concerns**: Clear boundaries between API, business logic, and data access
2. **Dependency Injection**: Services and agent dependencies are injected, promoting modularity and testability
3. **Configuration-driven**: Prompts and models configurable via YAML
4. **Testable**: Clear separation allows for easy unit and integration testing with mock dependencies
5. **Scalable**: Modular design supports adding new tools and agents
6. **Professional**: Follows FastAPI and Python best practices

### **Testing Strategy with Dependency Injection**

The dependency injection pattern makes testing straightforward by allowing easy mocking of external dependencies:

**Unit Testing Agents:**
```python
# test_chat_agent.py
import pytest
from unittest.mock import AsyncMock
from pydantic_ai.models.test import TestModel

from app.agents.chat_agent import chat_agent, ChatAgentDeps

@pytest.fixture
def mock_deps():
    """Create mock dependencies for testing"""
    return ChatAgentDeps(
        graphdb_conn=AsyncMock(),
        vector_store=AsyncMock(),
        minio_client=AsyncMock(),
        config=AsyncMock()
    )

async def test_chat_agent_graphdb_query(mock_deps):
    """Test agent's GraphDB querying capability"""
    # Setup mock responses
    mock_deps.graphdb_conn.execute_query.return_value = "Mock GraphDB result"

    # Use TestModel to control agent responses
    test_model = TestModel()

    with chat_agent.override(model=test_model):
        result = await chat_agent.run(
            "Query the building data",
            deps=mock_deps
        )

    # Verify tool was called with correct parameters
    mock_deps.graphdb_conn.execute_query.assert_called_once()
    assert "Mock GraphDB result" in str(result.output)

async def test_agent_dependency_override():
    """Test overriding dependencies for different test scenarios"""
    # Create test-specific dependencies
    test_deps = ChatAgentDeps(
        graphdb_conn=AsyncMock(return_value="test data"),
        vector_store=AsyncMock(),
        minio_client=AsyncMock(),
        config=AsyncMock()
    )

    with chat_agent.override(deps=test_deps):
        result = await chat_agent.run("Test query")
        # Agent will use test_deps instead of runtime deps
```

**Integration Testing:**
```python
# test_ai_agent_service.py
async def test_ai_agent_service_integration(test_db, test_vector_store):
    """Integration test with real database connections"""
    service = AIAgentService(
        graphdb_client=test_db,
        vector_store=test_vector_store,
        minio_client=MockMinIOClient(),
        config=test_config
    )

    result = await service.process_user_query(
        "What buildings are in this project?",
        repository_id="test-repo"
    )

    assert result is not None
    assert "building" in result.lower()
```

### **Environment Variables (.env)**
```env
# Database
DATABASE_URL=postgresql://user:pass@localhost/dbname

# GraphDB
GRAPHDB_USERNAME=admin
GRAPHDB_PASSWORD=secret

# Vector Store
VECTOR_STORE_API_KEY=your-key

# MinIO
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# LLM Provider APIs (only need the one you're using)
OPENROUTER_API_KEY=your-openrouter-key
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key

# Agent Configuration
AGENT_DEFAULT_MODEL=openai:gpt-4o
AGENT_MAX_RETRIES=3
AGENT_TIMEOUT=60

# Dependency Injection Settings
DI_GRAPHDB_POOL_SIZE=10
DI_VECTOR_STORE_CACHE_SIZE=1000
DI_MINIO_MAX_CONNECTIONS=20

# Security
SECRET_KEY=your-secret-key
```

This architecture provides a solid foundation that's both simple to understand and professional enough for production use.