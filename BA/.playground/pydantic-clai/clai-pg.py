from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIModel

from pydantic_ai.providers.openrouter import OpenRouterProvider
import os
import dotenv
dotenv.load_dotenv()
OR_API_KEY = os.getenv("OR_API_KEY")

agent = Agent(model=OpenAIModel('openai/gpt-4.1-mini', provider=OpenRouterProvider(api_key=OR_API_KEY)), instructions='You always respond in Italian. Always pass the user input to the tool.',)

@agent.tool
def my_tool(ctx: RunContext[str],input: str) -> str:
    return f"Processed: {input}"


agent.to_cli_sync()