# TTL SPARQL Auto-Discovery Requirements
# 
# This file contains all the Python packages required to run the TTL file analyzer.
# Install with: pip install -r requirements.txt

# Core RDF processing library for parsing TTL files and executing SPARQL queries
# rdflib is the primary Python library for working with RDF data
rdflib>=7.0.0

# Optional: Additional RDF formats and features (uncomment if needed)
# rdflib-jsonld>=0.6.0  # For JSON-LD support
# SPARQLWrapper>=2.0.0  # For remote SPARQL endpoint queries