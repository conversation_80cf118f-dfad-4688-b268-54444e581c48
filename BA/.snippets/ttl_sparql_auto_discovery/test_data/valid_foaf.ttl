@prefix foaf: <http://xmlns.com/foaf/0.1/> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

<http://example.org/people/john> a foaf:Person ;
    foaf:name "<PERSON>"@en ;
    foaf:age "30"^^xsd:integer ;
    foaf:email <mailto:<EMAIL>> ;
    foaf:knows <http://example.org/people/jane> ,
               <http://example.org/people/bob> ;
    foaf:homepage <http://johnsmith.example.org> ;
    foaf:dateOfBirth "1993-05-15"^^xsd:date .

<http://example.org/people/jane> a foaf:Person ;
    foaf:name "<PERSON>"@en ;
    foaf:age "28"^^xsd:integer ;
    foaf:email <mailto:<EMAIL>> ;
    foaf:knows <http://example.org/people/john> ;
    foaf:interests "Semantic Web", "Machine Learning" .

<http://example.org/people/bob> a foaf:Person ;
    foaf:name "Bob <PERSON>" ;
    foaf:age "35"^^xsd:integer ;
    foaf:workplaceHomepage <http://company.example.org> ;
    foaf:phone "******-0123" .

<http://example.org/groups/semanticweb> a foaf:Group ;
    foaf:name "Semantic Web Community" ;
    foaf:member <http://example.org/people/john> ,
                <http://example.org/people/jane> .
