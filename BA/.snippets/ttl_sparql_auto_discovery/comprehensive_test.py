#!/usr/bin/env python3
"""
Comprehensive Test Suite for TTL Auto-Discovery Tool

This script thoroughly tes            # Validate essential content in result
            required_content = [
                "http://xmlns.com/foaf/0.1/Person",  # Should find FOAF Person class
                "http://xmlns.com/foaf/0.1/name",    # Should find name property
                "RDF Classes",   # Should have structural sections
                "SPARQL",       # Should include query templates
                "Declared Namespace Prefixes"  # Should have namespace section
            ]TL analyzer with both valid and invalid data,
covering all error conditions, edge cases, and functionality aspects.

Test Categories:
1. Valid TTL Files (Different Vocabularies)
2. Invalid TTL Files (Syntax Errors)
3. Edge Cases (Empty files, minimal content)
4. File System Errors (Missing files, permissions)
5. CLI Interface Testing
6. Error Handling Validation

Author: Test Suite for TTL Analyzer
Date: August 2025
"""

import sys
import os
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Tuple
import subprocess
import time

# Add the main module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our analyzer
try:
    from ttl_sparql_auto_discovery import (
        analyze_ttl_file,
        TTLFileNotFoundError,
        TTLParsingError,
        TTLAnalysisError,
        FileSystemError
    )
    print("✅ Successfully imported TTL analyzer module")
except ImportError as e:
    print(f"❌ Failed to import TTL analyzer module: {e}")
    sys.exit(1)


class TestResult:
    """Container for test results"""
    def __init__(self, test_name: str, passed: bool, message: str = "", execution_time: float = 0.0):
        self.test_name = test_name
        self.passed = passed
        self.message = message
        self.execution_time = execution_time
    
    def __str__(self):
        status = "✅ PASS" if self.passed else "❌ FAIL"
        time_str = f" ({self.execution_time:.3f}s)" if self.execution_time > 0 else ""
        return f"{status} {self.test_name}{time_str}\n    {self.message}"


class TTLAnalyzerTester:
    """Comprehensive test suite for TTL analyzer"""
    
    def __init__(self, test_data_dir: str):
        self.test_data_dir = Path(test_data_dir)
        self.results: List[TestResult] = []
        self.temp_files: List[Path] = []
    
    def cleanup(self):
        """Clean up temporary files"""
        for temp_file in self.temp_files:
            try:
                if temp_file.exists():
                    temp_file.unlink()
            except Exception:
                pass  # Best effort cleanup
    
    def run_test(self, test_name: str, test_func) -> TestResult:
        """Run a single test and capture results"""
        print(f"🔄 Running: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func()
            execution_time = time.time() - start_time
            
            if isinstance(result, bool):
                test_result = TestResult(test_name, result, "", execution_time)
            elif isinstance(result, tuple) and len(result) == 2:
                passed, message = result
                test_result = TestResult(test_name, passed, message, execution_time)
            else:
                test_result = TestResult(test_name, True, str(result), execution_time)
                
        except Exception as e:
            execution_time = time.time() - start_time
            test_result = TestResult(test_name, False, f"Exception: {str(e)}", execution_time)
        
        self.results.append(test_result)
        print(f"    {test_result}")
        return test_result
    
    def test_valid_foaf_file(self) -> Tuple[bool, str]:
        """Test analysis of valid FOAF TTL file"""
        foaf_file = self.test_data_dir / "valid_foaf.ttl"
        
        if not foaf_file.exists():
            return False, f"Test file not found: {foaf_file}"
        
        try:
            result = analyze_ttl_file(str(foaf_file))
            
            # Validate essential content in result
            required_content = [
                "http://xmlns.com/foaf/0.1/Person",  # Should find FOAF Person class
                "http://xmlns.com/foaf/0.1/name",    # Should find name property
                "RDF Classes",   # Should have structural sections
                "SPARQL",       # Should include query templates
                "Namespace Prefixes"  # Should have namespace section
            ]
            
            for content in required_content:
                if content not in result:
                    return False, f"Missing expected content: '{content}'"
            
            # Check for proper markdown structure
            if not result.startswith("# TTL File Analysis"):
                return False, "Result should start with proper markdown header"
            
            # Check for presence of major sections
            sections = [
                "## File Statistics",
                "## Declared Namespace Prefixes",  # Should have namespace section 
                "## RDF Classes",
                "## RDF Properties",
                "## Data Value Types",
                "## Query Possibilities"
            ]
            
            for section in sections:
                if section not in result:
                    return False, f"Missing section: {section}"
            
            return True, f"Successfully analyzed FOAF file ({len(result)} chars)"
            
        except Exception as e:
            return False, f"Failed to analyze valid FOAF file: {str(e)}"
    
    def test_valid_dublin_core_file(self) -> Tuple[bool, str]:
        """Test analysis of valid Dublin Core TTL file"""
        dc_file = self.test_data_dir / "valid_dublin_core.ttl"
        
        if not dc_file.exists():
            return False, f"Test file not found: {dc_file}"
        
        try:
            result = analyze_ttl_file(str(dc_file))
            
            # Check for Dublin Core specific content
            dc_content = [
                "http://purl.org/dc/terms/BibliographicResource",
                "http://purl.org/dc/elements/1.1/title",
                "http://purl.org/dc/elements/1.1/creator", 
                "RDF Classes"
            ]
            
            for content in dc_content:
                if content not in result:
                    return False, f"Missing Dublin Core content: '{content}'"
            
            return True, f"Successfully analyzed Dublin Core file ({len(result)} chars)"
            
        except Exception as e:
            return False, f"Failed to analyze Dublin Core file: {str(e)}"
    
    def test_valid_schema_org_file(self) -> Tuple[bool, str]:
        """Test analysis of valid Schema.org TTL file"""
        schema_file = self.test_data_dir / "valid_schema_org.ttl"
        
        if not schema_file.exists():
            return False, f"Test file not found: {schema_file}"
        
        try:
            result = analyze_ttl_file(str(schema_file))
            
            # Check for Schema.org specific content
            schema_content = [
                "https://schema.org/Restaurant",
                "https://schema.org/MusicEvent", 
                "https://schema.org/Product",
                "RDF Classes"
            ]
            
            for content in schema_content:
                if content not in result:
                    return False, f"Missing Schema.org content: '{content}'"
            
            return True, f"Successfully analyzed Schema.org file ({len(result)} chars)"
            
        except Exception as e:
            return False, f"Failed to analyze Schema.org file: {str(e)}"
    
    def test_valid_custom_domain_file(self) -> Tuple[bool, str]:
        """Test analysis of custom domain vocabulary TTL file"""
        custom_file = self.test_data_dir / "valid_custom_domain.ttl"
        
        if not custom_file.exists():
            return False, f"Test file not found: {custom_file}"
        
        try:
            result = analyze_ttl_file(str(custom_file))
            
            # Check for custom domain content
            custom_content = [
                "http://example.org/building/ontology#Building",
                "http://example.org/building/ontology#Floor",
                "http://example.org/properties#LeaseContract",
                "RDF Classes"
            ]
            
            for content in custom_content:
                if content not in result:
                    return False, f"Missing custom domain content: '{content}'"
            
            return True, f"Successfully analyzed custom domain file ({len(result)} chars)"
            
        except Exception as e:
            return False, f"Failed to analyze custom domain file: {str(e)}"
    
    def test_empty_file(self) -> Tuple[bool, str]:
        """Test analysis of empty TTL file"""
        empty_file = self.test_data_dir / "empty.ttl"
        
        if not empty_file.exists():
            return False, f"Test file not found: {empty_file}"
        
        try:
            result = analyze_ttl_file(str(empty_file))
            
            # Empty file should still produce a valid analysis
            if "File Statistics" not in result:
                return False, "Empty file should still produce analysis structure"
            
            if "0 triples" not in result:
                return False, "Empty file should report 0 triples"
            
            return True, "Successfully handled empty file"
            
        except Exception as e:
            return False, f"Failed to handle empty file: {str(e)}"
    
    def test_minimal_file(self) -> Tuple[bool, str]:
        """Test analysis of minimal TTL file (only prefixes)"""
        minimal_file = self.test_data_dir / "minimal.ttl"
        
        if not minimal_file.exists():
            return False, f"Test file not found: {minimal_file}"
        
        try:
            result = analyze_ttl_file(str(minimal_file))
            
            # Should handle file with only prefixes gracefully
            if "Namespace Prefixes" not in result:
                return False, "Minimal file should still show namespace prefixes"
            
            return True, "Successfully handled minimal file"
            
        except Exception as e:
            return False, f"Failed to handle minimal file: {str(e)}"
    
    def test_file_not_found_error(self) -> Tuple[bool, str]:
        """Test TTLFileNotFoundError handling"""
        non_existent_file = self.test_data_dir / "does_not_exist.ttl"
        
        try:
            analyze_ttl_file(str(non_existent_file))
            return False, "Should have raised TTLFileNotFoundError"
            
        except TTLFileNotFoundError as e:
            if "does_not_exist.ttl" in str(e):
                return True, f"Correctly raised TTLFileNotFoundError: {str(e)[:100]}..."
            else:
                return False, f"TTLFileNotFoundError message incorrect: {str(e)}"
            
        except Exception as e:
            return False, f"Raised wrong exception type: {type(e).__name__}: {str(e)}"
    
    def test_parsing_error_invalid_syntax(self) -> Tuple[bool, str]:
        """Test TTLParsingError handling with invalid syntax"""
        invalid_file = self.test_data_dir / "invalid_syntax.ttl"
        
        if not invalid_file.exists():
            return False, f"Test file not found: {invalid_file}"
        
        try:
            analyze_ttl_file(str(invalid_file))
            return False, "Should have raised TTLParsingError"
            
        except TTLParsingError as e:
            if "syntax" in str(e).lower() or "parse" in str(e).lower():
                return True, f"Correctly raised TTLParsingError: {str(e)[:100]}..."
            else:
                return False, f"TTLParsingError message unclear: {str(e)}"
            
        except Exception as e:
            return False, f"Raised wrong exception type: {type(e).__name__}: {str(e)}"
    
    def test_parsing_error_malformed_uris(self) -> Tuple[bool, str]:
        """Test TTLParsingError handling with malformed URIs"""
        malformed_file = self.test_data_dir / "malformed_uris.ttl"
        
        if not malformed_file.exists():
            return False, f"Test file not found: {malformed_file}"
        
        try:
            analyze_ttl_file(str(malformed_file))
            return False, "Should have raised TTLParsingError"
            
        except TTLParsingError as e:
            return True, f"Correctly raised TTLParsingError: {str(e)[:100]}..."
            
        except Exception as e:
            return False, f"Raised wrong exception type: {type(e).__name__}: {str(e)}"
    
    def test_parsing_error_not_ttl(self) -> Tuple[bool, str]:
        """Test TTLParsingError handling with non-TTL content"""
        not_ttl_file = self.test_data_dir / "not_ttl.ttl"
        
        if not not_ttl_file.exists():
            return False, f"Test file not found: {not_ttl_file}"
        
        try:
            analyze_ttl_file(str(not_ttl_file))
            return False, "Should have raised TTLParsingError"
            
        except TTLParsingError as e:
            return True, f"Correctly raised TTLParsingError: {str(e)[:100]}..."
            
        except Exception as e:
            return False, f"Raised wrong exception type: {type(e).__name__}: {str(e)}"
    
    def test_cli_interface_help(self) -> Tuple[bool, str]:
        """Test CLI interface help functionality"""
        try:
            # Test help command
            result = subprocess.run([
                sys.executable, "ttl_sparql_auto_discovery.py", "--help"
            ], capture_output=True, text=True, cwd=str(self.test_data_dir.parent))
            
            if result.returncode != 0:
                return False, f"Help command failed with exit code {result.returncode}"
            
            help_content = result.stdout
            required_help_content = ["usage:", "TTL", "analyze", "ttl_file", "output"]
            
            for content in required_help_content:
                if content.lower() not in help_content.lower():
                    return False, f"Missing help content: '{content}'"
            
            return True, "CLI help interface working correctly"
            
        except Exception as e:
            return False, f"CLI help test failed: {str(e)}"
    
    def test_cli_interface_valid_file(self) -> Tuple[bool, str]:
        """Test CLI interface with valid TTL file"""
        foaf_file = self.test_data_dir / "valid_foaf.ttl"
        
        if not foaf_file.exists():
            return False, f"Test file not found: {foaf_file}"
        
        try:
            # Test basic CLI execution
            result = subprocess.run([
                sys.executable, "ttl_sparql_auto_discovery.py", str(foaf_file)
            ], capture_output=True, text=True, cwd=str(self.test_data_dir.parent), timeout=30)
            
            if result.returncode != 0:
                return False, f"CLI failed with exit code {result.returncode}: {result.stderr}"
            
            # Check output content
            output = result.stdout
            if "TTL File Analysis" not in output:
                return False, "CLI output missing expected analysis header"
            
            if "Person" not in output:
                return False, "CLI output missing expected FOAF content"
            
            return True, f"CLI interface working correctly ({len(output)} chars output)"
            
        except subprocess.TimeoutExpired:
            return False, "CLI execution timed out"
        except Exception as e:
            return False, f"CLI test failed: {str(e)}"
    
    def test_cli_interface_output_file(self) -> Tuple[bool, str]:
        """Test CLI interface with output file option"""
        foaf_file = self.test_data_dir / "valid_foaf.ttl"
        output_file = self.test_data_dir / "test_output.md"
        
        if not foaf_file.exists():
            return False, f"Test file not found: {foaf_file}"
        
        try:
            # Clean up any existing output file
            if output_file.exists():
                output_file.unlink()
            
            self.temp_files.append(output_file)  # For cleanup
            
            # Test CLI with output file
            result = subprocess.run([
                sys.executable, "ttl_sparql_auto_discovery.py", 
                str(foaf_file), "--output", str(output_file)
            ], capture_output=True, text=True, cwd=str(self.test_data_dir.parent), timeout=30)
            
            if result.returncode != 0:
                return False, f"CLI with output failed with exit code {result.returncode}: {result.stderr}"
            
            # Check output file was created
            if not output_file.exists():
                return False, "Output file was not created"
            
            # Check output file content
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "TTL File Analysis" not in content:
                return False, "Output file missing expected analysis header"
            
            if len(content) < 100:
                return False, f"Output file too short ({len(content)} chars)"
            
            return True, f"CLI output file functionality working ({len(content)} chars written)"
            
        except subprocess.TimeoutExpired:
            return False, "CLI execution with output file timed out"
        except Exception as e:
            return False, f"CLI output file test failed: {str(e)}"
    
    def test_cli_interface_invalid_file(self) -> Tuple[bool, str]:
        """Test CLI interface error handling with invalid file"""
        invalid_file = self.test_data_dir / "invalid_syntax.ttl"
        
        if not invalid_file.exists():
            return False, f"Test file not found: {invalid_file}"
        
        try:
            # Test CLI with invalid file
            result = subprocess.run([
                sys.executable, "ttl_sparql_auto_discovery.py", str(invalid_file)
            ], capture_output=True, text=True, cwd=str(self.test_data_dir.parent), timeout=30)
            
            # Should fail with non-zero exit code
            if result.returncode == 0:
                return False, "CLI should have failed with invalid TTL file"
            
            # Check error message
            error_output = result.stderr
            if "error" not in error_output.lower() and "failed" not in error_output.lower():
                return False, f"Expected error message, got: {error_output}"
            
            return True, f"CLI correctly handled invalid file (exit code: {result.returncode})"
            
        except subprocess.TimeoutExpired:
            return False, "CLI execution with invalid file timed out"
        except Exception as e:
            return False, f"CLI invalid file test failed: {str(e)}"
    
    def test_performance_large_content(self) -> Tuple[bool, str]:
        """Test performance with larger TTL content"""
        # Create a larger TTL file programmatically
        large_file = self.test_data_dir / "large_test.ttl"
        self.temp_files.append(large_file)
        
        try:
            # Generate content with multiple entities
            content_lines = [
                "@prefix foaf: <http://xmlns.com/foaf/0.1/> .",
                "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .",
                "@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .",
                ""
            ]
            
            # Add 100 person entities
            for i in range(100):
                content_lines.extend([
                    f"<http://example.org/people/person{i}> a foaf:Person ;",
                    f'    foaf:name "Person {i}" ;',
                    f'    foaf:age "{20 + (i % 60)}"^^xsd:integer .',
                    ""
                ])
            
            with open(large_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content_lines))
            
            # Time the analysis
            start_time = time.time()
            result = analyze_ttl_file(str(large_file))
            execution_time = time.time() - start_time
            
            # Check results
            if "http://xmlns.com/foaf/0.1/Person" not in result:
                return False, "Large file analysis missing expected content"
            
            # Performance check (should complete in reasonable time)
            if execution_time > 10.0:  # 10 seconds is generous for 100 entities
                return False, f"Analysis took too long: {execution_time:.2f}s"
            
            return True, f"Performance test passed ({execution_time:.3f}s, {len(result)} chars)"
            
        except Exception as e:
            return False, f"Performance test failed: {str(e)}"
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results"""
        print("🚀 Starting Comprehensive TTL Analyzer Test Suite")
        print("=" * 60)
        
        # Valid file tests
        self.run_test("Valid FOAF File Analysis", self.test_valid_foaf_file)
        self.run_test("Valid Dublin Core File Analysis", self.test_valid_dublin_core_file)
        self.run_test("Valid Schema.org File Analysis", self.test_valid_schema_org_file)
        self.run_test("Valid Custom Domain File Analysis", self.test_valid_custom_domain_file)
        
        # Edge case tests
        self.run_test("Empty File Handling", self.test_empty_file)
        self.run_test("Minimal File Handling", self.test_minimal_file)
        
        # Error handling tests
        self.run_test("File Not Found Error", self.test_file_not_found_error)
        self.run_test("Invalid Syntax Parsing Error", self.test_parsing_error_invalid_syntax)
        self.run_test("Malformed URIs Parsing Error", self.test_parsing_error_malformed_uris)
        self.run_test("Non-TTL Content Parsing Error", self.test_parsing_error_not_ttl)
        
        # CLI interface tests
        self.run_test("CLI Help Interface", self.test_cli_interface_help)
        self.run_test("CLI Valid File Processing", self.test_cli_interface_valid_file)
        self.run_test("CLI Output File Option", self.test_cli_interface_output_file)
        self.run_test("CLI Invalid File Error Handling", self.test_cli_interface_invalid_file)
        
        # Performance tests
        self.run_test("Performance with Large Content", self.test_performance_large_content)
        
        # Cleanup temporary files
        self.cleanup()
        
        # Calculate summary statistics
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        failed_tests = total_tests - passed_tests
        total_time = sum(r.execution_time for r in self.results)
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        for result in self.results:
            print(result)
        
        print("\n" + "=" * 60)
        print(f"📈 OVERALL RESULTS:")
        print(f"   Total Tests:  {total_tests}")
        print(f"   ✅ Passed:    {passed_tests}")
        print(f"   ❌ Failed:    {failed_tests}")
        print(f"   ⏱️ Total Time: {total_time:.3f}s")
        print(f"   📊 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests == 0:
            print("\n🎉 ALL TESTS PASSED! TTL Analyzer is working correctly.")
        else:
            print(f"\n⚠️ {failed_tests} TEST(S) FAILED. Please review the failures above.")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': passed_tests/total_tests*100,
            'total_time': total_time,
            'results': self.results
        }


def main():
    """Main test execution function"""
    # Determine test data directory
    script_dir = Path(__file__).parent
    test_data_dir = script_dir / "test_data"
    
    if not test_data_dir.exists():
        print(f"❌ Test data directory not found: {test_data_dir}")
        print("Please ensure test data files are created first.")
        return 1
    
    # Run comprehensive tests
    tester = TTLAnalyzerTester(str(test_data_dir))
    results = tester.run_all_tests()
    
    # Return appropriate exit code
    return 0 if results['failed_tests'] == 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
