# TTL File Auto-Discovery and Analysis Tool

A completely generic TTL (Turtle) RDF file analyzer that works with **ANY** RDF vocabulary or ontology without making assumptions about specific data structures, prefixes, or schemas.

## 🎯 Why Use This Tool?

### The Problem

Most RDF analysis tools are designed for specific vocabularies (FOAF, Dublin Core, etc.) or make assumptions about data structure. When you encounter a new TTL file from an unknown source, you need to understand:

- What classes (entity types) exist?
- What properties (relationships) are available?
- How entities relate to each other?
- What can be queried from this dataset?

### The Solution

This tool provides **vocabulary-agnostic analysis** that works equally well with:

- 🏢 **Domain-specific ontologies** (building management, healthcare, finance)
- 👤 **FOAF personal profiles** and social networks
- 📚 **Dublin Core metadata** for digital libraries
- 🛒 **Schema.org structured data** for web content
- 🔬 **Scientific vocabularies** (biology, chemistry, physics)
- 🏛️ **Government open data** and civic information
- 🎨 **Custom vocabularies** for specific applications

## ✨ Key Features

### 🔍 **Truly Generic**

- **No hardcoded prefixes**: Dynamically discovers all namespaces
- **No vocabulary assumptions**: Works with any RDF ontology
- **No structural expectations**: Adapts to any data model
- **Universal compatibility**: Handles any valid TTL content

### 📊 **Comprehensive Analysis**

- **File statistics**: Size, triple count, complexity assessment
- **Namespace inventory**: All declared prefixes and their URIs
- **Class discovery**: All entity types with instance counts
- **Property mapping**: All relationships with usage statistics
- **Data type analysis**: Literal types and value patterns
- **Relationship patterns**: How different classes connect

### 🎯 **AI-Agent Ready**

- **Query templates**: Generic SPARQL patterns with placeholders
- **Structural context**: Complete understanding of queryable content
- **No concrete queries**: Flexible patterns that adapt to user needs
- **Comprehensive documentation**: Everything needed for dynamic query generation

## 🚀 Installation and Setup

### 1. Create and Activate Virtual Environment

```bash
# Windows
python -m venv .venv
.venv\Scripts\activate

# Linux/macOS
python -m venv .venv
source .venv/bin/activate
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Verify Installation

```bash
python ttl_sparql_auto_discovery.py --help
```

## 📖 Usage

### Command Line Interface

```bash
# Basic analysis (output to console)
python ttl_sparql_auto_discovery.py assets/example.ttl

# Save analysis to file
python ttl_sparql_auto_discovery.py assets/example.ttl -o analysis.md

# With full path
python ttl_sparql_auto_discovery.py /path/to/your/data.ttl --output report.md
```

### Python API

```python
from ttl_sparql_auto_discovery import analyze_ttl_file

# Analyze any TTL file
analysis = analyze_ttl_file("path/to/file.ttl")
print(analysis)

# The function returns comprehensive markdown documentation
with open("analysis.md", "w") as f:
    f.write(analysis)
```

## 📋 Output Explanation

The tool generates structured markdown documentation with the following sections:

### 📊 **File Statistics**

- File size and triple count
- Complexity assessment (High/Medium/Low)
- Number of declared namespaces

### 🏷️ **Namespace Prefixes**

- All `@prefix` declarations from the TTL file
- Complete namespace-to-URI mappings
- Ready-to-use prefixes for SPARQL queries

### 🏗️ **RDF Classes (Entity Types)**

- All classes found via `rdf:type` statements
- Instance count for each class
- Namespace distribution analysis
- Percentage breakdown of entity types

### 🔗 **RDF Properties (Relationships)**

- All properties with usage statistics
- Subject and object counts per property
- Property namespace analysis
- Relationship frequency data

### 📊 **Data Value Types**

- Breakdown of URI vs Literal vs Blank Node values
- Literal datatype analysis (string, date, number, etc.)
- Data distribution percentages

### 🔄 **Relationship Patterns**

- Class-to-class relationships through properties
- Connection patterns between entity types
- Relationship frequency analysis

### 🎯 **Query Possibilities**

- **Generic SPARQL templates** with placeholders like:
  - `<REPLACE_WITH_CLASS_URI>`
  - `<REPLACE_WITH_PROPERTY_URI>`
  - `<REPLACE_WITH_INSTANCE_URI>`
- **Query strategy recommendations** based on data characteristics
- **Structural insights** for query construction

## 🎯 Why Generic Templates Instead of Concrete Queries?

### ❌ **Problem with Concrete Queries**

```sparql
# This only works with specific vocabularies
PREFIX foaf: <http://xmlns.com/foaf/0.1/>
SELECT ?person WHERE { ?person a foaf:Person . }
```

### ✅ **Solution: Generic Templates**

```sparql
# This works with ANY vocabulary
SELECT ?instance WHERE {
    ?instance a <REPLACE_WITH_CLASS_URI> .
}
```

**Benefits:**

- **Universal applicability**: Works with any TTL file
- **AI-friendly**: Easy for agents to customize based on user queries
- **Flexible**: Can be adapted for any specific use case
- **Educational**: Shows query structure without vocabulary assumptions

## 🔧 Technical Details

### Error Handling

The tool provides specific exceptions for different failure modes:

- **TTLFileNotFoundError**: File doesn't exist or path issues
- **TTLParsingError**: Invalid TTL syntax with helpful suggestions
- **TTLAnalysisError**: SPARQL query failures during analysis
- **FileSystemError**: Permission or I/O problems

### Performance

- Efficient SPARQL queries optimized for large datasets
- Memory-conscious processing for big TTL files
- Progress indication for long-running analyses
- Graceful handling of complex ontologies

### Dependencies

- **rdflib**: Core RDF processing (parsing, querying)
- **pathlib**: Cross-platform file handling
- **argparse**: Command-line interface
- **typing**: Type hints for code clarity

## 🤖 Integration with AI Agents

This tool is designed to provide perfect context for AI agents to generate accurate SPARQL queries:

### 1. **Context Understanding**

```python
analysis = analyze_ttl_file("user_data.ttl")
# AI agent now knows all available classes, properties, and patterns
```

### 2. **Dynamic Query Generation**

```python
# AI agent can replace placeholders based on user requests
user_query = "Find all buildings in Germany"
# Agent uses analysis to identify relevant class and property URIs
# Generates: SELECT ?building WHERE { ?building a <building_class_uri> ; <country_property> "Germany" . }
```

### 3. **Validation and Guidance**

```python
# AI agent knows what's possible to query
# Can suggest alternatives if user asks for non-existent properties
# Provides guidance on query complexity and optimization
```

## 🌟 Examples

### FOAF Profile Analysis

```bash
python ttl_sparql_auto_discovery.py foaf_profile.ttl
# Discovers: foaf:Person, foaf:knows, foaf:name, etc.
# Generates templates for social network queries
```

### Dublin Core Metadata

```bash
python ttl_sparql_auto_discovery.py dublin_core_records.ttl
# Discovers: dcterms:title, dcterms:creator, dcterms:subject, etc.
# Generates templates for metadata searches
```

### Custom Domain Vocabulary

```bash
python ttl_sparql_auto_discovery.py custom_domain.ttl
# Discovers: whatever classes and properties are actually present
# No assumptions, complete flexibility
```

## 📄 License

MIT License - Feel free to use, modify, and distribute.

## 🤝 Contributing

Contributions welcome! The tool is designed to be:

- Completely generic (no vocabulary-specific code)
- Well-documented (every function and line explained)
- Error-resilient (comprehensive exception handling)
- User-friendly (clear output and helpful messages)

---

**🎯 Perfect for understanding any TTL file structure and enabling AI agents to generate accurate, dynamic SPARQL queries!**
