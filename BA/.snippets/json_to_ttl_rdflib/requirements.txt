# RDFlib - Python library for working with RDF, semantic web, and linked data
# Provides Graph, Namespace, Literal, URIRef classes for RDF manipulation
# Supports multiple RDF serialization formats including Turtle, JSON-LD, RDF/XML
rdflib>=7.0.0

# typing_extensions - Provides backport of typing features for older Python versions
# Used for type hints like List, Dict, Optional, Any for better code documentation
typing_extensions>=4.0.0
