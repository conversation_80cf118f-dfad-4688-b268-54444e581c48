#!/usr/bin/env python3
"""
JSON to TTL (Turtle) Converter using RDFlib

This script provides a robust solution for converting JSON graph data to TTL (Turtle) format
using the industry-standard RDFlib library. It's specifically designed for building portfolio
data but can be adapted for any JSON structure containing RDF-like graph metadata.

Author: <PERSON><PERSON><PERSON> Labidi
Date: August 2025
License: MIT

Dependencies:
    - rdflib: Python library for working with RDF graphs
    - json: Built-in JSON parsing (part of Python standard library)
    - re: Regular expressions for string manipulation (part of Python standard library)
    - typing: Type hints for better code documentation (part of Python standard library)
    - sys: System-specific parameters and functions (part of Python standard library)
    - os: Operating system interface (part of Python standard library)

Usage:
    python json_to_ttl_rdflib.py <input_json_file> [output_ttl_file]
    
    If output_ttl_file is not specified, it defaults to the input filename with .ttl extension.

Example:
    python json_to_ttl_rdflib.py assets/example_building_portfolio.json output.ttl
"""

# Standard library imports - these are built into Python
import json        # For parsing JSON files and data structures
import re          # Regular expressions for string pattern matching and replacement
import sys         # System-specific parameters and functions for command line arguments
import os          # Operating system interface for file path operations
from typing import Dict, List, Any, Optional  # Type hints for better code documentation and IDE support

# Third-party imports - these need to be installed via pip (see requirements.txt)
from rdflib import Graph, Namespace, Literal, URIRef  # Core RDFlib classes for RDF graph manipulation
from rdflib.namespace import RDF, RDFS, XSD           # Predefined RDF namespaces (RDF, RDF Schema, XML Schema Datatypes)


def json_to_ttl_rdflib(json_data: List[Dict[str, Any]], output_file: Optional[str] = None) -> str:
    """
    Convert JSON graph data to TTL (Turtle) format using RDFlib.
    
    This function takes a list of JSON objects representing RDF graph data and converts them
    to TTL format using the industry-standard RDFlib library. The function handles:
    - Namespace management and binding
    - Type conversion and validation
    - Property name normalization (camelCase to kebab-case)
    - RDF literal typing (strings, integers, booleans, decimals)
    - Graph serialization to TTL format
    
    Args:
        json_data (List[Dict[str, Any]]): List of graph objects from JSON file.
                                         Each object should contain:
                                         - graphData: TTL string with existing triples (optional)
                                         - graphMetadata: List of entity metadata (optional)
                                         - accessRights: Access control information (optional)
                                         - useCase: Use case description (optional)
        
        output_file (Optional[str]): Optional file path to save the TTL content.
                                   If provided, the TTL content will be written to this file.
                                   If None, only the string is returned.
        
    Returns:
        str: TTL formatted string containing all RDF triples from the input data.
             The string includes proper namespace declarations and is valid Turtle syntax.
    
    Raises:
        Exception: If there are issues parsing the graph data or creating RDF triples.
                  Warnings are printed to stdout for individual parsing failures.
    
    Example:
        >>> json_data = [{"graphMetadata": [{"id": "123", "classType": "Building", "propertiesValues": {"name": "Office"}}]}]
        >>> ttl_output = json_to_ttl_rdflib(json_data)
        >>> print(ttl_output)
        @prefix inst: <https://example.com/> .
        @prefix prop: <https://ibpdi.datacat.org/property/> .
        inst:123 a <Building> ;
            prop:name "Office" .
    """
    
    # Create an RDF graph - this is the main container for all RDF triples
    # Graph() creates an empty RDF graph that can hold subject-predicate-object triples
    g = Graph()
    
    # Define custom namespaces used in the building portfolio data
    # These namespaces provide URI prefixes for our RDF entities and properties
    IBPDI = Namespace("https://ibpdi.datacat.org/class/")      # Namespace for building/address class types
    PROP = Namespace("https://ibpdi.datacat.org/property/")    # Namespace for property URIs (street-name, city, etc.)
    INST = Namespace("https://example.com/")                   # Namespace for instance URIs (individual buildings/addresses)
    
    # Bind namespaces to prefixes in the graph
    # This creates the @prefix declarations at the top of the TTL output
    # Making the output more readable and compact
    g.bind("ibpdi", IBPDI)    # @prefix ibpdi: <https://ibpdi.datacat.org/class/> .
    g.bind("prop", PROP)      # @prefix prop: <https://ibpdi.datacat.org/property/> .
    g.bind("inst", INST)      # @prefix inst: <https://example.com/> .
    g.bind("rdf", RDF)        # @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
    g.bind("rdfs", RDFS)      # @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
    g.bind("xsd", XSD)        # @prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
    
    def clean_property_name(prop_name: str) -> str:
        """
        Convert camelCase property names to kebab-case for RDF URIs.
        
        This function normalizes property names from JSON (typically camelCase) 
        to RDF URI conventions (kebab-case with lowercase). This ensures consistency
        and follows RDF best practices for property naming.
        
        Args:
            prop_name (str): Property name in camelCase (e.g., "streetName", "houseNumber")
        
        Returns:
            str: Property name in kebab-case (e.g., "street-name", "house-number")
        
        Examples:
            >>> clean_property_name("streetName")
            'street-name'
            >>> clean_property_name("buildingCode")
            'building-code'
            >>> clean_property_name("city")  # Already lowercase, no change needed
            'city'
        """
        # Use regex to find uppercase letters that are not at the start of the string
        # (?<!^) = negative lookbehind, don't match at start of string
        # (?=[A-Z]) = positive lookahead, match position before uppercase letter
        # Replace with hyphen and convert to lowercase
        return re.sub(r'(?<!^)(?=[A-Z])', '-', prop_name).lower()
    
    def get_typed_literal(value: Any) -> Literal:
        """
        Convert Python values to properly typed RDF literals.
        
        This function ensures that Python data types are correctly mapped to RDF
        literals with appropriate XSD (XML Schema Definition) datatypes. This is
        crucial for maintaining data type information in the RDF graph.
        
        Args:
            value (Any): Python value of any type (str, int, float, bool, etc.)
        
        Returns:
            Literal: RDFlib Literal object with appropriate XSD datatype
        
        Type Mappings:
            - bool → xsd:boolean
            - int → xsd:integer  
            - float → xsd:decimal
            - str (and others) → xsd:string (implicit)
        
        Examples:
            >>> get_typed_literal(True)
            Literal('true', datatype=XSD.boolean)
            >>> get_typed_literal(42)
            Literal('42', datatype=XSD.integer)
            >>> get_typed_literal(3.14)
            Literal('3.14', datatype=XSD.decimal)
            >>> get_typed_literal("hello")
            Literal('hello')
        """
        if isinstance(value, bool):
            # Boolean values: True/False → "true"^^xsd:boolean / "false"^^xsd:boolean
            return Literal(value, datatype=XSD.boolean)
        elif isinstance(value, int):
            # Integer values: 42 → "42"^^xsd:integer
            return Literal(value, datatype=XSD.integer)
        elif isinstance(value, float):
            # Floating point values: 3.14 → "3.14"^^xsd:decimal
            return Literal(value, datatype=XSD.decimal)
        else:
            # String and other types: "hello" → "hello" (implicit xsd:string)
            # Convert to string to handle any other Python types safely
            return Literal(str(value))
    
    # Process each graph object from the JSON data
    # Each object represents a complete graph with relationships and metadata
    for graph_obj in json_data:
        # Skip empty or None objects to avoid processing errors
        if not graph_obj:
            continue
        
        # Extract the main relationships from graphData field
        # This field contains pre-existing TTL triples that define relationships between entities
        graph_data = graph_obj.get('graphData', '')
        if graph_data:
            try:
                # Create a temporary graph to parse the existing TTL content
                temp_graph = Graph()
                
                # Parse the TTL string directly into the temporary graph
                # format='turtle' tells RDFlib to expect Turtle/TTL syntax
                temp_graph.parse(data=graph_data, format='turtle')
                
                # Add all parsed triples from the temporary graph to our main graph
                # This preserves existing relationships like "inst:address ibpdi:hasBuilding inst:building"
                for triple in temp_graph:
                    g.add(triple)  # Each triple is a (subject, predicate, object) tuple
                    
            except Exception as e:
                # Print warning but continue processing other objects
                # This makes the function robust against malformed TTL data
                print(f"Warning: Could not parse graph data: {e}")
                continue
        
        # Process metadata for individual entities (buildings, addresses, etc.)
        graph_metadata = graph_obj.get('graphMetadata', [])
        for metadata in graph_metadata:
            # Skip empty metadata entries
            if not metadata:
                continue
                
            # Extract entity information from metadata
            entity_id = metadata.get('id')              # Unique identifier (UUID)
            class_type = metadata.get('classType')      # RDF class URI (e.g., https://ibpdi.datacat.org/class/Building)
            properties_values = metadata.get('propertiesValues', {})  # Dictionary of property-value pairs
            
            # Skip entities without IDs as they can't be processed
            if not entity_id:
                continue
            
            # Create entity URI using the instance namespace
            # e.g., "bf0bf34a-a3a2-4616-a0f7-811a11641cc2" → inst:bf0bf34a-a3a2-4616-a0f7-811a11641cc2
            entity_uri = INST[entity_id]
            
            # Add RDF type information (rdf:type triple)
            if class_type:
                if class_type.startswith('https://ibpdi.datacat.org/class/'):
                    # Extract class name from full URI for cleaner namespace usage
                    # e.g., "https://ibpdi.datacat.org/class/Building" → "Building"
                    class_name = class_type.split('/')[-1]
                    # Add triple: inst:entity rdf:type ibpdi:Building
                    g.add((entity_uri, RDF.type, IBPDI[class_name]))
                else:
                    # Handle other class URIs by creating full URIRef
                    # Add triple: inst:entity rdf:type <full_class_uri>
                    g.add((entity_uri, RDF.type, URIRef(class_type)))
            
            # Add property-value pairs as RDF triples
            for prop_name, prop_value in properties_values.items():
                # Only process non-null values
                if prop_value is not None:
                    # Convert property name to RDF-friendly format
                    # e.g., "streetName" → "street-name"
                    clean_prop = clean_property_name(prop_name)
                    
                    # Create property URI in the property namespace
                    # e.g., "street-name" → prop:street-name
                    prop_uri = PROP[clean_prop]
                    
                    # Convert Python value to typed RDF literal
                    literal_value = get_typed_literal(prop_value)
                    
                    # Add property triple: inst:entity prop:property "value"^^datatype
                    g.add((entity_uri, prop_uri, literal_value))
        
        # Handle optional metadata fields (access rights, use case, etc.)
        # These are currently null in the example data, so we skip processing them
        # In a real implementation, these could be added as graph-level annotations
        access_rights = graph_obj.get('accessRights')  # Currently not processed (null in data)
        use_case = graph_obj.get('useCase')            # Currently not processed (null in data)
    
    # Serialize the complete RDF graph to TTL format
    # This creates a string representation of all triples in Turtle syntax
    ttl_result = g.serialize(format='turtle')
    
    # Save to file if output path is specified
    if output_file:
        try:
            # Write TTL content to file with UTF-8 encoding to handle special characters
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(ttl_result)
            print(f"TTL content saved to: {output_file}")
        except IOError as e:
            print(f"Error saving file: {e}")
            raise
    
    # Return the TTL string for further processing or inspection
    return ttl_result


def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Load and parse a JSON file containing graph data.
    
    This function safely loads a JSON file and parses it into a Python data structure.
    It includes error handling for common file operations (file not found, invalid JSON).
    
    Args:
        file_path (str): Path to the JSON file to load
        
    Returns:
        List[Dict[str, Any]]: Parsed JSON data as a list of dictionaries
        
    Raises:
        FileNotFoundError: If the specified file doesn't exist
        json.JSONDecodeError: If the file contains invalid JSON
        IOError: If there are file reading permissions issues
        
    Example:
        >>> data = load_json_file("assets/example_building_portfolio.json")
        >>> print(f"Loaded {len(data)} graph objects")
    """
    try:
        # Open file with UTF-8 encoding to handle international characters
        with open(file_path, 'r', encoding='utf-8') as f:
            # Parse JSON content into Python data structures
            json_data = json.load(f)
        
        # Validate that the result is a list (expected format)
        if not isinstance(json_data, list):
            raise ValueError(f"Expected JSON array, got {type(json_data)}")
            
        print(f"Successfully loaded {len(json_data)} graph objects from {file_path}")
        return json_data
        
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        print("Please check the file path and try again.")
        raise
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{file_path}': {e}")
        print("Please check the JSON syntax and try again.")
        raise
    except IOError as e:
        print(f"Error: Could not read file '{file_path}': {e}")
        print("Please check file permissions and try again.")
        raise


def main():
    """
    Main function to handle command-line execution of the JSON to TTL converter.
    
    This function processes command-line arguments and orchestrates the conversion process.
    It provides a simple command-line interface for the json_to_ttl_rdflib function.
    
    Command-line usage:
        python json_to_ttl_rdflib.py <input_json_file> [output_ttl_file]
        
    Arguments:
        input_json_file: Path to the input JSON file (required)
        output_ttl_file: Path for output TTL file (optional, defaults to input filename with .ttl extension)
        
    Examples:
        python json_to_ttl_rdflib.py data.json
        python json_to_ttl_rdflib.py assets/building_data.json output/building_data.ttl
        
    Exit codes:
        0: Success
        1: Error (invalid arguments, file not found, processing error)
    """
    
    # Check command-line arguments
    # sys.argv[0] is the script name, so we need at least 2 arguments total
    if len(sys.argv) < 2:
        print("Usage: python json_to_ttl_rdflib.py <input_json_file> [output_ttl_file]")
        print("\nArguments:")
        print("  input_json_file   : Path to the input JSON file containing graph data (required)")
        print("  output_ttl_file   : Path for the output TTL file (optional)")
        print("                      If not specified, uses input filename with .ttl extension")
        print("\nExamples:")
        print("  python json_to_ttl_rdflib.py assets/example_building_portfolio.json")
        print("  python json_to_ttl_rdflib.py data.json output.ttl")
        sys.exit(1)  # Exit with error code 1 to indicate failure
    
    # Extract command-line arguments
    input_file = sys.argv[1]  # First argument: input JSON file path
    
    # Determine output file path
    if len(sys.argv) >= 3:
        # Second argument provided: use as output file path
        output_file = sys.argv[2]
    else:
        # No output file specified: create one based on input filename
        # e.g., "data.json" → "data.ttl"
        base_name = os.path.splitext(input_file)[0]  # Remove file extension
        output_file = f"{base_name}.ttl"             # Add .ttl extension
    
    print("JSON to TTL Converter using RDFlib")
    print("=" * 40)
    print(f"Input file:  {input_file}")
    print(f"Output file: {output_file}")
    print("=" * 40)
    
    try:
        # Load and parse the input JSON file
        print("Loading JSON data...")
        json_data = load_json_file(input_file)
        
        # Convert JSON to TTL using RDFlib
        print("Converting to TTL format...")
        ttl_result = json_to_ttl_rdflib(json_data, output_file)
        
        # Display conversion results
        print("\nConversion completed successfully!")
        print(f"Generated TTL content: {len(ttl_result):,} characters")
        print(f"Number of lines: {len(ttl_result.splitlines()):,}")
        
        # Show a preview of the generated TTL
        print("\nPreview (first 10 lines):")
        print("-" * 30)
        lines = ttl_result.splitlines()
        for i, line in enumerate(lines[:10], 1):
            print(f"{i:2d}: {line}")
        
        if len(lines) > 10:
            print(f"    ... and {len(lines) - 10} more lines")
            
    except Exception as e:
        # Handle any errors that occurred during processing
        print(f"\nError during conversion: {e}")
        sys.exit(1)  # Exit with error code 1 to indicate failure


# Entry point: only run main() when script is executed directly (not imported)
if __name__ == "__main__":
    main()
