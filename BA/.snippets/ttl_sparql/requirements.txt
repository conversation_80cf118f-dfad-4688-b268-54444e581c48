# Requirements for TTL SPARQL Querying Scripts
# 
# This file specifies all Python libraries needed for the TTL SPARQL querying functionality.
# Install with: pip install -r requirements.txt (or uv pip install -r requirements.txt)

# RDFLib - The primary Python library for working with RDF data and SPARQL queries
# - Provides Graph class for loading and storing RDF/TTL data
# - Includes SPARQL 1.1 query engine for executing queries
# - Supports multiple RDF serialization formats (TTL, RDF/XML, N-Triples, etc.)
rdflib>=7.0.0

# pathlib is part of Python standard library (Python 3.4+)
# typing is part of Python standard library (Python 3.5+)
# json is part of Python standard library
# re is part of Python standard library
# traceback is part of Python standard library
