"""
SPARQL Query Validation Module

This module provides functionality to validate SPARQL query syntax without executing the query.
It uses RDFLib's built-in SPARQL parser to check if a query is syntactically correct.

Author: <PERSON><PERSON><PERSON> Labidi
Date: August 2025
"""

# Standard library imports
import argparse     # Command-line argument parsing
import sys          # System-specific parameters and functions
from pathlib import Path  # For file path operations
from typing import Dict, Any, Optional  # Type hints for better code documentation

# Third-party library imports
from rdflib.plugins.sparql import prepareQuery  # RDFLib's SPARQL query parser
# prepareQuery: Parses and validates SPARQL queries, throws exceptions for invalid syntax


class SPARQLValidationError(Exception):
    """
    Custom exception for SPARQL validation errors.
    
    This exception is raised when a SPARQL query fails syntax validation.
    It provides detailed information about what went wrong and where.
    """
    
    def __init__(self, message: str, query: str = None, line: int = None, column: int = None, 
                 parser_message: str = None, exception_type: str = None):
        """
        Initialize SPARQL validation error with detailed context.
        
        Args:
            message (str): Primary error message
            query (str, optional): The query that failed validation
            line (int, optional): Line number where error occurred
            column (int, optional): Column number where error occurred
            parser_message (str, optional): Detailed message from parser
            exception_type (str, optional): Type of the original exception
        """
        self.query = query
        self.line = line
        self.column = column
        self.parser_message = parser_message
        self.exception_type = exception_type
        
        # Build detailed error message
        error_parts = [message]
        
        if exception_type:
            error_parts.append(f"Exception type: {exception_type}")
        
        if query:
            query_preview = query.strip()
            if len(query_preview) > 200:
                query_preview = query_preview[:200] + "..."
            error_parts.append(f"Query: {repr(query_preview)}")
        
        if line is not None:
            error_parts.append(f"Line: {line}")
        
        if column is not None:
            error_parts.append(f"Column: {column}")
        
        if parser_message:
            error_parts.append(f"Parser message: {parser_message}")
        
        detailed_message = " | ".join(error_parts)
        super().__init__(detailed_message)


class QueryInputError(Exception):
    """Exception raised for invalid query input (empty, None, wrong type)."""
    pass


def validate_sparql_query(query: str) -> str:
    """
    Validate the syntax of a SPARQL query without executing it.
    
    This function uses RDFLib's prepareQuery function to parse the SPARQL query.
    If parsing succeeds, the query is syntactically valid. If it fails with an
    exception, raises a SPARQLValidationError with detailed information.
    
    Args:
        query (str): The SPARQL query string to validate
                    Can be any SPARQL 1.1 query type (SELECT, ASK, CONSTRUCT, DESCRIBE)
    
    Returns:
        str: The type of SPARQL query (SELECT, ASK, CONSTRUCT, DESCRIBE, UNKNOWN)
    
    Raises:
        QueryInputError: If query is empty, None, or not a string
        SPARQLValidationError: If query syntax is invalid
    
    Example:
        >>> query_type = validate_sparql_query("SELECT ?s ?p ?o WHERE { ?s ?p ?o . }")
        >>> print(query_type)  # 'SELECT'
        
        >>> validate_sparql_query("SELET ?s WHERE { ?s ?p ?o . }")  # Typo
        # Raises SPARQLValidationError with detailed information
    """
    if not query or not isinstance(query, str):
        if query is None:
            raise QueryInputError("Query cannot be None")
        elif not isinstance(query, str):
            raise QueryInputError(f"Query must be a string, got {type(query).__name__}: {repr(query)}")
        else:
            raise QueryInputError("Query cannot be empty string")
    
    try:
        # Attempt to parse the SPARQL query
        # prepareQuery() validates syntax and creates a reusable query object
        prepared_query = prepareQuery(query)
        
        # Extract the query type (SELECT, ASK, CONSTRUCT, DESCRIBE) from the query string
        query_type = _extract_query_type(query.strip())
        
        return query_type
        
    except Exception as e:
        # If prepareQuery() throws any exception, the query syntax is invalid
        # Extract detailed information from the exception
        line = getattr(e, 'lineno', None)
        column = getattr(e, 'col', None)
        parser_message = getattr(e, 'msg', None)
        exception_type = type(e).__name__
        
        raise SPARQLValidationError(
            message=f"SPARQL syntax validation failed: {str(e)}",
            query=query,
            line=line,
            column=column,
            parser_message=parser_message,
            exception_type=exception_type
        )


def _extract_query_type(query: str) -> str:
    """
    Extract the SPARQL query type from the query string.
    
    This is a private helper function that examines the beginning of the query
    to determine what type of SPARQL operation it represents.
    
    Args:
        query (str): The SPARQL query string (should be stripped of whitespace)
    
    Returns:
        str: The query type ('SELECT', 'ASK', 'CONSTRUCT', 'DESCRIBE', 'UNKNOWN')
    
    Note:
        This function performs case-insensitive matching and looks for the first
        keyword that appears after any PREFIX declarations.
    """
    if not query:
        return 'UNKNOWN'
        
    # Convert to uppercase for case-insensitive matching
    query_upper = query.upper().strip()
    
    # Remove PREFIX declarations to find the actual query type
    # PREFIX declarations can appear before the main query keyword
    lines = query_upper.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('PREFIX'):
            continue
            
        # Check for query type keywords at the start of non-prefix lines
        if line.startswith('SELECT'):
            return 'SELECT'
        elif line.startswith('ASK'):
            return 'ASK'
        elif line.startswith('CONSTRUCT'):
            return 'CONSTRUCT'
        elif line.startswith('DESCRIBE'):
            return 'DESCRIBE'
    
    # Fallback: check the entire query string for keywords
    if 'SELECT' in query_upper:
        return 'SELECT'
    elif 'ASK' in query_upper:
        return 'ASK'
    elif 'CONSTRUCT' in query_upper:
        return 'CONSTRUCT'
    elif 'DESCRIBE' in query_upper:
        return 'DESCRIBE'
    else:
        return 'UNKNOWN'


def main():
    """
    Main function for command-line usage.
    
    Parses command-line arguments and validates SPARQL queries provided as arguments
    or reads from files.
    """
    parser = argparse.ArgumentParser(
        description='Validate SPARQL query syntax',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
Examples:
  python validate_sparql_query.py "SELECT ?s ?p ?o WHERE { ?s ?p ?o . }"
  python validate_sparql_query.py --file query.sparql
        '''
    )
    
    parser.add_argument(
        'query',
        nargs='?',
        help='SPARQL query string to validate'
    )
    
    parser.add_argument(
        '-f', '--file',
        help='Read SPARQL query from file'
    )
    
    parser.add_argument(
        '-q', '--quiet',
        action='store_true',
        help='Only output validation result (valid/invalid)'
    )
    
    args = parser.parse_args()
    
    # Determine query source
    query = None
    if args.file:
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                query = f.read()
        except FileNotFoundError:
            error_msg = f"File access error: Query file '{args.file}' not found. "
            error_msg += f"Checked path: {Path(args.file).absolute()}. "
            error_msg += "Please verify the file path and ensure the file exists."
            print(f"Error: {error_msg}", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            error_msg = f"File reading error: Failed to read query file '{args.file}'. "
            error_msg += f"Exception: {type(e).__name__}: {str(e)}. "
            error_msg += f"File path: {Path(args.file).absolute()}."
            print(f"Error: {error_msg}", file=sys.stderr)
            sys.exit(1)
    elif args.query:
        query = args.query
    else:
        parser.print_help()
        sys.exit(1)
    
    # Validate the query
    try:
        query_type = validate_sparql_query(query)
        
        # Output results
        if args.quiet:
            print("valid")
        else:
            print(f"✅ Query is VALID (Type: {query_type})")
        
        # Exit with success code
        sys.exit(0)
        
    except (QueryInputError, SPARQLValidationError) as e:
        # Handle validation errors
        if args.quiet:
            print("invalid")
        else:
            print(f"❌ Query is INVALID: {str(e)}")
        
        # Exit with error code
        sys.exit(1)


if __name__ == "__main__":
    main()
