"""
TTL File SPARQL Querying Module

This module provides functionality to load TTL (Turtle) RDF files and execute SPARQL queries
against them. It includes a comprehensive TTL query engine that works with any TTL file structure
and vocabulary, making it completely generic and reusable.

Features:
- Load TTL files in any RDF vocabulary/schema
- Execute all SPARQL 1.1 query types (SELECT, ASK, CONSTRUCT, DESCRIBE)
- Comprehensive error handling with proper exceptions
- Detailed result processing and formatting
- Generic exploration of TTL file structure

Author: Ghassen Labidi
Date: August 2025
"""

# Standard library imports
import argparse     # For command-line argument parsing
import json         # For JSON serialization of results (if needed)
import sys          # For system exit codes and stderr output
import traceback    # For detailed error reporting and debugging
from pathlib import Path      # For cross-platform file path handling
from typing import Dict, Any, List, Union, Optional  # Type hints for better code documentation

# Third-party library imports
from rdflib import Graph      # Core RDF graph for storing and querying RDF data
from rdflib.plugins.sparql import prepareQuery  # SPARQL query parser and validator

# Import validation function and exceptions from our companion module
from validate_sparql_query import validate_sparql_query, SPARQLValidationError, QueryInputError


class TTLFileError(Exception):
    """
    Custom exception for TTL file loading errors.
    
    This exception is raised when a TTL file cannot be loaded due to various issues
    like file not found, parsing errors, permission issues, etc.
    """
    
    def __init__(self, message: str, file_path: str = None, file_size: int = None,
                 line_number: int = None, parsing_error: bool = False, exception_type: str = None):
        """
        Initialize TTL file error with detailed context.
        
        Args:
            message (str): Primary error message
            file_path (str, optional): Path to the problematic file
            file_size (int, optional): Size of the file in bytes
            line_number (int, optional): Line number where parsing failed
            parsing_error (bool): Whether this is a TTL parsing error
            exception_type (str, optional): Type of the original exception
        """
        self.file_path = file_path
        self.file_size = file_size
        self.line_number = line_number
        self.parsing_error = parsing_error
        self.exception_type = exception_type
        
        # Build detailed error message
        error_parts = [message]
        
        if exception_type:
            error_parts.append(f"Exception type: {exception_type}")
        
        if file_path:
            error_parts.append(f"File: {file_path}")
        
        if file_size is not None:
            error_parts.append(f"Size: {file_size} bytes")
        
        if line_number is not None:
            error_parts.append(f"Line: {line_number}")
        
        if parsing_error:
            error_parts.append("This appears to be a TTL syntax error")
        
        detailed_message = " | ".join(error_parts)
        super().__init__(detailed_message)


class SPARQLExecutionError(Exception):
    """
    Custom exception for SPARQL query execution errors.
    
    This exception is raised when a SPARQL query fails to execute against
    the loaded RDF graph, providing context about the query and graph state.
    """
    
    def __init__(self, message: str, query: str = None, query_type: str = None,
                 graph_size: int = None, namespaces_count: int = None, 
                 ttl_file: str = None, exception_type: str = None):
        """
        Initialize SPARQL execution error with detailed context.
        
        Args:
            message (str): Primary error message
            query (str, optional): The query that failed
            query_type (str, optional): Type of the failed query
            graph_size (int, optional): Number of triples in the graph
            namespaces_count (int, optional): Number of available namespaces
            ttl_file (str, optional): Path to the loaded TTL file
            exception_type (str, optional): Type of the original exception
        """
        self.query = query
        self.query_type = query_type
        self.graph_size = graph_size
        self.namespaces_count = namespaces_count
        self.ttl_file = ttl_file
        self.exception_type = exception_type
        
        # Build detailed error message
        error_parts = [message]
        
        if exception_type:
            error_parts.append(f"Exception type: {exception_type}")
        
        if ttl_file:
            error_parts.append(f"TTL file: {ttl_file}")
        
        if graph_size is not None:
            error_parts.append(f"Graph size: {graph_size} triples")
        
        if namespaces_count is not None:
            error_parts.append(f"Namespaces: {namespaces_count}")
        
        if query_type:
            error_parts.append(f"Query type: {query_type}")
        
        if query:
            query_preview = query.strip()
            if len(query_preview) > 150:
                query_preview = query_preview[:150] + "..."
            error_parts.append(f"Query: {repr(query_preview)}")
        
        detailed_message = " | ".join(error_parts)
        super().__init__(detailed_message)


class TTLEngineError(Exception):
    """Exception raised when the TTL engine is in an invalid state."""
    pass


class TTLQueryEngine:
    """
    A comprehensive engine for loading TTL files and executing SPARQL queries.
    
    This class provides generic functionality that works with any TTL file structure,
    vocabulary, or schema. It doesn't assume any specific RDF ontology or data model.
    
    Key features:
    - Loads TTL files using RDFLib's robust parsing
    - Validates SPARQL queries before execution
    - Processes results differently based on query type
    - Extracts and stores namespace information
    - Provides detailed error reporting
    
    Attributes:
        graph (rdflib.Graph): The RDF graph containing loaded TTL data
        loaded_file (pathlib.Path|None): Path to the currently loaded TTL file
        namespaces (dict): Dictionary mapping namespace prefixes to their URIs
    """
    
    def __init__(self):
        """
        Initialize a new TTL Query Engine instance.
        
        Creates an empty RDF graph and initializes tracking variables.
        No TTL file is loaded initially - call load_ttl_file() first.
        """
        self.graph = Graph()           # Empty RDF graph to store TTL data
        self.loaded_file = None        # No file loaded initially
        self.namespaces = {}          # Empty namespace dictionary
    
    def load_ttl_file(self, file_path: Union[str, Path]) -> None:
        """
        Load a TTL (Turtle) file into the RDF graph.
        
        This method clears any previously loaded data and loads the specified TTL file.
        It also extracts namespace prefixes defined in the file for reference.
        
        Args:
            file_path (Union[str, Path]): Path to the TTL file to load
                                        Can be string or pathlib.Path object
        
        Raises:
            TTLFileError: If file cannot be loaded due to various issues:
                - File not found
                - Path is a directory, not a file
                - TTL parsing errors
                - Permission issues
                - IO errors
        
        Side Effects:
            - Clears self.graph and loads new data
            - Updates self.loaded_file with the file path
            - Updates self.namespaces with extracted prefixes
        
        Example:
            >>> engine = TTLQueryEngine()
            >>> engine.load_ttl_file("data/example.ttl")
            # File loaded successfully or raises TTLFileError
        """
        try:
            # Convert string path to pathlib.Path for consistent handling
            file_path = Path(file_path)
            abs_file_path = file_path.absolute()
            
            # Check if the file exists before attempting to load
            if not file_path.exists():
                raise TTLFileError(
                    message=f"TTL file not found: '{file_path}' | Absolute path: '{abs_file_path}' | Working directory: '{Path.cwd()}'",
                    file_path=str(abs_file_path)
                )
            
            # Check if path is actually a file
            if not file_path.is_file():
                path_type = "directory" if file_path.is_dir() else "special file"
                raise TTLFileError(
                    message=f"Path exists but is not a regular file: '{file_path}' | Path type: {path_type}",
                    file_path=str(abs_file_path)
                )
            
            # Get file size for context
            file_size = file_path.stat().st_size
            
            # Clear any previously loaded RDF data
            self.graph = Graph()
            
            # Load the TTL file using RDFLib's built-in Turtle parser
            self.graph.parse(file_path, format="turtle")
            
            # Store the successfully loaded file path for reference
            self.loaded_file = file_path
            
            # Extract namespace prefixes defined in the TTL file
            self.namespaces = dict(self.graph.namespaces())
            
        except TTLFileError:
            # Re-raise our custom exceptions as-is
            raise
        except Exception as e:
            # Catch any other errors during file loading
            error_message = f"Failed to load TTL file '{file_path}': {str(e)}"
            
            # Determine if this is likely a parsing error
            is_parsing_error = any(keyword in str(e).lower() 
                                 for keyword in ['parsing', 'turtle', 'syntax', 'bad syntax'])
            
            # Extract line number if available
            line_number = getattr(e, 'lineno', None)
            
            # Get file size if possible
            file_size = None
            try:
                if 'file_path' in locals() and file_path.exists():
                    file_size = file_path.stat().st_size
            except:
                pass
            
            raise TTLFileError(
                message=error_message,
                file_path=str(file_path.absolute() if 'file_path' in locals() else 'unknown'),
                file_size=file_size,
                line_number=line_number,
                parsing_error=is_parsing_error,
                exception_type=type(e).__name__
            )
    
    def execute_sparql_query(self, query: str, validate_first: bool = True) -> Dict[str, Any]:
        """
        Execute a SPARQL query against the loaded TTL data.
        
        This method validates the query syntax (optional), executes it against the RDF graph,
        and processes the results based on the query type (SELECT, ASK, etc.).
        
        Args:
            query (str): The SPARQL query string to execute
            validate_first (bool): Whether to validate query syntax before execution
                                 Default is True for safety
        
        Returns:
            Dict[str, Any]: A result dictionary containing:
                - 'results' (List|bool|None): Query results (format depends on query type)
                - 'query_type' (str): Type of SPARQL query executed
                - 'result_count' (int|None): Number of results (for SELECT/CONSTRUCT)
        
        Raises:
            TTLEngineError: If no TTL file has been loaded
            SPARQLValidationError: If query validation fails (when validate_first=True)
            QueryInputError: If query input is invalid
            SPARQLExecutionError: If query execution fails
        
        Result formats by query type:
            - SELECT: List of dictionaries (one per result row)
            - ASK: Boolean value
            - CONSTRUCT/DESCRIBE: List of dictionaries (one per triple)
        
        Example:
            >>> result = engine.execute_sparql_query("SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 5")
            >>> for row in result['results']:
            ...     print(row)
        """
        # Check if a TTL file has been loaded
        if not self.loaded_file:
            raise TTLEngineError(
                f"No TTL file loaded in engine. Call load_ttl_file() first. Current graph state: empty ({len(self.graph)} triples)"
            )
        
        # Validate query syntax if requested (recommended for safety)
        if validate_first:
            try:
                query_type = validate_sparql_query(query)
            except (QueryInputError, SPARQLValidationError) as e:
                # Re-raise validation errors with additional context
                raise SPARQLValidationError(f"Query validation failed before execution: {str(e)}")
        else:
            # Still need to determine query type even if not validating
            query_type = self._get_query_type(query.strip())
        
        try:
            # Execute the SPARQL query against the loaded RDF graph
            results = self.graph.query(query)
            
            # Process results based on the query type
            processed_results = self._process_query_results(results, query_type)
            
            # Return successful execution result
            return {
                'results': processed_results,
                'query_type': query_type,
                'result_count': len(processed_results) if isinstance(processed_results, list) else None
            }
            
        except Exception as e:
            # Catch any errors during query execution and provide rich context
            error_message = f"SPARQL query execution failed: {str(e)}"
            
            # Determine if this looks like a namespace/prefix error
            is_prefix_error = any(keyword in str(e).lower() 
                                for keyword in ['prefix', 'namespace', 'qname', 'undefined'])
            
            # Get available prefixes for context if it's a prefix error
            available_prefixes = None
            if is_prefix_error and self.namespaces:
                available_prefixes = list(self.namespaces.keys())[:10]  # First 10 prefixes
            
            raise SPARQLExecutionError(
                message=error_message,
                query=query,
                query_type=query_type if 'query_type' in locals() else self._get_query_type(query.strip()),
                graph_size=len(self.graph),
                namespaces_count=len(self.namespaces),
                ttl_file=str(self.loaded_file),
                exception_type=type(e).__name__
            )
    
    def _get_query_type(self, query: str) -> str:
        """
        Extract the SPARQL query type from the query string.
        
        Private helper method that identifies whether the query is SELECT, ASK, 
        CONSTRUCT, DESCRIBE, or unknown by examining the query text.
        
        Args:
            query (str): The SPARQL query string (should be stripped of whitespace)
        
        Returns:
            str: The query type ('SELECT', 'ASK', 'CONSTRUCT', 'DESCRIBE', 'UNKNOWN')
        """
        # Convert to uppercase for case-insensitive matching
        query_upper = query.upper().strip()
        
        # Check for each query type keyword at the beginning of the query
        # Note: This handles PREFIX declarations by looking past them
        if 'SELECT' in query_upper:
            return 'SELECT'
        elif 'ASK' in query_upper:
            return 'ASK'
        elif 'CONSTRUCT' in query_upper:
            return 'CONSTRUCT'
        elif 'DESCRIBE' in query_upper:
            return 'DESCRIBE'
        else:
            return 'UNKNOWN'
    
    def _process_query_results(self, results, query_type: str) -> Union[List[Dict], bool, List[Dict]]:
        """
        Process query results based on the SPARQL query type.
        
        Different SPARQL query types return results in different formats from RDFLib.
        This method normalizes them into consistent Python data structures.
        
        Args:
            results: Raw results from RDFLib's query execution
            query_type (str): Type of SPARQL query ('SELECT', 'ASK', etc.)
        
        Returns:
            Union[List[Dict], bool, List[Dict]]: Processed results:
                - SELECT: List of dictionaries (variable bindings per row)
                - ASK: Boolean value
                - CONSTRUCT/DESCRIBE: List of dictionaries (triples as dicts)
        """
        if query_type == 'SELECT':
            # SELECT queries return variable bindings for each result row
            # Convert to list of dictionaries for easy access
            result_list = []
            for row in results:
                row_dict = {}
                # Process each variable in the SELECT clause
                for var in results.vars:
                    value = row[var] if row[var] is not None else None
                    # Convert RDFLib terms to strings for JSON serialization
                    if value is not None:
                        row_dict[str(var)] = str(value)
                    else:
                        row_dict[str(var)] = None
                result_list.append(row_dict)
            return result_list
            
        elif query_type == 'ASK':
            # ASK queries return a boolean result
            # Convert RDFLib's result to Python boolean
            return bool(results)
            
        elif query_type in ['CONSTRUCT', 'DESCRIBE']:
            # CONSTRUCT and DESCRIBE queries return RDF triples
            # Convert each triple to a dictionary with subject/predicate/object
            triples_list = []
            for triple in results:
                triples_list.append({
                    'subject': str(triple[0]),     # Subject of the triple
                    'predicate': str(triple[1]),   # Predicate (property) of the triple
                    'object': str(triple[2])       # Object (value) of the triple
                })
            return triples_list
            
        else:
            # Fallback for unknown query types
            # Convert results to list format
            return list(results)


def query_ttl_file(ttl_file_path: str, sparql_query: str, validate_first: bool = True) -> Dict[str, Any]:
    """
    Complete function to load a TTL file and execute a SPARQL query in one step.
    
    This is a convenience function that combines TTL file loading and query execution
    into a single function call. It creates a new TTLQueryEngine instance for each call.
    
    Args:
        ttl_file_path (str): Path to the TTL file to load and query
        sparql_query (str): SPARQL query string to execute
        validate_first (bool): Whether to validate query syntax before execution
    
    Returns:
        Dict[str, Any]: Result dictionary containing:
            - 'results': Query results (format depends on query type)
            - 'query_type': Type of SPARQL query executed
            - 'result_count': Number of results (for SELECT/CONSTRUCT)
    
    Raises:
        TTLFileError: If TTL file cannot be loaded
        QueryInputError: If query input is invalid
        SPARQLValidationError: If query validation fails
        SPARQLExecutionError: If query execution fails
    
    Example:
        >>> result = query_ttl_file("data/example.ttl", "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 5")
        >>> for row in result['results']:
        ...     print(row)
    """
    # Create a new query engine instance
    engine = TTLQueryEngine()
    
    # Load the TTL file (will raise TTLFileError if it fails)
    engine.load_ttl_file(ttl_file_path)
    
    # Execute the SPARQL query and return results (will raise appropriate exceptions if it fails)
    return engine.execute_sparql_query(sparql_query, validate_first=validate_first)


def main():
    """
    Main function for command-line usage.
    
    Parses command-line arguments and executes SPARQL queries against TTL files.
    """
    parser = argparse.ArgumentParser(
        description='Execute SPARQL queries against TTL files',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
Examples:
  # Query a TTL file with a SPARQL query
  python query_ttl_with_sparql.py data.ttl "SELECT ?s ?p ?o WHERE { ?s ?p ?o . } LIMIT 10"
  
  # Read query from file
  python query_ttl_with_sparql.py data.ttl --query-file query.sparql
  
  # Output results as JSON
  python query_ttl_with_sparql.py data.ttl "ASK { ?s ?p ?o }" --output json
        '''
    )
    
    parser.add_argument(
        'ttl_file',
        help='Path to the TTL file to query'
    )
    
    parser.add_argument(
        'query',
        nargs='?',
        help='SPARQL query string to execute'
    )
    
    parser.add_argument(
        '-f', '--query-file',
        help='Read SPARQL query from file'
    )
    
    parser.add_argument(
        '-o', '--output',
        choices=['text', 'json'],
        default='text',
        help='Output format (default: text)'
    )
    
    parser.add_argument(
        '--no-validate',
        action='store_true',
        help='Skip SPARQL query validation'
    )
    
    parser.add_argument(
        '--limit-results',
        type=int,
        default=100,
        help='Limit number of results displayed (default: 100)'
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.query and not args.query_file:
        print("Error: Must provide either a query string or --query-file", file=sys.stderr)
        sys.exit(1)
    
    # Get the query
    query = None
    if args.query_file:
        try:
            with open(args.query_file, 'r', encoding='utf-8') as f:
                query = f.read()
        except FileNotFoundError:
            error_msg = f"Query file access error: File '{args.query_file}' not found. "
            error_msg += f"Absolute path checked: {Path(args.query_file).absolute()}. "
            error_msg += f"Current directory: {Path.cwd()}. "
            error_msg += "Please verify the file path and ensure the query file exists."
            print(f"Error: {error_msg}", file=sys.stderr)
            sys.exit(1)
        except Exception as e:
            error_msg = f"Query file reading error: Failed to read '{args.query_file}'. "
            error_msg += f"Exception: {type(e).__name__}: {str(e)}. "
            error_msg += f"File path: {Path(args.query_file).absolute()}. "
            
            # Add file information if possible
            try:
                file_path = Path(args.query_file)
                if file_path.exists():
                    error_msg += f"File size: {file_path.stat().st_size} bytes. "
                    error_msg += f"File permissions: readable={file_path.is_file() and file_path.stat().st_mode}."
            except:
                error_msg += "Unable to get file information."
                
            print(f"Error: {error_msg}", file=sys.stderr)
            sys.exit(1)
    else:
        query = args.query
    
    # Execute the query
    try:
        result = query_ttl_file(
            args.ttl_file, 
            query, 
            validate_first=not args.no_validate
        )
        
        # Output results
        if args.output == 'json':
            # Convert result to JSON-serializable format
            json_result = {
                'success': True,
                'query_type': result['query_type'],
                'result_count': result['result_count'],
                'error': None
            }
            
            # Handle different result types for JSON output
            if result['results'] is not None:
                if result['query_type'] == 'SELECT':
                    json_result['results'] = [
                        {str(var): str(value) for var, value in row.items()} 
                        for row in (result['results'] or [])[:args.limit_results]
                    ]
                elif result['query_type'] == 'ASK':
                    json_result['results'] = bool(result['results'])
                elif result['query_type'] in ['CONSTRUCT', 'DESCRIBE']:
                    json_result['results'] = [
                        {
                            'subject': str(triple['subject']),
                            'predicate': str(triple['predicate']),
                            'object': str(triple['object'])
                        }
                        for triple in (result['results'] or [])[:args.limit_results]
                    ]
            else:
                json_result['results'] = None
                
            print(json.dumps(json_result, indent=2))
            
        else:
            # Text output
            print(f"Query executed successfully (Type: {result['query_type']})")
            
            if result['query_type'] == 'SELECT' and result['results']:
                print(f"Found {result['result_count']} results:")
                for i, row in enumerate(result['results'][:args.limit_results], 1):
                    row_str = ', '.join(f"{var}={value}" for var, value in row.items())
                    print(f"  {i}. {row_str}")
                if result['result_count'] > args.limit_results:
                    print(f"  ... and {result['result_count'] - args.limit_results} more results")
                    
            elif result['query_type'] == 'ASK':
                print(f"ASK result: {result['results']}")
                
            elif result['query_type'] in ['CONSTRUCT', 'DESCRIBE'] and result['results']:
                print(f"Found {result['result_count']} triples:")
                for i, triple in enumerate(result['results'][:args.limit_results], 1):
                    print(f"  {i}. {triple['subject']} {triple['predicate']} {triple['object']}")
                if result['result_count'] > args.limit_results:
                    print(f"  ... and {result['result_count'] - args.limit_results} more triples")
                    
            elif result['result_count'] == 0:
                print("No results found")
                
    except (TTLFileError, QueryInputError, SPARQLValidationError, SPARQLExecutionError, TTLEngineError) as e:
        if args.output == 'json':
            error_result = {
                'success': False,
                'error': str(e),
                'query_type': None,
                'result_count': None,
                'results': None
            }
            print(json.dumps(error_result, indent=2))
        else:
            print(f"Query execution failed: {str(e)}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        # Catch any unexpected exceptions
        error_msg = f"Unexpected error: {type(e).__name__}: {str(e)} | TTL file: {args.ttl_file}"
        if 'query' in locals():
            query_preview = query.strip()[:100] if query else "None"
            error_msg += f" | Query preview: {repr(query_preview)}"
            
        if args.output == 'json':
            error_result = {
                'success': False,
                'error': error_msg,
                'query_type': None,
                'result_count': None,
                'results': None
            }
            print(json.dumps(error_result, indent=2))
        else:
            print(f"Error: {error_msg}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
