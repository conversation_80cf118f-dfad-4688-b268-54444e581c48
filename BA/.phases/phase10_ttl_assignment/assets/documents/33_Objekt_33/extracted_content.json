{"schema_name": "DoclingDocument", "version": "1.7.0", "name": "33_<PERSON>bjekt_33", "origin": {"mimetype": "application/pdf", "binary_hash": 4205012331629262788, "filename": "33_Objekt_33.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/pictures/0"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/pictures/1"}, {"$ref": "#/pictures/2"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/22"}, {"$ref": "#/tables/1"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/52"}, {"$ref": "#/tables/2"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/tables/3"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}, {"$ref": "#/texts/71"}, {"$ref": "#/texts/72"}, {"$ref": "#/pictures/4"}, {"$ref": "#/tables/4"}, {"$ref": "#/texts/73"}, {"$ref": "#/tables/5"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/texts/76"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}, {"$ref": "#/texts/63"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 49.076, "t": 796.2509588867186, "r": 141.056, "b": 788.2739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Deka-ImmobilienEuropa", "text": "Deka-ImmobilienEuropa", "level": 1}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 49.076, "t": 782.3309588867187, "r": 199.635, "b": 774.3539588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "'FLZ - Fracht- und Logistik Zentrum'", "text": "'FLZ - Fracht- und Logistik Zentrum'", "level": 1}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 768.3299588867187, "r": 147.924, "b": 761.5229588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Cargo City Süd Gebäude 558", "text": "Cargo City Süd Gebäude 558", "level": 1}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 755.2509588867186, "r": 133.501, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "60549 Frankfurt am Main", "text": "60549 Frankfurt am Main"}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 595.8309588867187, "r": 79.193, "b": 589.5559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "level": 1}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 512.5929588867186, "r": 123.664, "b": 508.55095888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "jeweiligen Jahres-/Halbjahresberichten.", "text": "jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 476.0, "t": 771.0136922200521, "r": 510.6666666666667, "b": 759.0136922200521, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 733.1189588867187, "r": 286.753, "b": 726.4649588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 723.3989588867187, "r": 286.753, "b": 716.7449588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 713.6789588867186, "r": 286.753, "b": 707.0249588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 703.9589588867186, "r": 286.753, "b": 697.3049588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 694.2389588867187, "r": 286.753, "b": 687.5849588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 684.5189588867187, "r": 286.753, "b": 677.8649588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 674.7989588867187, "r": 286.753, "b": 668.1449588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 713.1909588867187, "r": 424.448, "b": 706.9159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "Frankfurter Flughafen in ca. 11 Fahrminuten", "text": "Frankfurter Flughafen in ca. 11 Fahrminuten"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 703.4709588867187, "r": 417.365, "b": 697.1959588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 38]}], "orig": "FFM Hauptbahnhof in ca. 20 Fahrminuten", "text": "FFM Hauptbahnhof in ca. 20 Fahrminuten"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 693.7509588867186, "r": 462.032, "b": 687.4759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Reichhaltiges gastronomisches Angebot direkt am Objekt", "text": "Reichhaltiges gastronomisches Angebot direkt am Objekt"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 684.0309588867186, "r": 431.545, "b": 677.7559588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 45]}], "orig": "FRAPORT Airport Shuttle in unmittelbarer Nähe", "text": "FRAPORT Airport Shuttle in unmittelbarer Nähe"}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 674.3109588867187, "r": 416.703, "b": 668.0359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "Sicherheitsbereich Frankfurt Int. Flughafen", "text": "Sicherheitsbereich Frankfurt Int. Flughafen"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 742.9509588867187, "r": 298.292, "b": 736.6759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "Lage", "text": "Lage", "level": 1}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 732.6309588867186, "r": 432.293, "b": 726.3559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 45]}], "orig": "Verkehrsgünstige Lage in der \"Cargo City Süd\"", "text": "Verkehrsgünstige Lage in der \"Cargo City Süd\""}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 722.9109588867186, "r": 442.216, "b": 716.6359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "A5 und \"Frankfurter Kreuz\" in unmittelbarer Nähe", "text": "A5 und \"Frankfurter Kreuz\" in unmittelbarer Nähe"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 455.036, "t": 596.3109588867187, "r": 521.469, "b": 590.0359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "Stichtag: 31.12.2022", "text": "Stichtag: 31.12.2022", "level": 1}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 499.23095888671867, "r": 239.14, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "text": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "level": 1}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 499.23095888671867, "r": 405.309, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Verkehrswerthistorie in EUR in TSD.*", "text": "Verkehrswerthistorie in EUR in TSD.*"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 187.733, "t": 482.14195888671867, "r": 235.572, "b": 477.9899588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "Lager/Logistik: 55,61 %", "text": "Lager/Logistik: 55,61 %"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 187.733, "t": 468.30395888671865, "r": 217.525, "b": 464.15195888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "Büro: 36,87 %", "text": "Büro: 36,87 %"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 187.733, "t": 454.4659588867187, "r": 219.604, "b": 450.3139588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "Handel: 4,26 %", "text": "Handel: 4,26 %"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 187.733, "t": 440.62795888671866, "r": 212.002, "b": 436.4749588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "Kfz: 2,96 %", "text": "Kfz: 2,96 %"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 187.733, "t": 426.7899588867187, "r": 222.517, "b": 422.6369588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "Sonstige: 0,31 %", "text": "Sonstige: 0,31 %"}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 302.435, "t": 472.61395888671865, "r": 318.116, "b": 468.05295888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "10.325", "text": "10.325"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 325.976, "t": 469.69095888671865, "r": 341.657, "b": 465.12995888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "10.245", "text": "10.245"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 360.055, "t": 473.85895888671865, "r": 375.737, "b": 469.2979588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "10.635", "text": "10.635"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 387.548, "t": 472.61395888671865, "r": 403.23, "b": 468.05295888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "10.455", "text": "10.455"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 419.687, "t": 469.86095888671866, "r": 432.529, "b": 465.2999588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "9.640", "text": "9.640"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 449.779, "t": 480.18295888671867, "r": 465.461, "b": 475.6219588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "12.340", "text": "12.340"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 481.028, "t": 483.05395888671865, "r": 496.71, "b": 478.49295888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "12.925", "text": "12.925"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 295.73, "t": 434.00095888671865, "r": 298.591, "b": 429.43995888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 285.763, "t": 442.03395888671866, "r": 298.604, "b": 437.4729588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "2.000", "text": "2.000"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 285.763, "t": 450.06595888671865, "r": 298.604, "b": 445.50495888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "4.000", "text": "4.000"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 285.763, "t": 458.0979588867187, "r": 298.604, "b": 453.53695888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "6.000", "text": "6.000"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 285.763, "t": 466.12995888671867, "r": 298.604, "b": 461.56995888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "8.000", "text": "8.000"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.917, "t": 474.1629588867187, "r": 298.599, "b": 469.60195888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "10.000", "text": "10.000"}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.917, "t": 482.19495888671867, "r": 298.599, "b": 477.6339588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "12.000", "text": "12.000"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.913, "t": 490.2199588867187, "r": 298.595, "b": 485.65895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "14.000", "text": "14.000"}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 312.815, "t": 428.0119588867187, "r": 324.197, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2016", "text": "2016"}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 343.976, "t": 428.0119588867187, "r": 355.358, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2017", "text": "2017"}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 375.138, "t": 428.0119588867187, "r": 386.52, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2018", "text": "2018"}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 406.3, "t": 428.0119588867187, "r": 417.682, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2019", "text": "2019"}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 437.462, "t": 428.0119588867187, "r": 448.844, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2020", "text": "2020"}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 468.623, "t": 428.0119588867187, "r": 480.005, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2021", "text": "2021"}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 498.365, "t": 428.0119588867187, "r": 512.587, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "2022*", "text": "2022*"}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 405.99095888671866, "r": 144.55, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Auslaufende Mietverträge**", "text": "Auslaufende Mietverträge**", "level": 1}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 294.5519588867187, "r": 250.624, "b": 290.50995888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 99]}], "orig": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die", "text": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die"}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 288.43295888671867, "r": 270.351, "b": 284.39095888671864, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 105]}, {"page_no": 1, "bbox": {"l": 48.476, "t": 282.3119588867187, "r": 220.984, "b": 278.26995888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [106, 192]}], "orig": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt.", "text": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt."}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 269.4309588867186, "r": 116.035, "b": 263.15595888671874, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "Objektbeschreibung", "text": "Objektbeschreibung", "level": 1}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 259.7109588867187, "r": 241.248, "b": 252.7049588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "■ Moderne Logistikimmobilie mit 2-geschossigen Büroeinheiten", "text": "■ Moderne Logistikimmobilie mit 2-geschossigen Büroeinheiten", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 250.23095888671867, "r": 197.677, "b": 242.9849588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "■ LKW- und PKW-Stellplätze direkt am Gebäude", "text": "■ LKW- und PKW-Stellplätze direkt am Gebäude", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 240.99095888671866, "r": 145.591, "b": 233.74495888671868, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "■ 3 getrennte Eingangsbereiche", "text": "■ 3 getrennte Eingangsbereiche", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 231.75095888671865, "r": 128.56, "b": 224.50495888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "■ Restaurant am Gebäude", "text": "■ Restaurant am Gebäude", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 222.51095888671864, "r": 147.472, "b": 215.26495888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "■ Außenliegender Sonnenschutz", "text": "■ Außenliegender Sonnenschutz", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 213.27095888671863, "r": 151.511, "b": 206.02495888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 33]}], "orig": "■ Kleinteilige Vermietung möglich", "text": "■ Kleinteilige Vermietung möglich", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 204.03095888671874, "r": 220.735, "b": 196.78495888671864, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 56]}], "orig": "■ Optimales Verhältnis von Logistikfläche zu Büroflächen", "text": "■ Optimales Verhältnis von Logistikfläche zu Büroflächen", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 194.79095888671873, "r": 237.764, "b": 187.54495888671863, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 58]}], "orig": "■ Moderne Büros mit allen Möglichkeiten der Raumgestaltung", "text": "■ Moderne Büros mit allen Möglichkeiten der Raumgestaltung", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 186.61995888671868, "r": 53.012, "b": 182.04095888671873, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "", "text": ""}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 53.036, "t": 186.15095888671863, "r": 116.985, "b": 181.8979588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "zurück zum Inhaltsverzeichnis", "text": "zurück zum Inhaltsverzeichnis"}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 179.83195888671867, "r": 500.133, "b": 135.4699588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1518]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt. Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt. Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche"}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 405.99095888671866, "r": 394.671, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "Fläche nach Vermietungssituation", "text": "Fläche nach Vermietungssituation", "level": 1}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 49.076, "t": 796.2509588867186, "r": 141.056, "b": 788.2739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Deka-ImmobilienEuropa", "text": "Deka-ImmobilienEuropa", "level": 1}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 49.076, "t": 782.3309588867187, "r": 199.635, "b": 774.3539588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "'FLZ - Fracht- und Logistik Zentrum'", "text": "'FLZ - Fracht- und Logistik Zentrum'", "level": 1}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 768.3299588867187, "r": 147.924, "b": 761.5229588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Cargo City Süd Gebäude 558", "text": "Cargo City Süd Gebäude 558"}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 755.2509588867186, "r": 133.501, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "60549 Frankfurt am Main", "text": "60549 Frankfurt am Main"}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 708.1509588867186, "r": 129.465, "b": 701.8759588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "Nachhaltigkeitsangaben", "text": "Nachhaltigkeitsangaben", "level": 1}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 663.5109588867186, "r": 279.67, "b": 657.2359588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 64]}], "orig": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "text": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "level": 1}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 472.15195888671866, "r": 388.568, "b": 455.87095888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 369]}], "orig": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.", "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 354.25995888671866, "r": 116.986, "b": 349.5379588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": " zurück zum Inhaltsverzeichnis", "text": " zurück zum Inhaltsverzeichnis", "level": 1}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 346.03195888671866, "r": 288.357, "b": 341.9899588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 112]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt.", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt."}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 340.7519588867187, "r": 500.133, "b": 301.66995888671863, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1405]}], "orig": "Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit", "text": "Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit"}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 393.596, "t": 465.41395888671866, "r": 456.543, "b": 402.4009588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 166]}], "orig": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien", "text": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 45.7158088684082, "t": 746.275505065918, "r": 232.10560607910156, "b": 606.0460052490234, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/6"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 470.774658203125, "t": 802.0960960388184, "r": 524.6251831054688, "b": 750.8910522460938, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 279.2405700683594, "t": 743.0929183959961, "r": 524.8675537109375, "b": 637.3654937744141, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 45.31068801879883, "t": 502.1124267578125, "r": 525.0125122070312, "b": 414.33404541015625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 2, "bbox": {"l": 470.8011474609375, "t": 802.015796661377, "r": 524.8745727539062, "b": 750.3485336303711, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 279.2405700683594, "t": 743.0929183959961, "r": 524.8675537109375, "b": 637.3654937744141, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 282.476, "t": 108.56140000000005, "r": 432.293, "b": 115.32440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Verkehrsgünstige Lage in der \"Cargo City Süd\"", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 118.28140000000008, "r": 442.216, "b": 125.04440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ A5 und \"Frankfurter Kreuz\" in unmittelbarer Nähe", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 128.0014000000001, "r": 422.458, "b": 134.76440000000002, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Frankfurter Flughafen in ca. 11 Fahrminuten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 137.72140000000013, "r": 415.375, "b": 144.48440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ FFM Hauptbahnhof in ca. 20 Fahrminuten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 147.44140000000004, "r": 462.032, "b": 154.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Reichhaltiges gastronomisches Angebot direkt am Objekt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 157.16140000000007, "r": 431.545, "b": 163.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ FRAPORT Airport Shuttle in unmittelbarer Nähe", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 166.8814000000001, "r": 416.703, "b": 173.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Sicherheitsbereich Frankfurt Int. Flughafen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 98.72940000000006, "r": 298.292, "b": 105.00440000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Lage", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 8, "num_cols": 1, "grid": [[{"bbox": {"l": 282.476, "t": 98.72940000000006, "r": 298.292, "b": 105.00440000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Lage", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 108.56140000000005, "r": 432.293, "b": 115.32440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Verkehrsgünstige Lage in der \"Cargo City Süd\"", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 118.28140000000008, "r": 442.216, "b": 125.04440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ A5 und \"Frankfurter Kreuz\" in unmittelbarer Nähe", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 128.0014000000001, "r": 422.458, "b": 134.76440000000002, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Frankfurter Flughafen in ca. 11 Fahrminuten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 137.72140000000013, "r": 415.375, "b": 144.48440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ FFM Hauptbahnhof in ca. 20 Fahrminuten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 147.44140000000004, "r": 462.032, "b": 154.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Reichhaltiges gastronomisches Angebot direkt am Objekt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 157.16140000000007, "r": 431.545, "b": 163.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ FRAPORT Airport Shuttle in unmittelbarer Nähe", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 166.8814000000001, "r": 416.703, "b": 173.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Sicherheitsbereich Frankfurt Int. Flughafen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.22003936767578, "t": 591.3751525878906, "r": 525.04345703125, "b": 510.7151184082031, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 333.915, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Direktinvestment", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 310.041, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.122,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 328.032, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1999 (-) / 2000", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.242.900 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.476, "t": 310.7284000000001, "r": 276.303, "b": 327.0094, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Si<PERSON> bitte den", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.116, "t": 319.9094000000001, "r": 322.37, "b": 324.4834000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Erbbaugrundstück", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 332.112, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.925.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 294.643, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 7, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 310.041, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.122,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 328.032, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1999 (-) / 2000", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 333.915, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Direktinvestment", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 332.112, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.925.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 294.643, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.242.900 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.476, "t": 310.7284000000001, "r": 276.303, "b": 327.0094, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Si<PERSON> bitte den", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.116, "t": 319.9094000000001, "r": 322.37, "b": 324.4834000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Erbbaugrundstück", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.50849914550781, "t": 398.98541259765625, "r": 275.6719665527344, "b": 295.2708740234375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 455.3694000000001, "r": 121.483, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 455.3694000000001, "r": 155.563, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 455.3694000000001, "r": 273.403, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 464.60940000000005, "r": 121.483, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 464.60940000000005, "r": 155.563, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 464.60940000000005, "r": 273.403, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 473.84940000000006, "r": 121.483, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 473.84940000000006, "r": 155.563, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 473.84940000000006, "r": 273.403, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 483.08940000000007, "r": 121.483, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 483.08940000000007, "r": 155.563, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 483.08940000000007, "r": 273.403, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 492.3294000000001, "r": 121.483, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 492.3294000000001, "r": 155.563, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 492.3294000000001, "r": 273.403, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 501.5694000000001, "r": 121.483, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 501.5694000000001, "r": 155.563, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 501.5694000000001, "r": 273.403, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 510.8094000000001, "r": 121.483, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 510.8094000000001, "r": 155.563, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 510.8094000000001, "r": 273.403, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 520.0494000000001, "r": 121.483, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 520.0494000000001, "r": 155.563, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 520.0494000000001, "r": 273.403, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 529.2894000000001, "r": 121.483, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 529.2894000000001, "r": 155.563, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 529.2894000000001, "r": 273.403, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 538.5294000000001, "r": 121.483, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 538.5294000000001, "r": 155.563, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 538.5294000000001, "r": 273.403, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 538.0494000000001, "r": 81.787, "b": 544.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "unbefristet", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 11, "num_cols": 4, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 455.3694000000001, "r": 121.483, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 455.3694000000001, "r": 155.563, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 455.3694000000001, "r": 273.403, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 464.60940000000005, "r": 121.483, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 464.60940000000005, "r": 155.563, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 464.60940000000005, "r": 273.403, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 473.84940000000006, "r": 121.483, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 473.84940000000006, "r": 155.563, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 473.84940000000006, "r": 273.403, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 483.08940000000007, "r": 121.483, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 483.08940000000007, "r": 155.563, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 483.08940000000007, "r": 273.403, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 492.3294000000001, "r": 121.483, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 492.3294000000001, "r": 155.563, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 492.3294000000001, "r": 273.403, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 501.5694000000001, "r": 121.483, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 501.5694000000001, "r": 155.563, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 501.5694000000001, "r": 273.403, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 510.8094000000001, "r": 121.483, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 510.8094000000001, "r": 155.563, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 510.8094000000001, "r": 273.403, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 520.0494000000001, "r": 121.483, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 520.0494000000001, "r": 155.563, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 520.0494000000001, "r": 273.403, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 529.2894000000001, "r": 121.483, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 529.2894000000001, "r": 155.563, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 529.2894000000001, "r": 273.403, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 538.0494000000001, "r": 81.787, "b": 544.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "unbefristet", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 109.316, "t": 538.5294000000001, "r": 121.483, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 143.396, "t": 538.5294000000001, "r": 155.563, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 261.236, "t": 538.5294000000001, "r": 273.403, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 279.9635009765625, "t": 324.481201171875, "r": 524.8435668945312, "b": 276.41058349609375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 282.476, "t": 520.0494000000001, "r": 326.256, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.479, "t": 520.0494000000001, "r": 429.272, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.278, "t": 520.0494000000001, "r": 515.515, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 529.2894000000001, "r": 314.45, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.473, "t": 529.2894000000001, "r": 429.203, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.627", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.554, "t": 529.2894000000001, "r": 515.507, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 538.5294000000001, "r": 331.512, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.473, "t": 538.5294000000001, "r": 429.203, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "8.521", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.511, "t": 538.5294000000001, "r": 515.507, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "89%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 547.7694000000001, "r": 429.206, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.106", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.516, "t": 547.7694000000001, "r": 515.512, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "11%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 417.116, "t": 557.2494000000002, "r": 429.283, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 5, "num_cols": 3, "grid": [[{"bbox": {"l": 282.476, "t": 520.0494000000001, "r": 326.256, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.479, "t": 520.0494000000001, "r": 429.272, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.278, "t": 520.0494000000001, "r": 515.515, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 529.2894000000001, "r": 314.45, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.473, "t": 529.2894000000001, "r": 429.203, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.627", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.554, "t": 529.2894000000001, "r": 515.507, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 538.5294000000001, "r": 331.512, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.473, "t": 538.5294000000001, "r": 429.203, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "8.521", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.511, "t": 538.5294000000001, "r": 515.507, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "89%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 547.7694000000001, "r": 429.206, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.106", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.516, "t": 547.7694000000001, "r": 515.512, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "11%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 417.116, "t": 557.2494000000002, "r": 429.283, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "k.A.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.941925048828125, "t": 702.2834930419922, "r": 525.4318237304688, "b": 670.6060943603516, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 110.719, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ BREEAM DE: good", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 204.296, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): 312,6", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 154.096, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: D", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 3, "num_cols": 1, "grid": [[{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 110.719, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ BREEAM DE: good", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 204.296, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): 312,6", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 154.096, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: D", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.443519592285156, "t": 657.6009063720703, "r": 525.254638671875, "b": 472.8477478027344, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 197.96940000000006, "r": 391.12, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 373.316, "t": 207.44940000000008, "r": 391.118, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "59 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 187.88940000000002, "r": 390.686, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.627", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 255.8094000000001, "r": 391.039, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.429 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 265.5304000000001, "r": 117.433, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 265.5304000000001, "r": 391.039, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "52.263 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.196, "t": 275.24940000000004, "r": 391.04, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49.716 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.436, "t": 226.64940000000013, "r": 391.003, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "36,08 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.242.900 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.196, "t": 246.08940000000007, "r": 391.04, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.429 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 313.0494000000001, "r": 390.672, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "13.550.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 322.6494000000001, "r": 95.253, "b": 328.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 322.2894000000001, "r": 390.672, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.925.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12,03", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 284.96940000000006, "r": 391.036, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "126.837 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.116.063 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 342.596, "t": 303.8094000000001, "r": 390.676, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "-1.240.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10,81", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 367.436, "t": 350.0094000000001, "r": 390.71, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "-9,23%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 348.956, "t": 359.4894000000001, "r": 390.677, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.395,03 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 19, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 187.88940000000002, "r": 390.686, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.627", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 197.96940000000006, "r": 391.12, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 373.316, "t": 207.44940000000008, "r": 391.118, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "59 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.436, "t": 226.64940000000013, "r": 391.003, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "36,08 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.242.900 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.196, "t": 246.08940000000007, "r": 391.04, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.429 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 255.8094000000001, "r": 391.039, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.429 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 265.5304000000001, "r": 117.433, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 265.5304000000001, "r": 391.039, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "52.263 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.196, "t": 275.24940000000004, "r": 391.04, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49.716 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 284.96940000000006, "r": 391.036, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "126.837 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.116.063 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 342.596, "t": 303.8094000000001, "r": 390.676, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "-1.240.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 313.0494000000001, "r": 390.672, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "13.550.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 322.6494000000001, "r": 95.253, "b": 328.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 322.2894000000001, "r": 390.672, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12.925.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "12,03", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10,81", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 367.436, "t": 350.0094000000001, "r": 390.71, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "-9,23%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 348.956, "t": 359.4894000000001, "r": 390.677, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.395,03 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 1}, "2": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 2}}}