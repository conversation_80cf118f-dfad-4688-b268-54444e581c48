{"metadata": {"total_failures": 26, "timestamp": "2025-09-23 12:12:19"}, "failures": [{"document_folder": "130_Objekt_130", "reasoning": "No match found. Extracted: name='<PERSON>', address='10 Bressenden Place, London'", "pdf_path": "documents/130_Objekt_130/130_Objekt_130.pdf"}, {"document_folder": "120_Objekt_120", "reasoning": "No match found. Extracted: name='Oosterdokseiland Lose 5b+6', address='Lose 5b+6, Amsterdam'", "pdf_path": "documents/120_Objekt_120/120_Objekt_120.pdf"}, {"document_folder": "59_<PERSON><PERSON><PERSON><PERSON>_59", "reasoning": "No match found. Extracted: name='Gelrestraat 16', address='Gelrestraat 16, Amsterdam'", "pdf_path": "documents/59_Objekt_59/59_Objekt_59.pdf"}, {"document_folder": "97_Objekt_97", "reasoning": "No match found. Extracted: name='None', address='54, Boulevard Haussmann, Paris'", "pdf_path": "documents/97_Objekt_97/97_Objekt_97.pdf"}, {"document_folder": "129_Objekt_129", "reasoning": "No match found. Extracted: name='St James's Square', address='St James's Square, London'", "pdf_path": "documents/129_Objekt_129/129_Objekt_129.pdf"}, {"document_folder": "105_Objekt_105", "reasoning": "No match found. Extracted: name='\"<PERSON><PERSON><PERSON><PERSON>\"', address='Via Orefici 13, Mailand'", "pdf_path": "documents/105_Objekt_105/105_Objekt_105.pdf"}, {"document_folder": "122_Objekt_122", "reasoning": "No match found. Extracted: name='Tower 185', address='<PERSON><PERSON> 35-37, Frankfurt am Main'", "pdf_path": "documents/122_Objekt_122/122_Objekt_122.pdf"}, {"document_folder": "67_Ob<PERSON><PERSON>_67", "reasoning": "No match found. Extracted: name='None', address='Calle Conde de Gondomar 13, Cordoba'", "pdf_path": "documents/67_Objekt_67/67_Objekt_67.pdf"}, {"document_folder": "37_<PERSON><PERSON><PERSON><PERSON>_37", "reasoning": "No match found. Extracted: name='\"Arnulfpark\"', address='Arnulfstraße 59, München'", "pdf_path": "documents/37_Objekt_37/37_Objekt_37.pdf"}, {"document_folder": "123_Objekt_123", "reasoning": "No match found. Extracted: name='None', address='Route des Acacias 60, Genf'", "pdf_path": "documents/123_Objekt_123/123_Objekt_123.pdf"}, {"document_folder": "110_Objekt_110", "reasoning": "No match found. Extracted: name='None', address='None, Mailand'", "pdf_path": "documents/110_Objekt_110/110_Objekt_110.pdf"}, {"document_folder": "113_Objekt_113", "reasoning": "No match found. Extracted: name='<PERSON><PERSON><PERSON><PERSON> 113', address='38, Avenue John F. Kennedy, Luxemburg'", "pdf_path": "documents/113_Objekt_113/113_Objekt_113.pdf"}, {"document_folder": "72_Objekt_72", "reasoning": "No match found. Extracted: name='72_Objekt_72', address='35, Rue de la Gare, Paris'", "pdf_path": "documents/72_Objekt_72/72_Objekt_72.pdf"}, {"document_folder": "33_<PERSON>bjekt_33", "reasoning": "No match found. Extracted: name='FLZ - Fracht- und Logistik Zentrum', address='Cargo City Süd Gebäude 558, Frankfurt am Main'", "pdf_path": "documents/33_Objekt_33/33_Objekt_33.pdf"}, {"document_folder": "13_Objekt_13", "reasoning": "No match found. Extracted: name='Prime <PERSON>, <PERSON><PERSON><PERSON>  A1-A5', address='Am <PERSON> 4-12, <PERSON><PERSON><PERSON>'", "pdf_path": "documents/13_Objekt_13/13_Objekt_13.pdf"}, {"document_folder": "60_Ob<PERSON><PERSON>_60", "reasoning": "No match found. Extracted: name='Rond-<PERSON> Schuman', address='Avenue d`Auderghem 2-14, Brüssel'", "pdf_path": "documents/60_Objekt_60/60_Objekt_60.pdf"}, {"document_folder": "108_Objekt_108", "reasoning": "No match found. Extracted: name='None', address='Strada Buffalora 31, Magenta'", "pdf_path": "documents/108_Objekt_108/108_Objekt_108.pdf"}, {"document_folder": "61_<PERSON><PERSON><PERSON><PERSON>_61", "reasoning": "No match found. Extracted: name='<PERSON>', address='Opernring 13-15 / Elisabethenstraße 12,14, Wien'", "pdf_path": "documents/61_Objekt_61/61_Objekt_61.pdf"}, {"document_folder": "66_Obje<PERSON>_66", "reasoning": "No match found. Extracted: name='<PERSON><PERSON><PERSON><PERSON>', address='Puerta del Sol, Madrid'", "pdf_path": "documents/66_Objekt_66/66_Objekt_66.pdf"}, {"document_folder": "34_<PERSON>bjekt_34", "reasoning": "No match found. Extracted: name='Deka-ImmobilienEuropa', address='Große Elbstraße 14 / Buttstraße 3, Hamburg'", "pdf_path": "documents/34_Objekt_34/34_Objekt_34.pdf"}, {"document_folder": "112_Objekt_112", "reasoning": "No match found. Extracted: name='None', address='Via Broletto 16, Mailand'", "pdf_path": "documents/112_Objekt_112/112_Objekt_112.pdf"}, {"document_folder": "32_Objekt_32", "reasoning": "No match found. Extracted: name=''FLZ - Fracht- und Logistik Zentrum'', address='Cargo City Süd Gebäude 558, Frankfurt am Main'", "pdf_path": "documents/32_Objekt_32/32_Objekt_32.pdf"}, {"document_folder": "83_Objekt_83", "reasoning": "No match found. Extracted: name=''Atria One &amp; Two'', address='144 Morrison Street, Edinburgh'", "pdf_path": "documents/83_Objekt_83/83_Objekt_83.pdf"}, {"document_folder": "75_Objekt_75", "reasoning": "No match found. Extracted: name='None', address='Utoquai 29/31, Zürich'", "pdf_path": "documents/75_Objekt_75/75_Objekt_75.pdf"}, {"document_folder": "73_<PERSON><PERSON><PERSON><PERSON>_73", "reasoning": "No match found. Extracted: name='None', address='136-140 Avenue Charles de Gaulle / 1, Rue des Huissiers 1-3, Rue des Poissonniers, Neuilly-sur-Seine'", "pdf_path": "documents/73_Objekt_73/73_Objekt_73.pdf"}, {"document_folder": "68_Objekt_68", "reasoning": "No match found. Extracted: name='None', address='Calle Pelayo 58, Barcelona'", "pdf_path": "documents/68_Objekt_68/68_Objekt_68.pdf"}]}