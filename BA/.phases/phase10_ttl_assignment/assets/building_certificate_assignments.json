{"metadata": {"total_documents": 137, "successful_assignments": 111, "failed_assignments": 26, "success_rate": 81.02189781021897, "processing_time_seconds": 517.770341873169, "timestamp": "2025-09-23 12:12:19"}, "assignments": [{"document_folder": "30_Objekt_30", "building_id": "986ee284-f90a-4d68-a0a1-7294fe92de88", "building_name": "Logistikzentrum Rhein-Ruhr", "building_code": "1000000030", "match_type": "name", "confidence": 0.9285714285714286, "reasoning": "Name match: ''Logistikzentrum Rhein-Ruhr'' -> 'Logistikzentrum Rhein-Ruhr' (confidence: 0.93)", "certificate_info": {"pdf_path": "documents/30_Objekt_30/30_Objekt_30.pdf", "markdown_path": "documents/30_Objekt_30/extracted_content.md", "json_path": "documents/30_Objekt_30/extracted_content.json"}}, {"document_folder": "02_Objekt_2", "building_id": "10d7c8c3-bc2f-4166-97d7-d11f04a39d82", "building_name": "Checkpoint Charlie", "building_code": "1000000002", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Checkpoint <PERSON>' -> 'Checkpoint <PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/02_Objekt_2/02_Objekt_2.pdf", "markdown_path": "documents/02_Objekt_2/extracted_content.md", "json_path": "documents/02_Objekt_2/extracted_content.json"}}, {"document_folder": "124_Objekt_124", "building_id": "7310956d-6d8f-4bd7-af7b-4943aae4c2a4", "building_name": "Gemini", "building_code": "1000000124", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON>' -> '<PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/124_Objekt_124/124_Objekt_124.pdf", "markdown_path": "documents/124_Objekt_124/extracted_content.md", "json_path": "documents/124_Objekt_124/extracted_content.json"}}, {"document_folder": "78_Objekt_78", "building_id": "1ba5c96e-d666-4981-8962-b62d195f4eca", "building_name": "One Southampton Row", "building_code": "1000000078", "match_type": "name", "confidence": 0.8142857142857143, "reasoning": "Name match: ''One Southampton Row'' -> 'One Southampton Row' (confidence: 0.90)", "certificate_info": {"pdf_path": "documents/78_Objekt_78/78_Objekt_78.pdf", "markdown_path": "documents/78_Objekt_78/extracted_content.md", "json_path": "documents/78_Objekt_78/extracted_content.json"}}, {"document_folder": "12_Objekt_12", "building_id": "8995106d-e483-4266-a3c7-65120a6f3bbe", "building_name": "Fleethof", "building_code": "1000000012", "match_type": "name", "confidence": 0.****************, "reasoning": "Name match: ''Fleethof'' -> 'Fleethof' (confidence: 0.80)", "certificate_info": {"pdf_path": "documents/12_Objekt_12/12_Objekt_12.pdf", "markdown_path": "documents/12_Objekt_12/extracted_content.md", "json_path": "documents/12_Objekt_12/extracted_content.json"}}, {"document_folder": "126_Objekt_126", "building_id": "8e8d2928-9d12-4b2a-8e7e-5136c6dd736a", "building_name": "City Green Court", "building_code": "1000000126", "match_type": "name", "confidence": 0.7999999999999999, "reasoning": "Name match: ''City Green Court'' -> 'City Green Court' (confidence: 0.89)", "certificate_info": {"pdf_path": "documents/126_Objekt_126/126_Objekt_126.pdf", "markdown_path": "documents/126_Objekt_126/extracted_content.md", "json_path": "documents/126_Objekt_126/extracted_content.json"}}, {"document_folder": "35_Objekt_35", "building_id": "3c04623f-e668-40e8-81a8-1945d6e577d8", "building_name": "Logistikzentrum Bremen", "building_code": "1000000035", "match_type": "name", "confidence": 0.825, "reasoning": "Name match: ''Logistikzentrum Bremen'' -> 'Logistikzentrum Bremen' (confidence: 0.92)", "certificate_info": {"pdf_path": "documents/35_Objekt_35/35_Objekt_35.pdf", "markdown_path": "documents/35_Objekt_35/extracted_content.md", "json_path": "documents/35_Objekt_35/extracted_content.json"}}, {"document_folder": "36_Obje<PERSON>_36", "building_id": "42ee10cc-3dfa-496b-b165-8deeb57f63ea", "building_name": "Hohe Bleichen 11", "building_code": "1000000036", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON> Bleichen 11' -> '<PERSON><PERSON> Bleichen 11' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/36_Objekt_36/36_Objekt_36.pdf", "markdown_path": "documents/36_Objekt_36/extracted_content.md", "json_path": "documents/36_Objekt_36/extracted_content.json"}}, {"document_folder": "111_Objekt_111", "building_id": "adbd1c9e-49b4-4f4d-8155-6329f72b6b6d", "building_name": "MAC567", "building_code": "1000000111", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'MAC567' -> 'MAC567' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/111_Objekt_111/111_Objekt_111.pdf", "markdown_path": "documents/111_Objekt_111/extracted_content.md", "json_path": "documents/111_Objekt_111/extracted_content.json"}}, {"document_folder": "91_Objekt_91", "building_id": "58929013-f385-45cc-83e6-bbb1b329818f", "building_name": "The One", "building_code": "1000000091", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'The One' -> 'The One' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/91_Objekt_91/91_Objekt_91.pdf", "markdown_path": "documents/91_Objekt_91/extracted_content.md", "json_path": "documents/91_Objekt_91/extracted_content.json"}}, {"document_folder": "102_Objekt_102", "building_id": "fb1f443f-4478-4044-a8e7-d551d75802d1", "building_name": "33 Rue La Fayette", "building_code": "1000000102", "match_type": "address", "confidence": 0.7999999999999999, "reasoning": "Address match: '33, Rue La Fayette, Paris' -> '{'street': 'Rue La Fayette', 'house_number': '33', 'city': 'Paris', 'postal_code': '75009'}' (confidence: 0.89)", "certificate_info": {"pdf_path": "documents/102_Objekt_102/102_Objekt_102.pdf", "markdown_path": "documents/102_Objekt_102/extracted_content.md", "json_path": "documents/102_Objekt_102/extracted_content.json"}}, {"document_folder": "134_Objekt_134", "building_id": "bb24716d-c0d1-4772-96f7-0a69002d759f", "building_name": "Andersia Tower", "building_code": "1000000134", "match_type": "name", "confidence": 0.7875, "reasoning": "Name match: ''Andersia Tower'' -> 'Andersia Tower' (confidence: 0.88)", "certificate_info": {"pdf_path": "documents/134_Objekt_134/134_Objekt_134.pdf", "markdown_path": "documents/134_Objekt_134/extracted_content.md", "json_path": "documents/134_Objekt_134/extracted_content.json"}}, {"document_folder": "116_Objekt_116", "building_id": "7b46d5e9-cb1b-4897-bb20-14c4110629ab", "building_name": "Whitewater", "building_code": "1000000116", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Whitewater' -> 'Whitewater' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/116_Objekt_116/116_Objekt_116.pdf", "markdown_path": "documents/116_Objekt_116/extracted_content.md", "json_path": "documents/116_Objekt_116/extracted_content.json"}}, {"document_folder": "71_<PERSON><PERSON><PERSON><PERSON>_71", "building_id": "832cb406-5337-4132-81b6-74298751d028", "building_name": "Le Centorial", "building_code": "1000000071", "match_type": "name", "confidence": 0.7714285714285714, "reasoning": "Name match: ''<PERSON> Centorial'' -> 'Le Centorial' (confidence: 0.86)", "certificate_info": {"pdf_path": "documents/71_Objekt_71/71_Objekt_71.pdf", "markdown_path": "documents/71_Objekt_71/extracted_content.md", "json_path": "documents/71_Objekt_71/extracted_content.json"}}, {"document_folder": "20_Objekt_20", "building_id": "4f72a0ac-ef2f-4e33-aa1b-c34572988b6a", "building_name": "Isenburg-Zentrum", "building_code": "1000000020", "match_type": "name", "confidence": 0.7999999999999999, "reasoning": "Name match: ''Isenburg-Zentrum'' -> 'Isenburg-Zentrum' (confidence: 0.89)", "certificate_info": {"pdf_path": "documents/20_Objekt_20/20_Objekt_20.pdf", "markdown_path": "documents/20_Objekt_20/extracted_content.md", "json_path": "documents/20_Objekt_20/extracted_content.json"}}, {"document_folder": "85_Objekt_85", "building_id": "2e503f44-0591-486c-b333-f326ed980a21", "building_name": "Bema Plaza", "building_code": "1000000085", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Bema Plaza' -> 'Bema Plaza' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/85_Objekt_85/85_Objekt_85.pdf", "markdown_path": "documents/85_Objekt_85/extracted_content.md", "json_path": "documents/85_Objekt_85/extracted_content.json"}}, {"document_folder": "90_Objekt_90", "building_id": "6edcfd7b-47ed-46f1-9a91-c3e99a082b92", "building_name": "<PERSON><PERSON><PERSON>", "building_code": "1000000090", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON><PERSON>' -> '<PERSON><PERSON><PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/90_Objekt_90/90_Objekt_90.pdf", "markdown_path": "documents/90_Objekt_90/extracted_content.md", "json_path": "documents/90_Objekt_90/extracted_content.json"}}, {"document_folder": "22_Objekt_22", "building_id": "a2e0b5f8-b290-440f-bdfb-4861730dc59a", "building_name": "Westend Sky", "building_code": "1000000022", "match_type": "name", "confidence": 0.7615384615384615, "reasoning": "Name match: ''Westend Sky'' -> 'Westend Sky' (confidence: 0.85)", "certificate_info": {"pdf_path": "documents/22_Objekt_22/22_Objekt_22.pdf", "markdown_path": "documents/22_Objekt_22/extracted_content.md", "json_path": "documents/22_Objekt_22/extracted_content.json"}}, {"document_folder": "51_Objekt_51", "building_id": "46423443-e41a-487d-a07a-c7a9fd5e1ff4", "building_name": "CUBES", "building_code": "1000000051", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'CUBES' -> 'CUBES' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/51_Objekt_51/51_Objekt_51.pdf", "markdown_path": "documents/51_Objekt_51/extracted_content.md", "json_path": "documents/51_Objekt_51/extracted_content.json"}}, {"document_folder": "96_Objekt_96", "building_id": "b2912904-1a1e-4f3e-8a85-cc98d196c385", "building_name": "EQWATER", "building_code": "1000000096", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'EQWATER' -> 'EQWATER' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/96_Objekt_96/96_Objekt_96.pdf", "markdown_path": "documents/96_Objekt_96/extracted_content.md", "json_path": "documents/96_Objekt_96/extracted_content.json"}}, {"document_folder": "118_Objekt_118", "building_id": "a5271b50-54b9-4517-af00-c7c2d163a1f4", "building_name": "Clayton Hotel Charlemont", "building_code": "1000000118", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Clayton Hotel Charlemont' -> 'Clayton Hotel Charlemont' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/118_Objekt_118/118_Objekt_118.pdf", "markdown_path": "documents/118_Objekt_118/extracted_content.md", "json_path": "documents/118_Objekt_118/extracted_content.json"}}, {"document_folder": "07_Objekt_7", "building_id": "57cd12c2-f87c-48cf-be65-66574c335774", "building_name": "s´Zentrum", "building_code": "1000000007", "match_type": "name", "confidence": 0.7363636363636364, "reasoning": "Name match: ''s´Z<PERSON><PERSON>'' -> 's´Zentrum' (confidence: 0.82)", "certificate_info": {"pdf_path": "documents/07_Objekt_7/07_Objekt_7.pdf", "markdown_path": "documents/07_Objekt_7/extracted_content.md", "json_path": "documents/07_Objekt_7/extracted_content.json"}}, {"document_folder": "81_<PERSON><PERSON>je<PERSON>_81", "building_id": "9be56927-0ba1-4397-b8e2-db496d7528f8", "building_name": "The St. Botolph Building", "building_code": "1000000081", "match_type": "name", "confidence": 1.0, "reasoning": "Name match: 'The St. Botolph Building' -> 'The St. Botolph Building' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/81_Objekt_81/81_Objekt_81.pdf", "markdown_path": "documents/81_Objekt_81/extracted_content.md", "json_path": "documents/81_Objekt_81/extracted_content.json"}}, {"document_folder": "04_Objekt_4", "building_id": "1f1024af-c0f0-44f6-881b-77dae01bbd6a", "building_name": "AIR CARGO Center", "building_code": "1000000004", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'AIR CARGO Center' -> 'AIR CARGO Center' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/04_Objekt_4/04_Objekt_4.pdf", "markdown_path": "documents/04_Objekt_4/extracted_content.md", "json_path": "documents/04_Objekt_4/extracted_content.json"}}, {"document_folder": "133_Objekt_133", "building_id": "cd9ae8c1-0380-4ecc-98a3-5510442e3b2c", "building_name": "Stortingsgata 6", "building_code": "1000000133", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Stortingsgata 6' -> 'Stortingsgata 6' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/133_Objekt_133/133_Objekt_133.pdf", "markdown_path": "documents/133_Objekt_133/extracted_content.md", "json_path": "documents/133_Objekt_133/extracted_content.json"}}, {"document_folder": "24_Objekt_24", "building_id": "cc4e3285-f6a0-4e9b-a204-4a70b555ab25", "building_name": "LEOMAX", "building_code": "1000000024", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'LEOMA<PERSON>' -> 'LEOMAX' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/24_Objekt_24/24_Objekt_24.pdf", "markdown_path": "documents/24_Objekt_24/extracted_content.md", "json_path": "documents/24_Objekt_24/extracted_content.json"}}, {"document_folder": "125_Objekt_125", "building_id": "654b86d9-c44b-406a-a627-fdfaa29c317d", "building_name": "Tesco Distribution Center", "building_code": "1000000125", "match_type": "name", "confidence": 0.8333333333333334, "reasoning": "Name match: ''Tesco Distribution Center'' -> 'Tesco Distribution Center' (confidence: 0.93)", "certificate_info": {"pdf_path": "documents/125_Objekt_125/125_Objekt_125.pdf", "markdown_path": "documents/125_Objekt_125/extracted_content.md", "json_path": "documents/125_Objekt_125/extracted_content.json"}}, {"document_folder": "70_Objekt_70", "building_id": "975306b0-55ed-4bec-8d1d-eb7403a01369", "building_name": "120 Rue du Faubourg Saint-Honoré", "building_code": "1000000070", "match_type": "address", "confidence": 0.7393939393939395, "reasoning": "Address match: '120, Rue du Faubourg Saint-Honoré, Paris' -> '{'street': 'Rue du Faubourg Saint-Honoré', 'house_number': '120', 'city': 'Paris', 'postal_code': '75008'}' (confidence: 0.92)", "certificate_info": {"pdf_path": "documents/70_Objekt_70/70_Objekt_70.pdf", "markdown_path": "documents/70_Objekt_70/extracted_content.md", "json_path": "documents/70_Objekt_70/extracted_content.json"}}, {"document_folder": "89_Objekt_89", "building_id": "e20a7cde-b6df-4ec1-9c21-00cb619c66a6", "building_name": "<PERSON><PERSON>", "building_code": "1000000089", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON>' -> '<PERSON><PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/89_Objekt_89/89_Objekt_89.pdf", "markdown_path": "documents/89_Objekt_89/extracted_content.md", "json_path": "documents/89_Objekt_89/extracted_content.json"}}, {"document_folder": "137_Objekt_137", "building_id": "d5ccdae6-a1b9-4d9c-8657-00aa64c5d7a8", "building_name": "IBC", "building_code": "1000000137", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'IBC' -> 'IBC' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/137_Objekt_137/137_Objekt_137.pdf", "markdown_path": "documents/137_Objekt_137/extracted_content.md", "json_path": "documents/137_Objekt_137/extracted_content.json"}}, {"document_folder": "03_Objekt_3", "building_id": "be0f3b3e-c7ed-4178-8cc4-6114ad605936", "building_name": "Waterfalls Berlin", "building_code": "1000000003", "match_type": "name", "confidence": 0.8052631578947369, "reasoning": "Name match: ''Waterfalls Berlin'' -> 'Waterfalls Berlin' (confidence: 0.89)", "certificate_info": {"pdf_path": "documents/03_Objekt_3/03_Objekt_3.pdf", "markdown_path": "documents/03_Objekt_3/extracted_content.md", "json_path": "documents/03_Objekt_3/extracted_content.json"}}, {"document_folder": "128_Objekt_128", "building_id": "40c031cd-6838-44e2-bd5f-8509fb2ff226", "building_name": "CTPark Prague North 2", "building_code": "1000000128", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'CTPark Prague North 2' -> 'CTPark Prague North 2' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/128_Objekt_128/128_Objekt_128.pdf", "markdown_path": "documents/128_Objekt_128/extracted_content.md", "json_path": "documents/128_Objekt_128/extracted_content.json"}}, {"document_folder": "62_Obje<PERSON>_62", "building_id": "cccc8903-cbd3-4636-b0ea-992c16c004d3", "building_name": "Business Center Muthgasse", "building_code": "1000000062", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Business Center Muthgasse' -> 'Business Center Muthgasse' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/62_Objekt_62/62_Objekt_62.pdf", "markdown_path": "documents/62_Objekt_62/extracted_content.md", "json_path": "documents/62_Objekt_62/extracted_content.json"}}, {"document_folder": "19_Objekt_19", "building_id": "da857556-b790-4f2c-b193-523c29501fe1", "building_name": "Novum", "building_code": "1000000019", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Novum' -> 'Novum' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/19_Objekt_19/19_Objekt_19.pdf", "markdown_path": "documents/19_Objekt_19/extracted_content.md", "json_path": "documents/19_Objekt_19/extracted_content.json"}}, {"document_folder": "115_Objekt_115", "building_id": "ea27f06d-b7a6-43e5-aee0-8ef57cb35279", "building_name": "Emporium", "building_code": "1000000115", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Emporium' -> 'Emporium' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/115_Objekt_115/115_Objekt_115.pdf", "markdown_path": "documents/115_Objekt_115/extracted_content.md", "json_path": "documents/115_Objekt_115/extracted_content.json"}}, {"document_folder": "80_Objekt_80", "building_id": "66ef33d4-3f94-4a03-8f2e-6d9ec801ae53", "building_name": "Aldermanbury Square", "building_code": "1000000080", "match_type": "name", "confidence": 0.8142857142857143, "reasoning": "Name match: ''Aldermanbury Square'' -> 'Aldermanbury Square' (confidence: 0.90)", "certificate_info": {"pdf_path": "documents/80_Objekt_80/80_Objekt_80.pdf", "markdown_path": "documents/80_Objekt_80/extracted_content.md", "json_path": "documents/80_Objekt_80/extracted_content.json"}}, {"document_folder": "101_Objekt_101", "building_id": "cbde8030-0835-401a-81bb-239aa0923056", "building_name": "Solstys", "building_code": "1000000101", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON><PERSON>' -> '<PERSON>sty<PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/101_Objekt_101/101_Objekt_101.pdf", "markdown_path": "documents/101_Objekt_101/extracted_content.md", "json_path": "documents/101_Objekt_101/extracted_content.json"}}, {"document_folder": "28_Obje<PERSON>_28", "building_id": "10bd9e8d-dc9f-4cd9-a131-045a9c596498", "building_name": "Alpha-Haus", "building_code": "1000000028", "match_type": "name", "confidence": 0.75, "reasoning": "Name match: ''<PERSON><PERSON><PERSON><PERSON>'' -> '<PERSON><PERSON><PERSON><PERSON>' (confidence: 0.83)", "certificate_info": {"pdf_path": "documents/28_Objekt_28/28_Objekt_28.pdf", "markdown_path": "documents/28_Objekt_28/extracted_content.md", "json_path": "documents/28_Objekt_28/extracted_content.json"}}, {"document_folder": "46_Objekt_46", "building_id": "2c5ac2b6-7887-4b38-9203-c94df0e68b67", "building_name": "Mönchhof Frankfurt Airport", "building_code": "1000000046", "match_type": "name", "confidence": 0.8357142857142857, "reasoning": "Name match: ''Mönchhof Frankfurt Airport'' -> 'Mönchhof Frankfurt Airport' (confidence: 0.93)", "certificate_info": {"pdf_path": "documents/46_Objekt_46/46_Objekt_46.pdf", "markdown_path": "documents/46_Objekt_46/extracted_content.md", "json_path": "documents/46_Objekt_46/extracted_content.json"}}, {"document_folder": "109_Objekt_109", "building_id": "a02777cc-ae87-43f5-8a32-87d522d24d00", "building_name": "Logistics Centre B", "building_code": "1000000109", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Logistics Centre B' -> 'Logistics Centre B' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/109_Objekt_109/109_Objekt_109.pdf", "markdown_path": "documents/109_Objekt_109/extracted_content.md", "json_path": "documents/109_Objekt_109/extracted_content.json"}}, {"document_folder": "92_Objekt_92", "building_id": "0b8245dd-551c-45a4-bfc4-473e89ccacb5", "building_name": "Spectrum", "building_code": "1000000092", "match_type": "name", "confidence": 0.****************, "reasoning": "Name match: ''Spectrum'' -> 'Spectrum' (confidence: 0.80)", "certificate_info": {"pdf_path": "documents/92_Objekt_92/92_Objekt_92.pdf", "markdown_path": "documents/92_Objekt_92/extracted_content.md", "json_path": "documents/92_Objekt_92/extracted_content.json"}}, {"document_folder": "27_Ob<PERSON><PERSON>_27", "building_id": "899d7524-f048-4006-aeb8-7fc0410c05b4", "building_name": "<PERSON><PERSON>", "building_code": "1000000027", "match_type": "name", "confidence": 0.7000000000000001, "reasoning": "Name match: '<PERSON><PERSON>'s' -> '<PERSON><PERSON>' (confidence: 0.78)", "certificate_info": {"pdf_path": "documents/27_Objekt_27/27_Objekt_27.pdf", "markdown_path": "documents/27_Objekt_27/extracted_content.md", "json_path": "documents/27_Objekt_27/extracted_content.json"}}, {"document_folder": "10_Objekt_10", "building_id": "65c6d94e-ba3f-4c7e-8c49-942e1712cf1e", "building_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "building_code": "1000000010", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' -> '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/10_Objekt_10/10_Objekt_10.pdf", "markdown_path": "documents/10_Objekt_10/extracted_content.md", "json_path": "documents/10_Objekt_10/extracted_content.json"}}, {"document_folder": "79_<PERSON>bjekt_79", "building_id": "96c6d1fb-8146-42fc-bbf1-5ac0f10926c7", "building_name": "Palestra", "building_code": "**********", "match_type": "name", "confidence": 0.****************, "reasoning": "Name match: ''<PERSON><PERSON><PERSON>'' -> '<PERSON><PERSON><PERSON>' (confidence: 0.80)", "certificate_info": {"pdf_path": "documents/79_Objekt_79/79_Objekt_79.pdf", "markdown_path": "documents/79_Objekt_79/extracted_content.md", "json_path": "documents/79_Objekt_79/extracted_content.json"}}, {"document_folder": "54_Objekt_54", "building_id": "b4d50434-46d9-4bd1-b7fd-c9336e157401", "building_name": "The Bank", "building_code": "**********", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'The Bank' -> 'The Bank' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/54_Objekt_54/54_Objekt_54.pdf", "markdown_path": "documents/54_Objekt_54/extracted_content.md", "json_path": "documents/54_Objekt_54/extracted_content.json"}}, {"document_folder": "82_Objekt_82", "building_id": "b5f9f964-0241-4db9-9ca1-2fc469650560", "building_name": "Margaret Street 33", "building_code": "**********", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Margaret Street 33' -> 'Margaret Street 33' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/82_Objekt_82/82_Objekt_82.pdf", "markdown_path": "documents/82_Objekt_82/extracted_content.md", "json_path": "documents/82_Objekt_82/extracted_content.json"}}, {"document_folder": "31_<PERSON><PERSON>je<PERSON>_31", "building_id": "a3251601-d4a3-4345-ba97-c7cf47062948", "building_name": "Schönhauser Tor", "building_code": "**********", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Schönhauser Tor' -> 'Schönhauser Tor' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/31_Objekt_31/31_Objekt_31.pdf", "markdown_path": "documents/31_Objekt_31/extracted_content.md", "json_path": "documents/31_Objekt_31/extracted_content.json"}}, {"document_folder": "55_Objekt_55", "building_id": "a96f509a-1ce8-4a30-9e24-0144bd202cf2", "building_name": "Hotel NH Collection Amsterdam", "building_code": "1000000055", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Hotel NH Collection Amsterdam' -> 'Hotel NH Collection Amsterdam' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/55_Objekt_55/55_Objekt_55.pdf", "markdown_path": "documents/55_Objekt_55/extracted_content.md", "json_path": "documents/55_Objekt_55/extracted_content.json"}}, {"document_folder": "53_<PERSON>bjekt_53", "building_id": "52ca00a9-2cbe-4f54-8ea8-7b9f62c29f5d", "building_name": "Distribution Center Flextronics", "building_code": "1000000053", "match_type": "name", "confidence": 0.8454545454545456, "reasoning": "Name match: ''Distribution Center Flextronics'' -> 'Distribution Center Flextronics' (confidence: 0.94)", "certificate_info": {"pdf_path": "documents/53_Objekt_53/53_Objekt_53.pdf", "markdown_path": "documents/53_Objekt_53/extracted_content.md", "json_path": "documents/53_Objekt_53/extracted_content.json"}}, {"document_folder": "05_Objekt_5", "building_id": "1892ebb9-9532-4dee-bddc-a7044da700a2", "building_name": "Atrium Plaza", "building_code": "1000000005", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Atrium Plaza' -> 'Atrium Plaza' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/05_Objekt_5/05_Objekt_5.pdf", "markdown_path": "documents/05_Objekt_5/extracted_content.md", "json_path": "documents/05_Objekt_5/extracted_content.json"}}, {"document_folder": "119_Objekt_119", "building_id": "2042576d-5152-49bd-9785-8b8f4ee75d7d", "building_name": "Hotel St. George", "building_code": "1000000119", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Hotel St. George' -> 'Hotel St. George' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/119_Objekt_119/119_Objekt_119.pdf", "markdown_path": "documents/119_Objekt_119/extracted_content.md", "json_path": "documents/119_Objekt_119/extracted_content.json"}}, {"document_folder": "48_Objekt_48", "building_id": "4620ead9-e050-4d4c-9b7b-d8bff52936b8", "building_name": "Das Schloss Shoppingcenter", "building_code": "1000000048", "match_type": "name", "confidence": 0.8357142857142857, "reasoning": "Name match: ''Das Schloss Shoppingcenter'' -> 'Das Schloss Shoppingcenter' (confidence: 0.93)", "certificate_info": {"pdf_path": "documents/48_Objekt_48/48_Objekt_48.pdf", "markdown_path": "documents/48_Objekt_48/extracted_content.md", "json_path": "documents/48_Objekt_48/extracted_content.json"}}, {"document_folder": "121_Objekt_121", "building_id": "b0a3f8cc-829e-4fcd-bcbc-1b1db8d55bb0", "building_name": "DC Tower", "building_code": "1000000121", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'DC Tower' -> 'DC Tower' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/121_Objekt_121/121_Objekt_121.pdf", "markdown_path": "documents/121_Objekt_121/extracted_content.md", "json_path": "documents/121_Objekt_121/extracted_content.json"}}, {"document_folder": "95_Objekt_95", "building_id": "dfcf1ea1-69bb-416d-a393-1ca5cd58aa5a", "building_name": "Alta Diagonal", "building_code": "1000000095", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Alta Diagonal' -> 'Alta Diagonal' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/95_Objekt_95/95_Objekt_95.pdf", "markdown_path": "documents/95_Objekt_95/extracted_content.md", "json_path": "documents/95_Objekt_95/extracted_content.json"}}, {"document_folder": "98_Objekt_98", "building_id": "c82e04a6-352d-4b11-8fa8-1ee6d861973e", "building_name": "Central Park", "building_code": "1000000098", "match_type": "name", "confidence": 0.7714285714285714, "reasoning": "Name match: ''Central Park'' -> 'Central Park' (confidence: 0.86)", "certificate_info": {"pdf_path": "documents/98_Objekt_98/98_Objekt_98.pdf", "markdown_path": "documents/98_Objekt_98/extracted_content.md", "json_path": "documents/98_Objekt_98/extracted_content.json"}}, {"document_folder": "136_Objekt_136", "building_id": "de3e19d1-687f-4ffe-83f5-96363f23275d", "building_name": "Grzybowska Park", "building_code": "1000000136", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Grzybowska Park' -> 'Grzybowska Park' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/136_Objekt_136/136_Objekt_136.pdf", "markdown_path": "documents/136_Objekt_136/extracted_content.md", "json_path": "documents/136_Objekt_136/extracted_content.json"}}, {"document_folder": "43_Objekt_43", "building_id": "e2d82b02-76d0-4ceb-9df8-5c6293a333a6", "building_name": "<PERSON>sie", "building_code": "1000000043", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Theresie' -> 'Theresie' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/43_Objekt_43/43_Objekt_43.pdf", "markdown_path": "documents/43_Objekt_43/extracted_content.md", "json_path": "documents/43_Objekt_43/extracted_content.json"}}, {"document_folder": "09_Objekt_9", "building_id": "d3954c67-c035-4b3e-96bc-29515c31b7f5", "building_name": "Sunyard", "building_code": "1000000009", "match_type": "name", "confidence": 0.7000000000000001, "reasoning": "Name match: ''Sunyard'' -> 'Sunyard' (confidence: 0.78)", "certificate_info": {"pdf_path": "documents/09_Objekt_9/09_Objekt_9.pdf", "markdown_path": "documents/09_Objekt_9/extracted_content.md", "json_path": "documents/09_Objekt_9/extracted_content.json"}}, {"document_folder": "99_Objekt_99", "building_id": "0a8cbe14-3d18-4c6c-a778-abade5b4b8b1", "building_name": "<PERSON><PERSON><PERSON><PERSON>", "building_code": "1000000099", "match_type": "name", "confidence": 0.7615384615384615, "reasoning": "Name match: ''<PERSON><PERSON><PERSON><PERSON>'' -> '<PERSON><PERSON><PERSON><PERSON>' (confidence: 0.85)", "certificate_info": {"pdf_path": "documents/99_Objekt_99/99_Objekt_99.pdf", "markdown_path": "documents/99_Objekt_99/extracted_content.md", "json_path": "documents/99_Objekt_99/extracted_content.json"}}, {"document_folder": "104_Objekt_104", "building_id": "fdb38a15-d380-49ae-8639-cc5861edc1a3", "building_name": "Palazzo Aporti", "building_code": "1000000104", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Palazzo Aporti' -> 'Palazzo Aporti' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/104_Objekt_104/104_Objekt_104.pdf", "markdown_path": "documents/104_Objekt_104/extracted_content.md", "json_path": "documents/104_Objekt_104/extracted_content.json"}}, {"document_folder": "26_Objekt_26", "building_id": "11f9f028-6ee1-495b-9978-3e1a23d7c3fb", "building_name": "Königstraße 14", "building_code": "1000000026", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Königstraße 14' -> 'Königstraße 14' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/26_Objekt_26/26_Objekt_26.pdf", "markdown_path": "documents/26_Objekt_26/extracted_content.md", "json_path": "documents/26_Objekt_26/extracted_content.json"}}, {"document_folder": "17_Objekt_17", "building_id": "075bf17a-11cc-4609-83ed-5b172ae187f2", "building_name": "Magnusstraße 11", "building_code": "1000000017", "match_type": "name", "confidence": 0.75, "reasoning": "Name match: 'Magnusstraße 11-13' -> 'Magnusstraße 11' (confidence: 0.83)", "certificate_info": {"pdf_path": "documents/17_Objekt_17/17_Objekt_17.pdf", "markdown_path": "documents/17_Objekt_17/extracted_content.md", "json_path": "documents/17_Objekt_17/extracted_content.json"}}, {"document_folder": "42_Objekt_42", "building_id": "3942f7e3-440b-47b2-9c0a-587c5543c71e", "building_name": "Neumarkt Galerie", "building_code": "1000000042", "match_type": "name", "confidence": 0.7999999999999999, "reasoning": "Name match: ''Neumarkt Galerie'' -> 'Neumarkt Galerie' (confidence: 0.89)", "certificate_info": {"pdf_path": "documents/42_Objekt_42/42_Objekt_42.pdf", "markdown_path": "documents/42_Objekt_42/extracted_content.md", "json_path": "documents/42_Objekt_42/extracted_content.json"}}, {"document_folder": "86_Objekt_86", "building_id": "654b86d9-c44b-406a-a627-fdfaa29c317d", "building_name": "Tesco Distribution Center", "building_code": "1000000125", "match_type": "name", "confidence": 0.8333333333333334, "reasoning": "Name match: ''Tesco Distribution Center'' -> 'Tesco Distribution Center' (confidence: 0.93)", "certificate_info": {"pdf_path": "documents/86_Objekt_86/86_Objekt_86.pdf", "markdown_path": "documents/86_Objekt_86/extracted_content.md", "json_path": "documents/86_Objekt_86/extracted_content.json"}}, {"document_folder": "41_Objekt_41", "building_id": "d087f4c1-344d-401b-aff2-62dd21d74c68", "building_name": "<PERSON>", "building_code": "1000000041", "match_type": "name", "confidence": 0.81, "reasoning": "Name match: ''<PERSON>'' -> '<PERSON>' (confidence: 0.90)", "certificate_info": {"pdf_path": "documents/41_Objekt_41/41_Objekt_41.pdf", "markdown_path": "documents/41_Objekt_41/extracted_content.md", "json_path": "documents/41_Objekt_41/extracted_content.json"}}, {"document_folder": "58_Objekt_58", "building_id": "370d688f-fbac-4b18-9444-8ee31e05bb68", "building_name": "vidaXL Phase 3", "building_code": "1000000058", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'vidaXL Phase 3' -> 'vidaXL Phase 3' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/58_Objekt_58/58_Objekt_58.pdf", "markdown_path": "documents/58_Objekt_58/extracted_content.md", "json_path": "documents/58_Objekt_58/extracted_content.json"}}, {"document_folder": "56_Objekt_56", "building_id": "8e69a400-3087-4b3d-93a4-fcf0ba1dafd3", "building_name": "Cool 63", "building_code": "1000000056", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON> 63' -> '<PERSON> 63' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/56_Objekt_56/56_Objekt_56.pdf", "markdown_path": "documents/56_Objekt_56/extracted_content.md", "json_path": "documents/56_Objekt_56/extracted_content.json"}}, {"document_folder": "132_Objekt_132", "building_id": "0b3dfe7c-08fd-4139-8fc4-f99d963cd23c", "building_name": "<PERSON><PERSON><PERSON>", "building_code": "1000000132", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON><PERSON>' -> '<PERSON><PERSON><PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/132_Objekt_132/132_Objekt_132.pdf", "markdown_path": "documents/132_Objekt_132/extracted_content.md", "json_path": "documents/132_Objekt_132/extracted_content.json"}}, {"document_folder": "135_Objekt_135", "building_id": "0a2add59-d7d2-4802-ac6d-2bbe4f1a4575", "building_name": "Forum Gliwice", "building_code": "1000000135", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Forum Gliwice' -> 'Forum Gliwice' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/135_Objekt_135/135_Objekt_135.pdf", "markdown_path": "documents/135_Objekt_135/extracted_content.md", "json_path": "documents/135_Objekt_135/extracted_content.json"}}, {"document_folder": "88_Objekt_88", "building_id": "a8fa7d8c-dd49-4d11-9379-3d2d6ab2d67e", "building_name": "<PERSON><PERSON><PERSON> Tor Karlsruhe", "building_code": "1000000088", "match_type": "name", "confidence": 0.8280000000000001, "reasoning": "Name match: ''<PERSON><PERSON><PERSON>'' -> '<PERSON><PERSON><PERSON>' (confidence: 0.92)", "certificate_info": {"pdf_path": "documents/88_Objekt_88/88_Objekt_88.pdf", "markdown_path": "documents/88_Objekt_88/extracted_content.md", "json_path": "documents/88_Objekt_88/extracted_content.json"}}, {"document_folder": "47_Objekt_47", "building_id": "d54154fb-bd6f-4e1a-b2ef-c314e8ec6d4c", "building_name": "HighriseOne", "building_code": "1000000047", "match_type": "name", "confidence": 0.7615384615384615, "reasoning": "Name match: ''HighriseO<PERSON>'' -> 'HighriseOne' (confidence: 0.85)", "certificate_info": {"pdf_path": "documents/47_Objekt_47/47_Objekt_47.pdf", "markdown_path": "documents/47_Objekt_47/extracted_content.md", "json_path": "documents/47_Objekt_47/extracted_content.json"}}, {"document_folder": "06_Objekt_6", "building_id": "c7848807-75bd-4dc2-8bec-9df8c7949fe2", "building_name": "<PERSON><PERSON><PERSON>", "building_code": "1000000006", "match_type": "name", "confidence": 0.8142857142857143, "reasoning": "Name match: ''<PERSON><PERSON><PERSON>'' -> '<PERSON><PERSON><PERSON>' (confidence: 0.90)", "certificate_info": {"pdf_path": "documents/06_Objekt_6/06_Objekt_6.pdf", "markdown_path": "documents/06_Objekt_6/extracted_content.md", "json_path": "documents/06_Objekt_6/extracted_content.json"}}, {"document_folder": "49_Objekt_49", "building_id": "858414b0-9520-4d68-afac-b856688eb3d7", "building_name": "Logistikzentrum FFM-Airport, 2. BA", "building_code": "1000000049", "match_type": "name", "confidence": 0.85, "reasoning": "Name match: ''Logistikzentrum FFM-Airport, 2. BA'' -> 'Logistikzentrum FFM-Airport, 2. BA' (confidence: 0.94)", "certificate_info": {"pdf_path": "documents/49_Objekt_49/49_Objekt_49.pdf", "markdown_path": "documents/49_Objekt_49/extracted_content.md", "json_path": "documents/49_Objekt_49/extracted_content.json"}}, {"document_folder": "38_Ob<PERSON><PERSON>_38", "building_id": "99120dc1-8ee9-46c3-b465-c7e890895a76", "building_name": "<PERSON>", "building_code": "1000000038", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON>' -> '<PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/38_Objekt_38/38_Objekt_38.pdf", "markdown_path": "documents/38_Objekt_38/extracted_content.md", "json_path": "documents/38_Objekt_38/extracted_content.json"}}, {"document_folder": "94_Objekt_94", "building_id": "0058c837-25b9-4345-9cc1-ec056f456052", "building_name": "PlusZwei", "building_code": "1000000094", "match_type": "name", "confidence": 0.****************, "reasoning": "Name match: ''PlusZ<PERSON>'' -> 'PlusZwei' (confidence: 0.80)", "certificate_info": {"pdf_path": "documents/94_Objekt_94/94_Objekt_94.pdf", "markdown_path": "documents/94_Objekt_94/extracted_content.md", "json_path": "documents/94_Objekt_94/extracted_content.json"}}, {"document_folder": "106_Objekt_106", "building_id": "5b7a76cf-3c73-4553-9c30-5d3320084283", "building_name": "Logistic Park Castel San Giovanni", "building_code": "1000000106", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Logistic Park Castel San Giovanni' -> 'Logistic Park Castel San Giovanni' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/106_Objekt_106/106_Objekt_106.pdf", "markdown_path": "documents/106_Objekt_106/extracted_content.md", "json_path": "documents/106_Objekt_106/extracted_content.json"}}, {"document_folder": "69_Ob<PERSON><PERSON>_69", "building_id": "d577dc63-6bf1-4c89-8d9b-f4ee302444d0", "building_name": "Opéra-Victoire", "building_code": "1000000069", "match_type": "name", "confidence": 0.7875, "reasoning": "Name match: ''Opéra-Victoire'' -> 'Opéra-Victoire' (confidence: 0.88)", "certificate_info": {"pdf_path": "documents/69_Objekt_69/69_Objekt_69.pdf", "markdown_path": "documents/69_Objekt_69/extracted_content.md", "json_path": "documents/69_Objekt_69/extracted_content.json"}}, {"document_folder": "77_Objekt_77", "building_id": "4e5596f9-f928-47a8-907f-2538d291e0be", "building_name": "Moor House", "building_code": "1000000077", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Moor House' -> 'Moor House' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/77_Objekt_77/77_Objekt_77.pdf", "markdown_path": "documents/77_Objekt_77/extracted_content.md", "json_path": "documents/77_Objekt_77/extracted_content.json"}}, {"document_folder": "131_Objekt_131", "building_id": "ec3dc6d1-f6ef-4099-909f-ab897b7583ac", "building_name": "One Rathbone Square", "building_code": "1000000131", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'One Rathbone Square' -> 'One Rathbone Square' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/131_Objekt_131/131_Objekt_131.pdf", "markdown_path": "documents/131_Objekt_131/extracted_content.md", "json_path": "documents/131_Objekt_131/extracted_content.json"}}, {"document_folder": "107_Objekt_107", "building_id": "97b5f5d4-eebf-4874-b9f5-83bbf9f106fa", "building_name": "Logistics Centre A/C", "building_code": "1000000107", "match_type": "name", "confidence": 0.8181818181818181, "reasoning": "Name match: ''Logistics Centre A/C'' -> 'Logistics Centre A/C' (confidence: 0.91)", "certificate_info": {"pdf_path": "documents/107_Objekt_107/107_Objekt_107.pdf", "markdown_path": "documents/107_Objekt_107/extracted_content.md", "json_path": "documents/107_Objekt_107/extracted_content.json"}}, {"document_folder": "76_Objekt_76", "building_id": "9003555b-57c5-426a-b8e5-31fcdc035e5b", "building_name": "Old Jewry", "building_code": "1000000076", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Old Jewry' -> 'Old Jewry' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/76_Objekt_76/76_Objekt_76.pdf", "markdown_path": "documents/76_Objekt_76/extracted_content.md", "json_path": "documents/76_Objekt_76/extracted_content.json"}}, {"document_folder": "63_Obje<PERSON>_63", "building_id": "1ccb4dde-ccc2-4267-aa90-d682090fe9ab", "building_name": "Kaufhaus Gerngross", "building_code": "1000000063", "match_type": "name", "confidence": 0.81, "reasoning": "Name match: ''Kaufhaus Gerngross'' -> 'Kaufhaus Gerngross' (confidence: 0.90)", "certificate_info": {"pdf_path": "documents/63_Objekt_63/63_Objekt_63.pdf", "markdown_path": "documents/63_Objekt_63/extracted_content.md", "json_path": "documents/63_Objekt_63/extracted_content.json"}}, {"document_folder": "39_Obje<PERSON>_39", "building_id": "9bdb9a60-6beb-40da-82cd-d0f8b556ce9d", "building_name": "Schloss-Arkaden", "building_code": "1000000039", "match_type": "name", "confidence": 0.7941176470588235, "reasoning": "Name match: ''<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>'' -> '<PERSON><PERSON><PERSON>-<PERSON>ade<PERSON>' (confidence: 0.88)", "certificate_info": {"pdf_path": "documents/39_Objekt_39/39_Objekt_39.pdf", "markdown_path": "documents/39_Objekt_39/extracted_content.md", "json_path": "documents/39_Objekt_39/extracted_content.json"}}, {"document_folder": "15_Obje<PERSON>_15", "building_id": "c592f971-2fda-4169-8e89-c7f296a3300f", "building_name": "Prime Parc, Bauteil C1 + Parkhaus", "building_code": "1000000015", "match_type": "name", "confidence": 0.8485714285714285, "reasoning": "Name match: ''Prime <PERSON><PERSON>, <PERSON><PERSON><PERSON> C1 + <PERSON><PERSON>'' -> 'Prime <PERSON><PERSON>, <PERSON><PERSON><PERSON> C1 + <PERSON><PERSON>' (confidence: 0.94)", "certificate_info": {"pdf_path": "documents/15_Objekt_15/15_Objekt_15.pdf", "markdown_path": "documents/15_Objekt_15/extracted_content.md", "json_path": "documents/15_Objekt_15/extracted_content.json"}}, {"document_folder": "18_Objekt_18", "building_id": "b51655df-18bc-4b4f-b8a6-48337c68757d", "building_name": "Lighttower", "building_code": "1000000018", "match_type": "name", "confidence": 0.75, "reasoning": "Name match: ''Lighttower'' -> 'Lighttower' (confidence: 0.83)", "certificate_info": {"pdf_path": "documents/18_Objekt_18/18_Objekt_18.pdf", "markdown_path": "documents/18_Objekt_18/extracted_content.md", "json_path": "documents/18_Objekt_18/extracted_content.json"}}, {"document_folder": "14_Objekt_14", "building_id": "89afdf02-d465-4814-85ec-9ac00ef4c580", "building_name": "<PERSON> <PERSON>, Bauteil B1-B8", "building_code": "1000000014", "match_type": "name", "confidence": 0.8333333333333334, "reasoning": "Name match: ''Prime <PERSON><PERSON>, <PERSON><PERSON><PERSON> B1-B8'' -> 'Prime <PERSON><PERSON>, <PERSON><PERSON><PERSON> B1-B8' (confidence: 0.93)", "certificate_info": {"pdf_path": "documents/14_Objekt_14/14_Objekt_14.pdf", "markdown_path": "documents/14_Objekt_14/extracted_content.md", "json_path": "documents/14_Objekt_14/extracted_content.json"}}, {"document_folder": "65_Ob<PERSON><PERSON>_65", "building_id": "7111ddf7-10b7-4a6b-8ad9-8beccef16f10", "building_name": "Hotel \"Le Méridien\"", "building_code": "1000000061", "match_type": "name", "confidence": 0.8142857142857143, "reasoning": "Name match: ''Hotel \"Le Méridien\"'' -> 'Hotel \"Le Méridien\"' (confidence: 0.90)", "certificate_info": {"pdf_path": "documents/65_Objekt_65/65_Objekt_65.pdf", "markdown_path": "documents/65_Objekt_65/extracted_content.md", "json_path": "documents/65_Objekt_65/extracted_content.json"}}, {"document_folder": "50_Objekt_50", "building_id": "d63aaff1-e5c0-4fd8-b44d-5816efff4582", "building_name": "<PERSON><PERSON>", "building_code": "1000000050", "match_type": "name", "confidence": 0.78, "reasoning": "Name match: ''<PERSON><PERSON>'' -> '<PERSON><PERSON> Ha<PERSON>' (confidence: 0.87)", "certificate_info": {"pdf_path": "documents/50_Objekt_50/50_Objekt_50.pdf", "markdown_path": "documents/50_Objekt_50/extracted_content.md", "json_path": "documents/50_Objekt_50/extracted_content.json"}}, {"document_folder": "45_Objekt_45", "building_id": "8c8e19d6-5c88-49f1-93a1-d16c78b942cf", "building_name": "Sofitel Munich Bayerpost", "building_code": "1000000045", "match_type": "name", "confidence": 1.0, "reasoning": "Name match: 'Sofitel Munich Bayerpost' -> 'Sofitel Munich Bayerpost' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/45_Objekt_45/45_Objekt_45.pdf", "markdown_path": "documents/45_Objekt_45/extracted_content.md", "json_path": "documents/45_Objekt_45/extracted_content.json"}}, {"document_folder": "08_Objekt_8", "building_id": "b663e664-9bcb-4adb-afb0-b3f7118ecec5", "building_name": "Tiergarten Tower", "building_code": "1000000008", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Tiergarten Tower' -> 'Tiergarten Tower' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/08_Objekt_8/08_Objekt_8.pdf", "markdown_path": "documents/08_Objekt_8/extracted_content.md", "json_path": "documents/08_Objekt_8/extracted_content.json"}}, {"document_folder": "01_Objekt_1", "building_id": "57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2", "building_name": "RONDO", "building_code": "1000000001", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'ROND<PERSON>' -> 'RONDO' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/01_Objekt_1/01_Objekt_1.pdf", "markdown_path": "documents/01_Objekt_1/extracted_content.md", "json_path": "documents/01_Objekt_1/extracted_content.json"}}, {"document_folder": "84_Objekt_84", "building_id": "be3982d9-1086-4de2-a5be-614a95e13330", "building_name": "<PERSON> MP2", "building_code": "1000000084", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON> MP2' -> '<PERSON> MP2' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/84_Objekt_84/84_Objekt_84.pdf", "markdown_path": "documents/84_Objekt_84/extracted_content.md", "json_path": "documents/84_Objekt_84/extracted_content.json"}}, {"document_folder": "100_Objekt_100", "building_id": "66c560f4-2f87-40af-9e3c-992d886a1d60", "building_name": "Hotel Renaissance Arc de Triomphe", "building_code": "1000000100", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Hotel Renaissance Arc de Triomphe' -> 'Hotel Renaissance Arc de Triomphe' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/100_Objekt_100/100_Objekt_100.pdf", "markdown_path": "documents/100_Objekt_100/extracted_content.md", "json_path": "documents/100_Objekt_100/extracted_content.json"}}, {"document_folder": "74_Objekt_74", "building_id": "0fb819dd-8215-4224-be62-f8bc93aab237", "building_name": "New Deal", "building_code": "1000000074", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'New Deal' -> 'New Deal' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/74_Objekt_74/74_Objekt_74.pdf", "markdown_path": "documents/74_Objekt_74/extracted_content.md", "json_path": "documents/74_Objekt_74/extracted_content.json"}}, {"document_folder": "29_<PERSON><PERSON>je<PERSON>_29", "building_id": "a01e6e44-48b0-41c4-aa7f-2fa3298e0f4c", "building_name": "Lindner Hotel", "building_code": "1000000029", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Lindner Hotel' -> 'Lindner Hotel' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/29_Objekt_29/29_Objekt_29.pdf", "markdown_path": "documents/29_Objekt_29/extracted_content.md", "json_path": "documents/29_Objekt_29/extracted_content.json"}}, {"document_folder": "40_Objekt_40", "building_id": "cfa4612e-4f03-4e50-a56f-6111375b2ab5", "building_name": "Metropolis Haus", "building_code": "1000000040", "match_type": "name", "confidence": 0.7941176470588235, "reasoning": "Name match: ''Metropolis Haus'' -> 'Metropolis Haus' (confidence: 0.88)", "certificate_info": {"pdf_path": "documents/40_Objekt_40/40_Objekt_40.pdf", "markdown_path": "documents/40_Objekt_40/extracted_content.md", "json_path": "documents/40_Objekt_40/extracted_content.json"}}, {"document_folder": "117_Objekt_117", "building_id": "6f8a060f-0954-411a-aba3-0425fc60288c", "building_name": "Mahon Point Shopping Center", "building_code": "1000000117", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Mahon Point Shopping Center' -> 'Mahon Point Shopping Center' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/117_Objekt_117/117_Objekt_117.pdf", "markdown_path": "documents/117_Objekt_117/extracted_content.md", "json_path": "documents/117_Objekt_117/extracted_content.json"}}, {"document_folder": "114_Objekt_114", "building_id": "a451ae80-eb47-4842-a66c-5340406fe0d9", "building_name": "Vitrum", "building_code": "1000000114", "match_type": "name", "confidence": 0.675, "reasoning": "Name match: ''Vitrum'' -> 'Vitrum' (confidence: 0.75)", "certificate_info": {"pdf_path": "documents/114_Objekt_114/114_Objekt_114.pdf", "markdown_path": "documents/114_Objekt_114/extracted_content.md", "json_path": "documents/114_Objekt_114/extracted_content.json"}}, {"document_folder": "44_Objekt_44", "building_id": "5154cc9b-6a58-4bb4-a015-7bad385f7c0c", "building_name": "Hotel Le Méridien", "building_code": "1000000044", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Hotel Le Méridien' -> 'Hotel Le Méridien' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/44_Objekt_44/44_Objekt_44.pdf", "markdown_path": "documents/44_Objekt_44/extracted_content.md", "json_path": "documents/44_Objekt_44/extracted_content.json"}}, {"document_folder": "127_Objekt_127", "building_id": "5f3672ed-fdea-4db2-b4cf-d06f9327d474", "building_name": "CTPark Prague North 1", "building_code": "1000000127", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'CTPark Prague North 1' -> 'CTPark Prague North 1' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/127_Objekt_127/127_Objekt_127.pdf", "markdown_path": "documents/127_Objekt_127/extracted_content.md", "json_path": "documents/127_Objekt_127/extracted_content.json"}}, {"document_folder": "16_Objekt_16", "building_id": "d86edecc-2881-4d9e-9372-faa829b541a6", "building_name": "Prime Parc, Bauteil C2", "building_code": "1000000016", "match_type": "name", "confidence": 0.825, "reasoning": "Name match: ''Prime <PERSON><PERSON>, <PERSON><PERSON><PERSON> C2'' -> 'Prime <PERSON><PERSON>, <PERSON><PERSON><PERSON> C2' (confidence: 0.92)", "certificate_info": {"pdf_path": "documents/16_Objekt_16/16_Objekt_16.pdf", "markdown_path": "documents/16_Objekt_16/extracted_content.md", "json_path": "documents/16_Objekt_16/extracted_content.json"}}, {"document_folder": "25_Objekt_25", "building_id": "a4288287-712a-4c30-86f5-26f4c9b39200", "building_name": "Bayerstraße 21/V", "building_code": "1000000025", "match_type": "address", "confidence": 0.615, "reasoning": "Address match: 'Bayerstraße 21 / Zweigstraße 4, München' -> '{'street': 'Bayerstraße', 'house_number': '21/V', 'city': 'München', 'postal_code': '80335'}' (confidence: 0.68)", "certificate_info": {"pdf_path": "documents/25_Objekt_25/25_Objekt_25.pdf", "markdown_path": "documents/25_Objekt_25/extracted_content.md", "json_path": "documents/25_Objekt_25/extracted_content.json"}}, {"document_folder": "64_Objekt_64", "building_id": "deeddaa1-108b-4c09-a75d-5d867249b53f", "building_name": "El Triangle", "building_code": "1000000064", "match_type": "name", "confidence": 0.7615384615384615, "reasoning": "Name match: ''El Triangle'' -> 'El Triangle' (confidence: 0.85)", "certificate_info": {"pdf_path": "documents/64_Objekt_64/64_Objekt_64.pdf", "markdown_path": "documents/64_Objekt_64/extracted_content.md", "json_path": "documents/64_Objekt_64/extracted_content.json"}}, {"document_folder": "57_O<PERSON><PERSON><PERSON>_57", "building_id": "eed9967e-f5b7-4bca-a464-a0638dc60f09", "building_name": "<PERSON><PERSON><PERSON>, Broekman", "building_code": "1000000057", "match_type": "name", "confidence": 0.7941176470588235, "reasoning": "Name match: ''<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>'' -> '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>' (confidence: 0.88)", "certificate_info": {"pdf_path": "documents/57_Objekt_57/57_Objekt_57.pdf", "markdown_path": "documents/57_Objekt_57/extracted_content.md", "json_path": "documents/57_Objekt_57/extracted_content.json"}}, {"document_folder": "87_Objekt_87", "building_id": "c159db6c-d0a5-4298-baff-f65a2d735bcc", "building_name": "Generation Park Z", "building_code": "1000000087", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: 'Generation Park Z' -> 'Generation Park Z' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/87_Objekt_87/87_Objekt_87.pdf", "markdown_path": "documents/87_Objekt_87/extracted_content.md", "json_path": "documents/87_Objekt_87/extracted_content.json"}}, {"document_folder": "52_Objekt_52", "building_id": "f978168a-f8c2-44de-a827-ffead32b8cba", "building_name": "De Resident", "building_code": "1000000052", "match_type": "name", "confidence": 0.7615384615384615, "reasoning": "Name match: ''De Resident'' -> 'De Resident' (confidence: 0.85)", "certificate_info": {"pdf_path": "documents/52_Objekt_52/52_Objekt_52.pdf", "markdown_path": "documents/52_Objekt_52/extracted_content.md", "json_path": "documents/52_Objekt_52/extracted_content.json"}}, {"document_folder": "103_Objekt_103", "building_id": "21c44521-ea5e-4e8e-97ce-d67fd02eeac4", "building_name": "23 Opéra", "building_code": "1000000103", "match_type": "name", "confidence": 0.****************, "reasoning": "Name match: ''23 <PERSON>éra'' -> '23 Opéra' (confidence: 0.80)", "certificate_info": {"pdf_path": "documents/103_Objekt_103/103_Objekt_103.pdf", "markdown_path": "documents/103_Objekt_103/extracted_content.md", "json_path": "documents/103_Objekt_103/extracted_content.json"}}, {"document_folder": "11_Objekt_11", "building_id": "ad8f9f50-c667-45e6-ac48-ac8d7ed7b29b", "building_name": "SpreePalais am Dom", "building_code": "1000000011", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON><PERSON><PERSON><PERSON> am Dom' -> '<PERSON><PERSON><PERSON><PERSON><PERSON> am Dom' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/11_Objekt_11/11_Objekt_11.pdf", "markdown_path": "documents/11_Objekt_11/extracted_content.md", "json_path": "documents/11_Objekt_11/extracted_content.json"}}, {"document_folder": "93_Objekt_93", "building_id": "87389579-0188-4e4b-af64-10a14fce29f2", "building_name": "HochZwei", "building_code": "1000000093", "match_type": "name", "confidence": 0.****************, "reasoning": "Name match: ''Hoch<PERSON><PERSON>'' -> 'Hoch<PERSON><PERSON>' (confidence: 0.80)", "certificate_info": {"pdf_path": "documents/93_Objekt_93/93_Objekt_93.pdf", "markdown_path": "documents/93_Objekt_93/extracted_content.md", "json_path": "documents/93_Objekt_93/extracted_content.json"}}, {"document_folder": "21_Objekt_21", "building_id": "06ccb9e5-e34b-4637-aaa4-d5e345cc7d22", "building_name": "Poseidon", "building_code": "1000000021", "match_type": "name", "confidence": 0.9, "reasoning": "Name match: '<PERSON><PERSON><PERSON>' -> '<PERSON><PERSON><PERSON>' (confidence: 1.00)", "certificate_info": {"pdf_path": "documents/21_Objekt_21/21_Objekt_21.pdf", "markdown_path": "documents/21_Objekt_21/extracted_content.md", "json_path": "documents/21_Objekt_21/extracted_content.json"}}, {"document_folder": "23_Objekt_23", "building_id": "1892ebb9-9532-4dee-bddc-a7044da700a2", "building_name": "Atrium Plaza", "building_code": "1000000005", "match_type": "address", "confidence": 0.5075, "reasoning": "Address match: 'Mainzer Landstraße 293 / Kleyerstraße 20, Frankfurt am Main' -> '{'street': 'Mainzer Landstraße', 'house_number': '172-190', 'city': 'Frankfurt am Main', 'postal_code': '60327'}' (confidence: 0.72)", "certificate_info": {"pdf_path": "documents/23_Objekt_23/23_Objekt_23.pdf", "markdown_path": "documents/23_Objekt_23/extracted_content.md", "json_path": "documents/23_Objekt_23/extracted_content.json"}}]}