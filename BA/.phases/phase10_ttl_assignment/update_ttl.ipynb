# Install rdflib if needed (no-op if already installed)
import sys, subprocess
try:
    import rdflib  # noqa: F401
except Exception:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "rdflib"])

from rdflib import Graph, Namespace, URIRef, Literal
from rdflib.namespace import RDF
import json
from pathlib import Path

# Resolve assets directory robustly for VS Code Jupyter

def find_assets_dir() -> Path:
    candidates = [
        Path("assets"),
        Path(".phases/phase10_ttl_assignment/assets"),
        Path.cwd() / "assets",
        Path.cwd() / ".phases/phase10_ttl_assignment/assets",
    ]
    for p in candidates:
        if (p / "example.ttl").exists() and (p / "building_certificate_assignments.json").exists():
            return p.resolve()
    # Search up the tree
    for parent in [Path.cwd()] + list(Path.cwd().parents):
        p = parent / ".phases/phase10_ttl_assignment/assets"
        if (p / "example.ttl").exists() and (p / "building_certificate_assignments.json").exists():
            return p.resolve()
    raise FileNotFoundError("Could not locate assets directory containing example.ttl and building_certificate_assignments.json")

ASSETS = find_assets_dir()
TTL_IN = ASSETS / "example.ttl"
TTL_OUT = ASSETS / "updated_example.ttl"
ASSIGNMENTS_JSON = ASSETS / "building_certificate_assignments.json"

# Load graph
g = Graph()
_ = g.parse(TTL_IN.as_posix(), format="turtle")

# Namespaces
IBPDI = Namespace("https://ibpdi.datacat.org/class/")
PROP = Namespace("https://ibpdi.datacat.org/property/")
INST = Namespace("https://example.com/")

g.bind("ibpdi", IBPDI)
g.bind("prop", PROP)
g.bind("inst", INST)

# Classes and properties
Building = IBPDI.Building
BuildingCertificate = IBPDI.BuildingCertificate
hasCertificate = IBPDI.hasCertificate

# Document path properties (using hyphen style like other props)
pdfPath = PROP["pdf-path"]
mdPath = PROP["markdown-path"]
jsonPath = PROP["json-path"]

# Read assignments
with ASSIGNMENTS_JSON.open("r", encoding="utf-8") as f:
    payload = json.load(f)

assignments = payload.get("assignments", [])

created = 0
linked = 0

for item in assignments:
    b_id = item.get("building_id")
    cert = item.get("certificate_info", {})
    pdf = cert.get("pdf_path")
    md = cert.get("markdown_path")
    js = cert.get("json_path")

    if not b_id or not (pdf or md or js):
        continue

    b_uri = INST[str(b_id)]

    # Certificate node IRI derived from document folder and building id
    doc_folder = item.get("document_folder") or "cert"
    cert_id = f"certificate-{b_id}-{doc_folder}"
    c_uri = INST[cert_id]

    # Assert certificate node and its type
    g.add((c_uri, RDF.type, BuildingCertificate))

    # Attach available document paths as literals
    if pdf:
        g.add((c_uri, pdfPath, Literal(pdf)))
    if md:
        g.add((c_uri, mdPath, Literal(md)))
    if js:
        g.add((c_uri, jsonPath, Literal(js)))

    created += 1

    # Link building -> certificate
    g.add((b_uri, hasCertificate, c_uri))
    linked += 1

# Serialize updated graph
_ = g.serialize(destination=TTL_OUT.as_posix(), format="turtle")

print(f"Certificates created: {created}")
print(f"Building-certificate links: {linked}")
print(f"Written: {TTL_OUT}")

# Quick verification: count BuildingCertificate nodes and preview some triples
from rdflib import Graph, Namespace
from rdflib.namespace import RDF
from pathlib import Path

IBPDI = Namespace("https://ibpdi.datacat.org/class/")
PROP = Namespace("https://ibpdi.datacat.org/property/")
INST = Namespace("https://example.com/")

# Reuse the same path finder

def find_assets_dir() -> Path:
    candidates = [
        Path("assets"),
        Path(".phases/phase10_ttl_assignment/assets"),
        Path.cwd() / "assets",
        Path.cwd() / ".phases/phase10_ttl_assignment/assets",
    ]
    for p in candidates:
        if (p / "updated_example.ttl").exists():
            return p.resolve()
    for parent in [Path.cwd()] + list(Path.cwd().parents):
        p = parent / ".phases/phase10_ttl_assignment/assets"
        if (p / "updated_example.ttl").exists():
            return p.resolve()
    raise FileNotFoundError("Could not locate assets directory containing updated_example.ttl")

ASSETS = find_assets_dir()

h = Graph()
h.parse((ASSETS / "updated_example.ttl").as_posix(), format="turtle")

count = sum(1 for _ in h.triples((None, RDF.type, IBPDI.BuildingCertificate)))
print("BuildingCertificate count:", count)

# Show 5 sample certificates with paths
shown = 0
for s, _, _ in h.triples((None, RDF.type, IBPDI.BuildingCertificate)):
    pdf = next((o for _, _, o in h.triples((s, PROP["pdf-path"], None))), None)
    md = next((o for _, _, o in h.triples((s, PROP["markdown-path"], None))), None)
    js = next((o for _, _, o in h.triples((s, PROP["json-path"], None))), None)
    print("-", s)
    print("  pdf:", pdf)
    print("  md :", md)
    print("  json:", js)
    shown += 1
    if shown >= 5:
        break