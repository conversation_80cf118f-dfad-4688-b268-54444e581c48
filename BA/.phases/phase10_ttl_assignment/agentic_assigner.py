#!/usr/bin/env python3
"""
Agentic Document Assignment System for TTL Buildings
Intelligently assigns documents to buildings using AI reasoning with name and address matching
"""

import os
import sys
import json
import time
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
import re

# Rich imports for beautiful CLI
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich import box
from rich.text import Text

# Load environment
import dotenv
dotenv.load_dotenv()

# Pydantic AI imports
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

# RDF handling
import rdflib
from rdflib import Graph

# Set up API key and model
OR_API_KEY = os.getenv("OR_API_KEY")
if not OR_API_KEY:
    raise ValueError("OR_API_KEY environment variable is required")

model = OpenAIChatModel(
    'openai/gpt-4o-mini',
    provider=OpenRouterProvider(api_key=OR_API_KEY)
)

# Data Models
@dataclass
class BuildingInfo:
    """Information about a building from TTL"""
    id: str
    name: str
    building_code: str
    address_info: Optional[Dict[str, str]] = None

@dataclass
class DocumentInfo:
    """Information about a document"""
    folder_name: str
    pdf_path: str
    markdown_path: str
    json_path: str
    markdown_content: str

class DocumentAnalysis(BaseModel):
    """Analysis result from AI agent"""
    building_name: Optional[str] = Field(description="Extracted building name from document", default=None)
    street_address: Optional[str] = Field(description="Extracted street address", default=None)
    city: Optional[str] = Field(description="Extracted city", default=None)
    postal_code: Optional[str] = Field(description="Extracted postal code", default=None)
    confidence: float = Field(description="Confidence in extraction (0-1)", default=0.0)
    reasoning: str = Field(description="Explanation of extraction reasoning")

class AssignmentResult(BaseModel):
    """Result of document assignment"""
    document_folder: str = Field(description="Document folder name")
    building_id: Optional[str] = Field(description="Assigned building ID", default=None)
    building_name: Optional[str] = Field(description="Assigned building name", default=None)
    match_type: str = Field(description="Type of match: 'name', 'address', or 'failed'")
    confidence: float = Field(description="Assignment confidence", default=0.0)
    reasoning: str = Field(description="Assignment reasoning")

class TimingInfo:
    """Track timing for operations"""
    def __init__(self):
        self.start_time = time.time()
        self.step_times = {}

    def mark_step(self, step_name: str):
        self.step_times[step_name] = time.time() - self.start_time

    def get_total_time(self) -> float:
        return time.time() - self.start_time

# Document Analysis Agent
document_analyzer = Agent[str, DocumentAnalysis](
    model,
    deps_type=str,
    output_type=DocumentAnalysis,
    system_prompt="""You are an expert document analyzer specializing in real estate and building documents.

Your task is to extract key information from building/property documents, specifically:
1. Building name (often appears as a title, project name, or property name)
2. Street address (street name and house number)
3. City name
4. Postal code

EXTRACTION GUIDELINES:
- Building names are often in quotes, titles, or prominently displayed
- Look for patterns like "Building Name", 'Building Name', or **Building Name**
- Street addresses typically follow European format: "Street Name Number" or "Street Name Number-Range"
- Cities are often followed by postal codes
- Be very careful to distinguish between building names and company names
- Focus on the actual property/building being described, not the company managing it

CONFIDENCE SCORING:
- 0.9-1.0: Very clear, unambiguous information
- 0.7-0.8: Good information with minor ambiguity
- 0.5-0.6: Moderate confidence, some uncertainty
- 0.3-0.4: Low confidence, significant ambiguity
- 0.0-0.2: Very uncertain or no clear information

Always provide detailed reasoning for your extractions and confidence scores.
"""
)

class AgenticDocumentAssigner:
    """Main class for agentic document assignment"""

    def __init__(self, assets_path: str = "assets"):
        self.console = Console()
        self.assets_path = Path(assets_path)
        self.ttl_path = self.assets_path / "example.ttl"
        self.documents_path = self.assets_path / "documents"
        self.buildings: Dict[str, BuildingInfo] = {}
        self.timing = TimingInfo()

    def print_header(self):
        """Print beautiful app header"""
        header_text = """
🤖 Agentic Document Assignment System
Intelligent Building Certificate Assignment using AI Reasoning
        """

        header_panel = Panel(
            header_text.strip(),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )

        self.console.print(header_panel)
        self.console.print()

    def load_buildings_from_ttl(self) -> bool:
        """Load building information from TTL file"""
        try:
            self.console.print("[cyan]Loading buildings from TTL file...[/cyan]")

            graph = Graph()
            graph.parse(str(self.ttl_path), format='turtle')

            # Query for buildings with their names and addresses
            query = """
            PREFIX ibpdi: <https://ibpdi.datacat.org/class/>
            PREFIX inst: <https://example.com/>
            PREFIX prop: <https://ibpdi.datacat.org/property/>

            SELECT ?building ?name ?code ?street ?house_number ?city ?postal_code WHERE {
                ?building a ibpdi:Building ;
                         prop:name ?name ;
                         prop:building-code ?code .

                OPTIONAL {
                    ?address ibpdi:hasBuilding ?building ;
                            prop:street-name ?street ;
                            prop:house-number ?house_number ;
                            prop:city ?city ;
                            prop:postal-code ?postal_code .
                }
            }
            """

            results = list(graph.query(query))

            for row in results:
                building_id = str(row[0]).split('/')[-1]  # Extract ID from URI
                name = str(row[1]) if row[1] else ""
                code = str(row[2]) if row[2] else ""

                address_info = None
                if row[3] and row[4] and row[5]:  # street, house_number, city
                    address_info = {
                        'street': str(row[3]),
                        'house_number': str(row[4]),
                        'city': str(row[5]),
                        'postal_code': str(row[6]) if row[6] else ""
                    }

                self.buildings[building_id] = BuildingInfo(
                    id=building_id,
                    name=name,
                    building_code=code,
                    address_info=address_info
                )

            self.console.print(f"[green]✅ Loaded {len(self.buildings)} buildings from TTL[/green]")
            self.timing.mark_step("load_buildings")
            return True

        except Exception as e:
            self.console.print(f"[red]❌ Error loading TTL file: {e}[/red]")
            return False

    def load_document_info(self, folder_path: Path) -> Optional[DocumentInfo]:
        """Load document information from a folder"""
        try:
            pdf_path = folder_path / f"{folder_path.name}.pdf"
            markdown_path = folder_path / "extracted_content.md"
            json_path = folder_path / "extracted_content.json"

            if not markdown_path.exists():
                return None

            # Read first 15 lines of markdown as specified
            with open(markdown_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:15]
                markdown_content = ''.join(lines)

            return DocumentInfo(
                folder_name=folder_path.name,
                pdf_path=str(pdf_path),
                markdown_path=str(markdown_path),
                json_path=str(json_path),
                markdown_content=markdown_content
            )

        except Exception as e:
            self.console.print(f"[yellow]⚠️ Error loading document {folder_path.name}: {e}[/yellow]")
            return None

    async def analyze_document(self, doc_info: DocumentInfo) -> DocumentAnalysis:
        """Analyze document using AI agent"""
        try:
            prompt = f"""Analyze this building document and extract key information:

Document: {doc_info.folder_name}
Content (first 15 lines):
{doc_info.markdown_content}

Extract the building name, street address, city, and postal code from this content.
Focus on the actual building/property being described, not company names.
"""

            result = await document_analyzer.run(prompt, deps="")
            return result.output

        except Exception as e:
            self.console.print(f"[red]❌ Error analyzing document {doc_info.folder_name}: {e}[/red]")
            return DocumentAnalysis(
                confidence=0.0,
                reasoning=f"Analysis failed: {e}"
            )

    def find_building_by_name(self, extracted_name: str) -> Optional[Tuple[str, BuildingInfo, float]]:
        """Find building by name matching"""
        if not extracted_name:
            return None

        extracted_name_clean = extracted_name.lower().strip()
        best_match = None
        best_score = 0.0

        for building_id, building in self.buildings.items():
            building_name_clean = building.name.lower().strip()

            # Exact match
            if extracted_name_clean == building_name_clean:
                return building_id, building, 1.0

            # Partial match (building name contains extracted name or vice versa)
            if extracted_name_clean in building_name_clean or building_name_clean in extracted_name_clean:
                score = min(len(extracted_name_clean), len(building_name_clean)) / max(len(extracted_name_clean), len(building_name_clean))
                if score > best_score and score > 0.7:  # Threshold for partial matches
                    best_match = (building_id, building, score)
                    best_score = score

        return best_match

    def find_building_by_address(self, street: str, city: str) -> Optional[Tuple[str, BuildingInfo, float]]:
        """Find building by address matching"""
        if not street or not city:
            return None

        street_clean = street.lower().strip()
        city_clean = city.lower().strip()

        for building_id, building in self.buildings.items():
            if not building.address_info:
                continue

            building_street = building.address_info['street'].lower().strip()
            building_city = building.address_info['city'].lower().strip()

            # Check city match first
            if city_clean not in building_city and building_city not in city_clean:
                continue

            # Check street match
            if street_clean in building_street or building_street in street_clean:
                # Calculate confidence based on match quality
                street_score = min(len(street_clean), len(building_street)) / max(len(street_clean), len(building_street))
                city_score = min(len(city_clean), len(building_city)) / max(len(city_clean), len(building_city))
                overall_score = (street_score + city_score) / 2

                if overall_score > 0.6:  # Threshold for address matches
                    return building_id, building, overall_score

        return None

    async def assign_document(self, doc_info: DocumentInfo) -> AssignmentResult:
        """Assign a document to a building"""
        # Analyze document
        analysis = await self.analyze_document(doc_info)

        # Try name matching first
        if analysis.building_name:
            name_match = self.find_building_by_name(analysis.building_name)
            if name_match:
                building_id, building, confidence = name_match
                return AssignmentResult(
                    document_folder=doc_info.folder_name,
                    building_id=building_id,
                    building_name=building.name,
                    match_type="name",
                    confidence=confidence * analysis.confidence,
                    reasoning=f"Name match: '{analysis.building_name}' -> '{building.name}' (confidence: {confidence:.2f})"
                )

        # Try address matching
        if analysis.street_address and analysis.city:
            address_match = self.find_building_by_address(analysis.street_address, analysis.city)
            if address_match:
                building_id, building, confidence = address_match
                return AssignmentResult(
                    document_folder=doc_info.folder_name,
                    building_id=building_id,
                    building_name=building.name,
                    match_type="address",
                    confidence=confidence * analysis.confidence,
                    reasoning=f"Address match: '{analysis.street_address}, {analysis.city}' -> '{building.address_info}' (confidence: {confidence:.2f})"
                )

        # No match found
        return AssignmentResult(
            document_folder=doc_info.folder_name,
            match_type="failed",
            confidence=0.0,
            reasoning=f"No match found. Extracted: name='{analysis.building_name}', address='{analysis.street_address}, {analysis.city}'"
        )

    async def process_all_documents(self) -> Tuple[List[AssignmentResult], List[AssignmentResult]]:
        """Process all documents and return successful assignments and failures"""
        if not self.documents_path.exists():
            self.console.print(f"[red]❌ Documents path not found: {self.documents_path}[/red]")
            return [], []

        document_folders = [d for d in self.documents_path.iterdir() if d.is_dir()]

        self.console.print(f"[cyan]Processing {len(document_folders)} documents...[/cyan]")

        successful_assignments = []
        failed_assignments = []

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        ) as progress:
            task = progress.add_task("Processing documents...", total=len(document_folders))

            for folder in document_folders:
                doc_info = self.load_document_info(folder)
                if not doc_info:
                    progress.advance(task)
                    continue

                progress.update(task, description=f"Processing {folder.name}...")

                assignment = await self.assign_document(doc_info)

                if assignment.match_type == "failed":
                    failed_assignments.append(assignment)
                else:
                    successful_assignments.append(assignment)

                progress.advance(task)

        self.timing.mark_step("process_documents")
        return successful_assignments, failed_assignments

    def save_results(self, successful: List[AssignmentResult], failed: List[AssignmentResult]):
        """Save assignment results to JSON files"""
        try:
            # Prepare successful assignments data
            assignments_data = []
            for assignment in successful:
                assignments_data.append({
                    "document_folder": assignment.document_folder,
                    "building_id": assignment.building_id,
                    "building_name": assignment.building_name,
                    "building_code": self.buildings[assignment.building_id].building_code if assignment.building_id else None,
                    "match_type": assignment.match_type,
                    "confidence": assignment.confidence,
                    "reasoning": assignment.reasoning,
                    "certificate_info": {
                        "pdf_path": f"documents/{assignment.document_folder}/{assignment.document_folder}.pdf",
                        "markdown_path": f"documents/{assignment.document_folder}/extracted_content.md",
                        "json_path": f"documents/{assignment.document_folder}/extracted_content.json"
                    }
                })

            # Prepare failed assignments data
            failures_data = []
            for failure in failed:
                failures_data.append({
                    "document_folder": failure.document_folder,
                    "reasoning": failure.reasoning,
                    "pdf_path": f"documents/{failure.document_folder}/{failure.document_folder}.pdf"
                })

            # Save assignments
            assignments_file = self.assets_path / "building_certificate_assignments.json"
            with open(assignments_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "metadata": {
                        "total_documents": len(successful) + len(failed),
                        "successful_assignments": len(successful),
                        "failed_assignments": len(failed),
                        "success_rate": len(successful) / (len(successful) + len(failed)) * 100 if (len(successful) + len(failed)) > 0 else 0,
                        "processing_time_seconds": self.timing.get_total_time(),
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    },
                    "assignments": assignments_data
                }, f, indent=2, ensure_ascii=False)

            # Save failures
            failures_file = self.assets_path / "assignment_failures.json"
            with open(failures_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "metadata": {
                        "total_failures": len(failed),
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    },
                    "failures": failures_data
                }, f, indent=2, ensure_ascii=False)

            self.console.print(f"[green]✅ Results saved to:[/green]")
            self.console.print(f"  - Assignments: {assignments_file}")
            self.console.print(f"  - Failures: {failures_file}")

        except Exception as e:
            self.console.print(f"[red]❌ Error saving results: {e}[/red]")

    def display_summary(self, successful: List[AssignmentResult], failed: List[AssignmentResult]):
        """Display assignment summary"""
        total = len(successful) + len(failed)
        success_rate = (len(successful) / total * 100) if total > 0 else 0

        # Summary table
        summary_table = Table(title="📊 Assignment Summary", box=box.ROUNDED)
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="green")

        summary_table.add_row("Total Documents", str(total))
        summary_table.add_row("Successful Assignments", str(len(successful)))
        summary_table.add_row("Failed Assignments", str(len(failed)))
        summary_table.add_row("Success Rate", f"{success_rate:.1f}%")
        summary_table.add_row("Processing Time", f"{self.timing.get_total_time():.2f}s")

        self.console.print(summary_table)

        # Match type breakdown
        if successful:
            match_types = {}
            for assignment in successful:
                match_types[assignment.match_type] = match_types.get(assignment.match_type, 0) + 1

            match_table = Table(title="🎯 Match Type Breakdown", box=box.ROUNDED)
            match_table.add_column("Match Type", style="cyan")
            match_table.add_column("Count", style="green")
            match_table.add_column("Percentage", style="yellow")

            for match_type, count in match_types.items():
                percentage = (count / len(successful) * 100)
                match_table.add_row(match_type.title(), str(count), f"{percentage:.1f}%")

            self.console.print(match_table)

    async def run(self):
        """Main execution method"""
        self.print_header()

        # Load buildings from TTL
        if not self.load_buildings_from_ttl():
            return

        # Process all documents
        successful, failed = await self.process_all_documents()

        # Save results
        self.save_results(successful, failed)

        # Display summary
        self.display_summary(successful, failed)

        self.console.print(f"\n[bold green]🎉 Assignment process completed![/bold green]")

async def main():
    """Main entry point"""
    assigner = AgenticDocumentAssigner()
    await assigner.run()

if __name__ == "__main__":
    asyncio.run(main())