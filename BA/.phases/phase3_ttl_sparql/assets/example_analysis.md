# TTL File Analysis: example.ttl\n\n## File Overview\n- **File Path**: `assets\example.ttl`\n- **File Size**: 91,423 bytes\n- **Total Triples**: 2,172\n\n## Namespaces and Prefixes\n| Prefix | Namespace URI |\n|--------|---------------|\n| `brick` | `https://brickschema.org/schema/Brick#` |\n| `csvw` | `http://www.w3.org/ns/csvw#` |\n| `dc` | `http://purl.org/dc/elements/1.1/` |\n| `dcam` | `http://purl.org/dc/dcam/` |\n| `dcat` | `http://www.w3.org/ns/dcat#` |\n| `dcmitype` | `http://purl.org/dc/dcmitype/` |\n| `dcterms` | `http://purl.org/dc/terms/` |\n| `doap` | `http://usefulinc.com/ns/doap#` |\n| `foaf` | `http://xmlns.com/foaf/0.1/` |\n| `geo` | `http://www.opengis.net/ont/geosparql#` |\n| `ibpdi` | `https://ibpdi.datacat.org/class/` |\n| `inst` | `https://example.com/` |\n| `odrl` | `http://www.w3.org/ns/odrl/2/` |\n| `org` | `http://www.w3.org/ns/org#` |\n| `owl` | `http://www.w3.org/2002/07/owl#` |\n| `prof` | `http://www.w3.org/ns/dx/prof/` |\n| `prop` | `https://ibpdi.datacat.org/property/` |\n| `prov` | `http://www.w3.org/ns/prov#` |\n| `qb` | `http://purl.org/linked-data/cube#` |\n| `rdf` | `http://www.w3.org/1999/02/22-rdf-syntax-ns#` |\n| `rdfs` | `http://www.w3.org/2000/01/rdf-schema#` |\n| `schema` | `https://schema.org/` |\n| `sh` | `http://www.w3.org/ns/shacl#` |\n| `skos` | `http://www.w3.org/2004/02/skos/core#` |\n| `sosa` | `http://www.w3.org/ns/sosa/` |\n| `ssn` | `http://www.w3.org/ns/ssn/` |\n| `time` | `http://www.w3.org/2006/time#` |\n| `vann` | `http://purl.org/vocab/vann/` |\n| `void` | `http://rdfs.org/ns/void#` |\n| `wgs` | `https://www.w3.org/2003/01/geo/wgs84_pos#` |\n| `xml` | `http://www.w3.org/XML/1998/namespace` |\n| `xsd` | `http://www.w3.org/2001/XMLSchema#` |\n\n## Classes and Instance Counts\n| Class | Instance Count |\n|-------|----------------|\n| `https://ibpdi.datacat.org/class/Address` | 137 |\n| `https://ibpdi.datacat.org/class/Building` | 137 |\n\n## Properties and Usage Patterns\n| Property | Usage Count | Distinct Subjects | Distinct Objects |\n|----------|-------------|-------------------|------------------|\n| `http://www.w3.org/1999/02/22-rdf-syntax-ns#type` | 274 | 274 | 2 |\n| `https://ibpdi.datacat.org/property/energy-efficiency-class` | 137 | 137 | 7 |\n| `https://ibpdi.datacat.org/property/building-code` | 137 | 137 | 137 |\n| `https://ibpdi.datacat.org/property/postal-code` | 137 | 137 | 108 |\n| `https://ibpdi.datacat.org/property/construction-year` | 137 | 137 | 52 |\n| `https://ibpdi.datacat.org/class/hasBuilding` | 137 | 137 | 137 |\n| `https://ibpdi.datacat.org/property/valid-from` | 137 | 137 | 1 |\n| `https://ibpdi.datacat.org/property/primary-type-of-building` | 137 | 137 | 4 |\n| `https://ibpdi.datacat.org/property/name` | 137 | 137 | 134 |\n| `https://ibpdi.datacat.org/property/primary-heating-type` | 137 | 137 | 2 |\n| `https://ibpdi.datacat.org/property/country` | 137 | 137 | 16 |\n| `https://ibpdi.datacat.org/property/city` | 136 | 136 | 65 |\n| `https://ibpdi.datacat.org/property/parking-spaces` | 136 | 136 | 88 |\n| `https://ibpdi.datacat.org/property/street-name` | 133 | 133 | 130 |\n| `https://ibpdi.datacat.org/property/house-number` | 123 | 123 | 82 |\n\n## Sample Data by Class\n### `https://ibpdi.datacat.org/class/Address` (Sample Instances)\n- `https://example.com/009cb9c7-07d7-48fd-a539-672fbe6f2652`\n- `https://example.com/018ac746-2503-4f2e-b681-8cb37d2f0e29`\n- `https://example.com/01bea4e6-fee0-40dc-a829-902d901d0a67`\n- `https://example.com/02a175f3-9042-44f7-bb2f-a3b9c5997bb8`\n- `https://example.com/04baa1d3-3007-41e6-ba27-1b124e96706c`\n\n### `https://ibpdi.datacat.org/class/Building` (Sample Instances)\n- `https://example.com/0058c837-25b9-4345-9cc1-ec056f456052`\n- `https://example.com/06ccb9e5-e34b-4637-aaa4-d5e345cc7d22`\n- `https://example.com/075bf17a-11cc-4609-83ed-5b172ae187f2`\n- `https://example.com/0a2add59-d7d2-4802-ac6d-2bbe4f1a4575`\n- `https://example.com/0a8cbe14-3d18-4c6c-a778-abade5b4b8b1`\n\n## Property Patterns by Class\n### Properties used by `https://ibpdi.datacat.org/class/Address` instances\n| Property | Usage Count |\n|----------|-------------|\n| `http://www.w3.org/1999/02/22-rdf-syntax-ns#type` | 137 |\n| `https://ibpdi.datacat.org/class/hasBuilding` | 137 |\n| `https://ibpdi.datacat.org/property/country` | 137 |\n| `https://ibpdi.datacat.org/property/postal-code` | 137 |\n| `https://ibpdi.datacat.org/property/city` | 136 |\n| `https://ibpdi.datacat.org/property/street-name` | 133 |\n| `https://ibpdi.datacat.org/property/house-number` | 123 |\n\n### Properties used by `https://ibpdi.datacat.org/class/Building` instances\n| Property | Usage Count |\n|----------|-------------|\n| `http://www.w3.org/1999/02/22-rdf-syntax-ns#type` | 137 |\n| `https://ibpdi.datacat.org/property/building-code` | 137 |\n| `https://ibpdi.datacat.org/property/construction-year` | 137 |\n| `https://ibpdi.datacat.org/property/energy-efficiency-class` | 137 |\n| `https://ibpdi.datacat.org/property/name` | 137 |\n| `https://ibpdi.datacat.org/property/primary-heating-type` | 137 |\n| `https://ibpdi.datacat.org/property/primary-type-of-building` | 137 |\n| `https://ibpdi.datacat.org/property/valid-from` | 137 |\n| `https://ibpdi.datacat.org/property/parking-spaces` | 136 |\n\n## Value Type Analysis\n### Value Types for `http://www.w3.org/1999/02/22-rdf-syntax-ns#type`\n- **URI**: 274 occurrences\n\n### Value Types for `https://ibpdi.datacat.org/property/energy-efficiency-class`\n- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences\n\n### Value Types for `https://ibpdi.datacat.org/property/building-code`\n- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences\n\n### Value Types for `https://ibpdi.datacat.org/property/postal-code`\n- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences\n\n### Value Types for `https://ibpdi.datacat.org/property/construction-year`\n- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences\n\n## Relationship Patterns\n| Subject Class | Property | Object Class | Count |\n|---------------|----------|--------------|-------|\n| `https://ibpdi.datacat.org/class/Address` | `https://ibpdi.datacat.org/class/hasBuilding` | `https://ibpdi.datacat.org/class/Building` | 137 |\n\n## Sample Literal Values\n### Sample values for `https://ibpdi.datacat.org/property/energy-efficiency-class`\n- `"k"`\n- `"E"`\n- `"A"`\n- `"C"`\n- `"D"`\n- `"B"`\n- `"F"`\n\n### Sample values for `https://ibpdi.datacat.org/property/building-code`\n- `"1000000094"`\n- `"1000000021"`\n- `"1000000017"`\n- `"1000000135"`\n- `"1000000099"`\n- `"1000000132"`\n- `"1000000092"`\n- `"1000000074"`\n- `"1000000034"`\n- `"1000000028"`\n\n## Summary and Query Recommendations\n\n### Key Insights:\n- **2** distinct classes found\n- **15** distinct properties used\n- **1** class-to-class relationship patterns\n- **32** namespace prefixes defined\n\n### Common Query Patterns:\n- **Find all instances of main class**: `SELECT ?instance WHERE { ?instance a <https://ibpdi.datacat.org/class/Address> . }`\n- **Find all values of main property**: `SELECT ?subject ?value WHERE { ?subject <http://www.w3.org/1999/02/22-rdf-syntax-ns#type> ?value . }`\n- **Main relationship pattern**: `SELECT ?s ?o WHERE { ?s a <https://ibpdi.datacat.org/class/Address> . ?s <https://ibpdi.datacat.org/class/hasBuilding> ?o . ?o a <https://ibpdi.datacat.org/class/Building> . }`\n\n---\n*Analysis completed successfully.*