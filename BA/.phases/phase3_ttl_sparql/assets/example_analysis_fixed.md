# TTL File Analysis: example.ttl

## File Overview
- **File Path**: `assets\example.ttl`
- **File Size**: 91,423 bytes
- **Total Triples**: 2,172

## Namespaces and Prefixes
| Prefix | Namespace URI |
|--------|---------------|
| `brick` | `https://brickschema.org/schema/Brick#` |
| `csvw` | `http://www.w3.org/ns/csvw#` |
| `dc` | `http://purl.org/dc/elements/1.1/` |
| `dcam` | `http://purl.org/dc/dcam/` |
| `dcat` | `http://www.w3.org/ns/dcat#` |
| `dcmitype` | `http://purl.org/dc/dcmitype/` |
| `dcterms` | `http://purl.org/dc/terms/` |
| `doap` | `http://usefulinc.com/ns/doap#` |
| `foaf` | `http://xmlns.com/foaf/0.1/` |
| `geo` | `http://www.opengis.net/ont/geosparql#` |
| `ibpdi` | `https://ibpdi.datacat.org/class/` |
| `inst` | `https://example.com/` |
| `odrl` | `http://www.w3.org/ns/odrl/2/` |
| `org` | `http://www.w3.org/ns/org#` |
| `owl` | `http://www.w3.org/2002/07/owl#` |
| `prof` | `http://www.w3.org/ns/dx/prof/` |
| `prop` | `https://ibpdi.datacat.org/property/` |
| `prov` | `http://www.w3.org/ns/prov#` |
| `qb` | `http://purl.org/linked-data/cube#` |
| `rdf` | `http://www.w3.org/1999/02/22-rdf-syntax-ns#` |
| `rdfs` | `http://www.w3.org/2000/01/rdf-schema#` |
| `schema` | `https://schema.org/` |
| `sh` | `http://www.w3.org/ns/shacl#` |
| `skos` | `http://www.w3.org/2004/02/skos/core#` |
| `sosa` | `http://www.w3.org/ns/sosa/` |
| `ssn` | `http://www.w3.org/ns/ssn/` |
| `time` | `http://www.w3.org/2006/time#` |
| `vann` | `http://purl.org/vocab/vann/` |
| `void` | `http://rdfs.org/ns/void#` |
| `wgs` | `https://www.w3.org/2003/01/geo/wgs84_pos#` |
| `xml` | `http://www.w3.org/XML/1998/namespace` |
| `xsd` | `http://www.w3.org/2001/XMLSchema#` |

## Classes and Instance Counts
| Class | Instance Count |
|-------|----------------|
| `https://ibpdi.datacat.org/class/Address` | 137 |
| `https://ibpdi.datacat.org/class/Building` | 137 |

## Properties and Usage Patterns
| Property | Usage Count | Distinct Subjects | Distinct Objects |
|----------|-------------|-------------------|------------------|
| `http://www.w3.org/1999/02/22-rdf-syntax-ns#type` | 274 | 274 | 2 |
| `https://ibpdi.datacat.org/property/energy-efficiency-class` | 137 | 137 | 7 |
| `https://ibpdi.datacat.org/property/building-code` | 137 | 137 | 137 |
| `https://ibpdi.datacat.org/property/postal-code` | 137 | 137 | 108 |
| `https://ibpdi.datacat.org/property/construction-year` | 137 | 137 | 52 |
| `https://ibpdi.datacat.org/class/hasBuilding` | 137 | 137 | 137 |
| `https://ibpdi.datacat.org/property/valid-from` | 137 | 137 | 1 |
| `https://ibpdi.datacat.org/property/primary-type-of-building` | 137 | 137 | 4 |
| `https://ibpdi.datacat.org/property/name` | 137 | 137 | 134 |
| `https://ibpdi.datacat.org/property/primary-heating-type` | 137 | 137 | 2 |
| `https://ibpdi.datacat.org/property/country` | 137 | 137 | 16 |
| `https://ibpdi.datacat.org/property/city` | 136 | 136 | 65 |
| `https://ibpdi.datacat.org/property/parking-spaces` | 136 | 136 | 88 |
| `https://ibpdi.datacat.org/property/street-name` | 133 | 133 | 130 |
| `https://ibpdi.datacat.org/property/house-number` | 123 | 123 | 82 |

## Sample Data by Class
### `https://ibpdi.datacat.org/class/Address` (Sample Instances)
- `https://example.com/009cb9c7-07d7-48fd-a539-672fbe6f2652`
- `https://example.com/018ac746-2503-4f2e-b681-8cb37d2f0e29`
- `https://example.com/01bea4e6-fee0-40dc-a829-902d901d0a67`
- `https://example.com/02a175f3-9042-44f7-bb2f-a3b9c5997bb8`
- `https://example.com/04baa1d3-3007-41e6-ba27-1b124e96706c`

### `https://ibpdi.datacat.org/class/Building` (Sample Instances)
- `https://example.com/0058c837-25b9-4345-9cc1-ec056f456052`
- `https://example.com/06ccb9e5-e34b-4637-aaa4-d5e345cc7d22`
- `https://example.com/075bf17a-11cc-4609-83ed-5b172ae187f2`
- `https://example.com/0a2add59-d7d2-4802-ac6d-2bbe4f1a4575`
- `https://example.com/0a8cbe14-3d18-4c6c-a778-abade5b4b8b1`

## Property Patterns by Class
### Properties used by `https://ibpdi.datacat.org/class/Address` instances
| Property | Usage Count |
|----------|-------------|
| `http://www.w3.org/1999/02/22-rdf-syntax-ns#type` | 137 |
| `https://ibpdi.datacat.org/class/hasBuilding` | 137 |
| `https://ibpdi.datacat.org/property/country` | 137 |
| `https://ibpdi.datacat.org/property/postal-code` | 137 |
| `https://ibpdi.datacat.org/property/city` | 136 |
| `https://ibpdi.datacat.org/property/street-name` | 133 |
| `https://ibpdi.datacat.org/property/house-number` | 123 |

### Properties used by `https://ibpdi.datacat.org/class/Building` instances
| Property | Usage Count |
|----------|-------------|
| `http://www.w3.org/1999/02/22-rdf-syntax-ns#type` | 137 |
| `https://ibpdi.datacat.org/property/building-code` | 137 |
| `https://ibpdi.datacat.org/property/construction-year` | 137 |
| `https://ibpdi.datacat.org/property/energy-efficiency-class` | 137 |
| `https://ibpdi.datacat.org/property/name` | 137 |
| `https://ibpdi.datacat.org/property/primary-heating-type` | 137 |
| `https://ibpdi.datacat.org/property/primary-type-of-building` | 137 |
| `https://ibpdi.datacat.org/property/valid-from` | 137 |
| `https://ibpdi.datacat.org/property/parking-spaces` | 136 |

## Value Type Analysis
### Value Types for `http://www.w3.org/1999/02/22-rdf-syntax-ns#type`
- **URI**: 274 occurrences

### Value Types for `https://ibpdi.datacat.org/property/energy-efficiency-class`
- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences

### Value Types for `https://ibpdi.datacat.org/property/building-code`
- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences

### Value Types for `https://ibpdi.datacat.org/property/postal-code`
- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences

### Value Types for `https://ibpdi.datacat.org/property/construction-year`
- **Literal: http://www.w3.org/2001/XMLSchema#string**: 137 occurrences

## Relationship Patterns
| Subject Class | Property | Object Class | Count |
|---------------|----------|--------------|-------|
| `https://ibpdi.datacat.org/class/Address` | `https://ibpdi.datacat.org/class/hasBuilding` | `https://ibpdi.datacat.org/class/Building` | 137 |

## Sample Literal Values
### Sample values for `https://ibpdi.datacat.org/property/energy-efficiency-class`
- `"k"`
- `"E"`
- `"A"`
- `"C"`
- `"D"`
- `"B"`
- `"F"`

### Sample values for `https://ibpdi.datacat.org/property/building-code`
- `"1000000094"`
- `"1000000021"`
- `"1000000017"`
- `"1000000135"`
- `"1000000099"`
- `"1000000132"`
- `"1000000092"`
- `"1000000074"`
- `"1000000034"`
- `"1000000028"`

## Summary and Query Recommendations

### Key Insights:
- **2** distinct classes found
- **15** distinct properties used
- **1** class-to-class relationship patterns
- **32** namespace prefixes defined

### Common Query Patterns:
- **Find all instances of main class**: `SELECT ?instance WHERE { ?instance a <https://ibpdi.datacat.org/class/Address> . }`
- **Find all values of main property**: `SELECT ?subject ?value WHERE { ?subject <http://www.w3.org/1999/02/22-rdf-syntax-ns#type> ?value . }`
- **Main relationship pattern**: `SELECT ?s ?o WHERE { ?s a <https://ibpdi.datacat.org/class/Address> . ?s <https://ibpdi.datacat.org/class/hasBuilding> ?o . ?o a <https://ibpdi.datacat.org/class/Building> . }`

---
*Analysis completed successfully.*