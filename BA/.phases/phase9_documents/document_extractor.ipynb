{"cells": [{"cell_type": "markdown", "id": "8824b58c", "metadata": {}, "source": ["# Simple PDF Document Extractor with Docling\n", "\n", "This notebook demonstrates how to extract text and content from PDF documents using the Docling library."]}, {"cell_type": "code", "execution_count": 1, "id": "37e13347", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: docling in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (2.54.0)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2.11.7)\n", "Requirement already satisfied: docling-core<3.0.0,>=2.48.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core[chunking]<3.0.0,>=2.48.2->docling) (2.48.2)\n", "Requirement already satisfied: docling-parse<5.0.0,>=4.4.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (4.5.0)\n", "Requirement already satisfied: docling-ibm-models<4,>=3.9.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (3.9.1)\n", "Requirement already satisfied: filetype<2.0.0,>=1.2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.2.0)\n", "Requirement already satisfied: pypdfium2!=4.30.1,<5.0.0,>=4.30.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (4.30.0)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.3.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2.10.1)\n", "Requirement already satisfied: huggingface_hub<1,>=0.23 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (0.34.4)\n", "Requirement already satisfied: requests<3.0.0,>=2.32.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2.32.5)\n", "Requirement already satisfied: easyocr<2.0,>=1.7 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.7.2)\n", "Requirement already satisfied: certifi>=2024.7.4 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2025.8.3)\n", "Requirement already satisfied: rtree<2.0.0,>=1.3.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.4.1)\n", "Requirement already satisfied: typer<0.17.0,>=0.12.5 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (0.16.1)\n", "Requirement already satisfied: python-docx<2.0.0,>=1.1.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.2.0)\n", "Requirement already satisfied: python-pptx<2.0.0,>=1.0.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.0.2)\n", "Requirement already satisfied: beautifulsoup4<5.0.0,>=4.12.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (4.13.5)\n", "Requirement already satisfied: pandas<3.0.0,>=2.1.4 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2.3.2)\n", "Requirement already satisfied: marko<3.0.0,>=2.1.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2.2.0)\n", "Requirement already satisfied: openpyxl<4.0.0,>=3.1.5 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (3.1.5)\n", "Requirement already satisfied: lxml<6.0.0,>=4.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (5.4.0)\n", "Requirement already satisfied: pillow<12.0.0,>=10.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (11.3.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.65.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (4.67.1)\n", "Requirement already satisfied: pluggy<2.0.0,>=1.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.6.0)\n", "Requirement already satisfied: pylatexenc<3.0,>=2.10 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2.10)\n", "Requirement already satisfied: scipy<2.0.0,>=1.6.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.16.2)\n", "Requirement already satisfied: accelerate<2,>=1.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (1.10.1)\n", "Requirement already satisfied: polyfactory>=2.22.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling) (2.22.2)\n", "Requirement already satisfied: numpy<3.0.0,>=1.17 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from accelerate<2,>=1.0.0->docling) (2.2.6)\n", "Requirement already satisfied: packaging>=20.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from accelerate<2,>=1.0.0->docling) (25.0)\n", "Requirement already satisfied: psutil in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from accelerate<2,>=1.0.0->docling) (7.1.0)\n", "Requirement already satisfied: pyyaml in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from accelerate<2,>=1.0.0->docling) (6.0.2)\n", "Requirement already satisfied: torch>=2.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from accelerate<2,>=1.0.0->docling) (2.8.0)\n", "Requirement already satisfied: safetensors>=0.4.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from accelerate<2,>=1.0.0->docling) (0.6.2)\n", "Requirement already satisfied: soupsieve>1.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from beautifulsoup4<5.0.0,>=4.12.3->docling) (2.8)\n", "Requirement already satisfied: typing-extensions>=4.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from beautifulsoup4<5.0.0,>=4.12.3->docling) (4.15.0)\n", "Requirement already satisfied: jsonschema<5.0.0,>=4.16.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (4.25.1)\n", "Requirement already satisfied: jsonref<2.0.0,>=1.1.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (1.1.0)\n", "Requirement already satisfied: tabulate<0.10.0,>=0.9.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.9.0)\n", "Requirement already satisfied: latex2mathml<4.0.0,>=3.77.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (3.78.1)\n", "Requirement already satisfied: semchunk<3.0.0,>=2.2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core[chunking]<3.0.0,>=2.48.2->docling) (2.2.2)\n", "Requirement already satisfied: transformers<5.0.0,>=4.34.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core[chunking]<3.0.0,>=2.48.2->docling) (4.56.2)\n", "Requirement already satisfied: torchvision<1,>=0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-ibm-models<4,>=3.9.1->docling) (0.23.0)\n", "Requirement already satisfied: jsonlines<4.0.0,>=3.1.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-ibm-models<4,>=3.9.1->docling) (3.1.0)\n", "Requirement already satisfied: opencv-python-headless<5.0.0.0,>=4.6.0.66 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-ibm-models<4,>=3.9.1->docling) (4.12.0.88)\n", "Requirement already satisfied: scikit-image in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (0.25.2)\n", "Requirement already satisfied: python-bidi in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (0.6.6)\n", "Requirement already satisfied: Shapely in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (2.1.1)\n", "Requirement already satisfied: pyclipper in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (1.3.0.post6)\n", "Requirement already satisfied: ninja in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (1.13.0)\n", "Requirement already satisfied: soupsieve>1.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from beautifulsoup4<5.0.0,>=4.12.3->docling) (2.8)\n", "Requirement already satisfied: typing-extensions>=4.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from beautifulsoup4<5.0.0,>=4.12.3->docling) (4.15.0)\n", "Requirement already satisfied: jsonschema<5.0.0,>=4.16.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (4.25.1)\n", "Requirement already satisfied: jsonref<2.0.0,>=1.1.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (1.1.0)\n", "Requirement already satisfied: tabulate<0.10.0,>=0.9.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.9.0)\n", "Requirement already satisfied: latex2mathml<4.0.0,>=3.77.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (3.78.1)\n", "Requirement already satisfied: semchunk<3.0.0,>=2.2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core[chunking]<3.0.0,>=2.48.2->docling) (2.2.2)\n", "Requirement already satisfied: transformers<5.0.0,>=4.34.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-core[chunking]<3.0.0,>=2.48.2->docling) (4.56.2)\n", "Requirement already satisfied: torchvision<1,>=0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-ibm-models<4,>=3.9.1->docling) (0.23.0)\n", "Requirement already satisfied: jsonlines<4.0.0,>=3.1.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-ibm-models<4,>=3.9.1->docling) (3.1.0)\n", "Requirement already satisfied: opencv-python-headless<5.0.0.0,>=4.6.0.66 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from docling-ibm-models<4,>=3.9.1->docling) (4.12.0.88)\n", "Requirement already satisfied: scikit-image in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (0.25.2)\n", "Requirement already satisfied: python-bidi in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (0.6.6)\n", "Requirement already satisfied: Shapely in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (2.1.1)\n", "Requirement already satisfied: pyclipper in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (1.3.0.post6)\n", "Requirement already satisfied: ninja in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from easyocr<2.0,>=1.7->docling) (1.13.0)\n", "Requirement already satisfied: filelock in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from huggingface_hub<1,>=0.23->docling) (3.19.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from huggingface_hub<1,>=0.23->docling) (2025.7.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from huggingface_hub<1,>=0.23->docling) (1.1.9)\n", "Requirement already satisfied: attrs>=19.2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonlines<4.0.0,>=3.1.0->docling-ibm-models<4,>=3.9.1->docling) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.16.0->docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.16.0->docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.16.0->docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.27.1)\n", "Requirement already satisfied: filelock in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from huggingface_hub<1,>=0.23->docling) (3.19.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from huggingface_hub<1,>=0.23->docling) (2025.7.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from huggingface_hub<1,>=0.23->docling) (1.1.9)\n", "Requirement already satisfied: attrs>=19.2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonlines<4.0.0,>=3.1.0->docling-ibm-models<4,>=3.9.1->docling) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.16.0->docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.16.0->docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jsonschema<5.0.0,>=4.16.0->docling-core<3.0.0,>=2.48.2->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.27.1)\n", "Requirement already satisfied: et-xmlfile in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from openpyxl<4.0.0,>=3.1.5->docling) (2.0.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pandas<3.0.0,>=2.1.4->docling) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pandas<3.0.0,>=2.1.4->docling) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pandas<3.0.0,>=2.1.4->docling) (2025.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->docling) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->docling) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->docling) (0.4.1)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic-settings<3.0.0,>=2.3.0->docling) (1.1.1)\n", "Requirement already satisfied: et-xmlfile in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from openpyxl<4.0.0,>=3.1.5->docling) (2.0.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pandas<3.0.0,>=2.1.4->docling) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pandas<3.0.0,>=2.1.4->docling) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pandas<3.0.0,>=2.1.4->docling) (2025.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->docling) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->docling) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.0.0->docling) (0.4.1)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from pydantic-settings<3.0.0,>=2.3.0->docling) (1.1.1)\n", "Requirement already satisfied: XlsxWriter>=0.5.7 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from python-pptx<2.0.0,>=1.0.2->docling) (3.2.9)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from requests<3.0.0,>=2.32.2->docling) (3.4.3)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from requests<3.0.0,>=2.32.2->docling) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from requests<3.0.0,>=2.32.2->docling) (2.5.0)\n", "Requirement already satisfied: mpire[dill] in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (2.10.2)\n", "Requirement already satisfied: setuptools in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (80.9.0)\n", "Requirement already satisfied: sympy>=1.13.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (1.14.0)\n", "Requirement already satisfied: networkx in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.5)\n", "Requirement already satisfied: jinja2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.1.4)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.8.93 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.93)\n", "Requirement already satisfied: XlsxWriter>=0.5.7 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from python-pptx<2.0.0,>=1.0.2->docling) (3.2.9)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from requests<3.0.0,>=2.32.2->docling) (3.4.3)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from requests<3.0.0,>=2.32.2->docling) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from requests<3.0.0,>=2.32.2->docling) (2.5.0)\n", "Requirement already satisfied: mpire[dill] in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (2.10.2)\n", "Requirement already satisfied: setuptools in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (80.9.0)\n", "Requirement already satisfied: sympy>=1.13.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (1.14.0)\n", "Requirement already satisfied: networkx in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.5)\n", "Requirement already satisfied: jinja2 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.1.4)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.8.93 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.93)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.8.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.90)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.8.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.90)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.10.2.21 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (9.10.2.21)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.8.4.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.4.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.3.3.83 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (11.3.3.83)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.9.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (10.3.9.90)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.7.3.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (11.7.3.90)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.5.8.93 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.5.8.93)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.7.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (0.7.1)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.27.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (2.27.3)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.8.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.90)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.8.93 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.93)\n", "Requirement already satisfied: nvidia-cufile-cu12==1.13.1.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (1.13.1.3)\n", "Requirement already satisfied: triton==3.4.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.4.0)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.8.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.90)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.8.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.90)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.10.2.21 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (9.10.2.21)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.8.4.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.4.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.3.3.83 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (11.3.3.83)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.9.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (10.3.9.90)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.7.3.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (11.7.3.90)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.5.8.93 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.5.8.93)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.7.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (0.7.1)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.27.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (2.27.3)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.8.90 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.90)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.8.93 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (12.8.93)\n", "Requirement already satisfied: nvidia-cufile-cu12==1.13.1.3 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (1.13.1.3)\n", "Requirement already satisfied: triton==3.4.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.4.0)\n", "Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from transformers<5.0.0,>=4.34.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (2025.8.29)\n", "Requirement already satisfied: tokenizers<=0.23.0,>=0.22.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from transformers<5.0.0,>=4.34.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.22.0)\n", "Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from transformers<5.0.0,>=4.34.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (2025.8.29)\n", "Requirement already satisfied: tokenizers<=0.23.0,>=0.22.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from transformers<5.0.0,>=4.34.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.22.0)\n", "Requirement already satisfied: click>=8.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from typer<0.17.0,>=0.12.5->docling) (8.2.1)\n", "Requirement already satisfied: shellingham>=1.3.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from typer<0.17.0,>=0.12.5->docling) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from typer<0.17.0,>=0.12.5->docling) (14.1.0)\n", "Requirement already satisfied: click>=8.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from typer<0.17.0,>=0.12.5->docling) (8.2.1)\n", "Requirement already satisfied: shellingham>=1.3.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from typer<0.17.0,>=0.12.5->docling) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from typer<0.17.0,>=0.12.5->docling) (14.1.0)\n", "Requirement already satisfied: faker>=5.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from polyfactory>=2.22.2->docling) (37.8.0)\n", "Requirement already satisfied: six>=1.5 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from python-dateutil>=2.8.2->pandas<3.0.0,>=2.1.4->docling) (1.17.0)\n", "Requirement already satisfied: faker>=5.0.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from polyfactory>=2.22.2->docling) (37.8.0)\n", "Requirement already satisfied: six>=1.5 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from python-dateutil>=2.8.2->pandas<3.0.0,>=2.1.4->docling) (1.17.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from rich>=10.11.0->typer<0.17.0,>=0.12.5->docling) (4.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from rich>=10.11.0->typer<0.17.0,>=0.12.5->docling) (2.19.2)\n", "Requirement already satisfied: mdurl~=0.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<0.17.0,>=0.12.5->docling) (0.1.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from rich>=10.11.0->typer<0.17.0,>=0.12.5->docling) (4.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from rich>=10.11.0->typer<0.17.0,>=0.12.5->docling) (2.19.2)\n", "Requirement already satisfied: mdurl~=0.1 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<0.17.0,>=0.12.5->docling) (0.1.2)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from sympy>=1.13.3->torch>=2.0.0->accelerate<2,>=1.0.0->docling) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jinja2->torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.0.2)\n", "Requirement already satisfied: multiprocess>=0.70.15 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from mpire[dill]->semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.70.18)\n", "Requirement already satisfied: dill>=0.4.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from multiprocess>=0.70.15->mpire[dill]->semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.4.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from sympy>=1.13.3->torch>=2.0.0->accelerate<2,>=1.0.0->docling) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from jinja2->torch>=2.0.0->accelerate<2,>=1.0.0->docling) (3.0.2)\n", "Requirement already satisfied: multiprocess>=0.70.15 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from mpire[dill]->semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.70.18)\n", "Requirement already satisfied: dill>=0.4.0 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from multiprocess>=0.70.15->mpire[dill]->semchunk<3.0.0,>=2.2.0->docling-core[chunking]<3.0.0,>=2.48.2->docling) (0.4.0)\n", "Requirement already satisfied: imageio!=2.35.0,>=2.33 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from scikit-image->easyocr<2.0,>=1.7->docling) (2.37.0)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from scikit-image->easyocr<2.0,>=1.7->docling) (2025.9.20)\n", "Requirement already satisfied: lazy-loader>=0.4 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from scikit-image->easyocr<2.0,>=1.7->docling) (0.4)\n", "Requirement already satisfied: imageio!=2.35.0,>=2.33 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from scikit-image->easyocr<2.0,>=1.7->docling) (2.37.0)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from scikit-image->easyocr<2.0,>=1.7->docling) (2025.9.20)\n", "Requirement already satisfied: lazy-loader>=0.4 in /home/<USER>/.pyenv/versions/3.13.7/lib/python3.13/site-packages (from scikit-image->easyocr<2.0,>=1.7->docling) (0.4)\n"]}], "source": ["# Install docling - only need to run this once\n", "!pip install docling"]}, {"cell_type": "code", "execution_count": 2, "id": "e86b9f21", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["# Import necessary libraries\n", "from docling.document_converter import DocumentConverter\n", "import os\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 3, "id": "0569a994", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ PDF file not found: assets/example.pdf\n", "Current directory contents:\n", "['assets', 'document_extractor.ipynb', 'extracted_content']\n"]}], "source": ["# Define the path to the example PDF\n", "pdf_path = \"assets/example.pdf\"\n", "\n", "# Check if the file exists\n", "if os.path.exists(pdf_path):\n", "    print(f\"✅ PDF file found: {pdf_path}\")\n", "    print(f\"📄 File size: {os.path.getsize(pdf_path) / 1024:.2f} KB\")\n", "else:\n", "    print(f\"❌ PDF file not found: {pdf_path}\")\n", "    print(\"Current directory contents:\")\n", "    print(os.listdir(\".\"))"]}, {"cell_type": "code", "execution_count": 4, "id": "72b0590c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📚 DocumentConverter initialized successfully!\n", "🔧 Ready to extract content from PDF documents\n"]}], "source": ["# Initialize the DocumentConverter\n", "converter = DocumentConverter()\n", "\n", "print(\"📚 DocumentConverter initialized successfully!\")\n", "print(\"🔧 Ready to extract content from PDF documents\")"]}, {"cell_type": "code", "execution_count": 5, "id": "738e4e90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Processing PDF document...\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'assets/example.pdf'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mValidationError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/docling_core/utils/file.py:70\u001b[39m, in \u001b[36mresolve_source_to_stream\u001b[39m\u001b[34m(source, headers)\u001b[39m\n\u001b[32m     69\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m70\u001b[39m     http_url: AnyHttpUrl = \u001b[43mTypeAdapter\u001b[49m\u001b[43m(\u001b[49m\u001b[43mAnyHttpUrl\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalidate_python\u001b[49m\u001b[43m(\u001b[49m\u001b[43msource\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     72\u001b[39m     \u001b[38;5;66;03m# make all header keys lower case\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/pydantic/type_adapter.py:421\u001b[39m, in \u001b[36mTypeAdapter.validate_python\u001b[39m\u001b[34m(self, object, strict, from_attributes, context, experimental_allow_partial, by_alias, by_name)\u001b[39m\n\u001b[32m    416\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m PydanticUserError(\n\u001b[32m    417\u001b[39m         \u001b[33m'\u001b[39m\u001b[33mAt least one of `by_alias` or `by_name` must be set to True.\u001b[39m\u001b[33m'\u001b[39m,\n\u001b[32m    418\u001b[39m         code=\u001b[33m'\u001b[39m\u001b[33mvalidate-by-alias-and-name-false\u001b[39m\u001b[33m'\u001b[39m,\n\u001b[32m    419\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m421\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mvalidator\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalidate_python\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    422\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43mobject\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    423\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstrict\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstrict\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    424\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfrom_attributes\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfrom_attributes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    425\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    426\u001b[39m \u001b[43m    \u001b[49m\u001b[43mallow_partial\u001b[49m\u001b[43m=\u001b[49m\u001b[43mexperimental_allow_partial\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    427\u001b[39m \u001b[43m    \u001b[49m\u001b[43mby_alias\u001b[49m\u001b[43m=\u001b[49m\u001b[43mby_alias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    428\u001b[39m \u001b[43m    \u001b[49m\u001b[43mby_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43mby_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    429\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mValidationError\u001b[39m: 1 validation error for function-wrap[wrap_val()]\n  Input should be a valid URL, relative URL without a base [type=url_parsing, input_value='assets/example.pdf', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.11/v/url_parsing", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m🔄 Processing PDF document...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# Convert the PDF document\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m result = \u001b[43mconverter\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpdf_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      6\u001b[39m document = result.document\n\u001b[32m      8\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m✅ PDF processed successfully!\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/pydantic/_internal/_validate_call.py:39\u001b[39m, in \u001b[36mupdate_wrapper_attributes.<locals>.wrapper_function\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     37\u001b[39m \u001b[38;5;129m@functools\u001b[39m.wraps(wrapped)\n\u001b[32m     38\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwrapper_function\u001b[39m(*args, **kwargs):\n\u001b[32m---> \u001b[39m\u001b[32m39\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapper\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/pydantic/_internal/_validate_call.py:136\u001b[39m, in \u001b[36mValidateCallWrapper.__call__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    133\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m.__pydantic_complete__:\n\u001b[32m    134\u001b[39m     \u001b[38;5;28mself\u001b[39m._create_validators()\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m res = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__pydantic_validator__\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalidate_python\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpydantic_core\u001b[49m\u001b[43m.\u001b[49m\u001b[43mArgsKwargs\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    137\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.__return_pydantic_validator__:\n\u001b[32m    138\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.__return_pydantic_validator__(res)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/docling/document_converter.py:245\u001b[39m, in \u001b[36mDocumentConverter.convert\u001b[39m\u001b[34m(self, source, headers, raises_on_error, max_num_pages, max_file_size, page_range)\u001b[39m\n\u001b[32m    227\u001b[39m \u001b[38;5;129m@validate_call\u001b[39m(config=ConfigDict(strict=\u001b[38;5;28;01mTrue\u001b[39;00m))\n\u001b[32m    228\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconvert\u001b[39m(\n\u001b[32m    229\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    235\u001b[39m     page_range: PageRange = DEFAULT_PAGE_RANGE,\n\u001b[32m    236\u001b[39m ) -> ConversionResult:\n\u001b[32m    237\u001b[39m     all_res = \u001b[38;5;28mself\u001b[39m.convert_all(\n\u001b[32m    238\u001b[39m         source=[source],\n\u001b[32m    239\u001b[39m         raises_on_error=raises_on_error,\n\u001b[32m   (...)\u001b[39m\u001b[32m    243\u001b[39m         page_range=page_range,\n\u001b[32m    244\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m245\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mall_res\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/docling/document_converter.py:268\u001b[39m, in \u001b[36mDocumentConverter.convert_all\u001b[39m\u001b[34m(self, source, headers, raises_on_error, max_num_pages, max_file_size, page_range)\u001b[39m\n\u001b[32m    265\u001b[39m conv_res_iter = \u001b[38;5;28mself\u001b[39m._convert(conv_input, raises_on_error=raises_on_error)\n\u001b[32m    267\u001b[39m had_result = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m268\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mconv_res\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mconv_res_iter\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    269\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhad_result\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\n\u001b[32m    270\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mraises_on_error\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mconv_res\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstatus\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    271\u001b[39m \u001b[43m        \u001b[49m\u001b[43mConversionStatus\u001b[49m\u001b[43m.\u001b[49m\u001b[43mSUCCESS\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    272\u001b[39m \u001b[43m        \u001b[49m\u001b[43mConversionStatus\u001b[49m\u001b[43m.\u001b[49m\u001b[43mPARTIAL_SUCCESS\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    273\u001b[39m \u001b[43m    \u001b[49m\u001b[43m}\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/docling/document_converter.py:318\u001b[39m, in \u001b[36mDocumentConverter._convert\u001b[39m\u001b[34m(self, conv_input, raises_on_error)\u001b[39m\n\u001b[32m    313\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_convert\u001b[39m(\n\u001b[32m    314\u001b[39m     \u001b[38;5;28mself\u001b[39m, conv_input: _DocumentConversionInput, raises_on_error: \u001b[38;5;28mbool\u001b[39m\n\u001b[32m    315\u001b[39m ) -> Iterator[ConversionResult]:\n\u001b[32m    316\u001b[39m     start_time = time.monotonic()\n\u001b[32m--> \u001b[39m\u001b[32m318\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43minput_batch\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunkify\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    319\u001b[39m \u001b[43m        \u001b[49m\u001b[43mconv_input\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdocs\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mformat_to_options\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    320\u001b[39m \u001b[43m        \u001b[49m\u001b[43msettings\u001b[49m\u001b[43m.\u001b[49m\u001b[43mperf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdoc_batch_size\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# pass format_options\u001b[39;49;00m\n\u001b[32m    321\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    322\u001b[39m \u001b[43m        \u001b[49m\u001b[43m_log\u001b[49m\u001b[43m.\u001b[49m\u001b[43minfo\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mGoing to convert document batch...\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    323\u001b[39m \u001b[43m        \u001b[49m\u001b[43mprocess_func\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    324\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_process_document\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mraises_on_error\u001b[49m\u001b[43m=\u001b[49m\u001b[43mraises_on_error\u001b[49m\n\u001b[32m    325\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/docling/utils/utils.py:15\u001b[39m, in \u001b[36mchunkify\u001b[39m\u001b[34m(iterator, chunk_size)\u001b[39m\n\u001b[32m     13\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(iterator, List):\n\u001b[32m     14\u001b[39m     iterator = \u001b[38;5;28miter\u001b[39m(iterator)\n\u001b[32m---> \u001b[39m\u001b[32m15\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mfirst\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43miterator\u001b[49m\u001b[43m:\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Take the first element from the iterator\u001b[39;49;00m\n\u001b[32m     16\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;<PERSON>my<PERSON>\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43mfirst\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mislice\u001b[49m\u001b[43m(\u001b[49m\u001b[43miterator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mchunk_size\u001b[49m\u001b[43m \u001b[49m\u001b[43m-\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m]\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/docling/datamodel/document.py:247\u001b[39m, in \u001b[36m_DocumentConversionInput.docs\u001b[39m\u001b[34m(self, format_options)\u001b[39m\n\u001b[32m    241\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mdocs\u001b[39m(\n\u001b[32m    242\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    243\u001b[39m     format_options: Mapping[InputFormat, \u001b[33m\"\u001b[39m\u001b[33mBaseFormatOption\u001b[39m\u001b[33m\"\u001b[39m],\n\u001b[32m    244\u001b[39m ) -> Iterable[InputDocument]:\n\u001b[32m    245\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m item \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.path_or_stream_iterator:\n\u001b[32m    246\u001b[39m         obj = (\n\u001b[32m--> \u001b[39m\u001b[32m247\u001b[39m             \u001b[43mresolve_source_to_stream\u001b[49m\u001b[43m(\u001b[49m\u001b[43mitem\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    248\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(item, \u001b[38;5;28mstr\u001b[39m)\n\u001b[32m    249\u001b[39m             \u001b[38;5;28;01melse\u001b[39;00m item\n\u001b[32m    250\u001b[39m         )\n\u001b[32m    251\u001b[39m         \u001b[38;5;28mformat\u001b[39m = \u001b[38;5;28mself\u001b[39m._guess_format(obj)\n\u001b[32m    252\u001b[39m         backend: Type[AbstractDocumentBackend]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/dev/BA/.venv/lib/python3.13/site-packages/docling_core/utils/file.py:116\u001b[39m, in \u001b[36mresolve_source_to_stream\u001b[39m\u001b[34m(source, headers)\u001b[39m\n\u001b[32m    114\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    115\u001b[39m     local_path = TypeAdapter(Path).validate_python(source)\n\u001b[32m--> \u001b[39m\u001b[32m116\u001b[39m     stream = BytesIO(\u001b[43mlocal_path\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_bytes\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n\u001b[32m    117\u001b[39m     doc_stream = DocumentStream(name=local_path.name, stream=stream)\n\u001b[32m    118\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ValidationError:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.7/lib/python3.13/pathlib/_abc.py:625\u001b[39m, in \u001b[36mPathBase.read_bytes\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    621\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mread_bytes\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m    622\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    623\u001b[39m \u001b[33;03m    Open the file in bytes mode, read it, and close the file.\u001b[39;00m\n\u001b[32m    624\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m625\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mrb\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m    626\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m f.read()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.7/lib/python3.13/pathlib/_local.py:537\u001b[39m, in \u001b[36mPath.open\u001b[39m\u001b[34m(self, mode, buffering, encoding, errors, newline)\u001b[39m\n\u001b[32m    535\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[32m    536\u001b[39m     encoding = io.text_encoding(encoding)\n\u001b[32m--> \u001b[39m\u001b[32m537\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mio\u001b[49m\u001b[43m.\u001b[49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffering\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnewline\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'assets/example.pdf'"]}], "source": ["# Extract content from the PDF\n", "print(\"🔄 Processing PDF document...\")\n", "\n", "# Convert the PDF document\n", "result = converter.convert(pdf_path)\n", "document = result.document\n", "\n", "print(\"✅ PDF processed successfully!\")\n", "print(f\"📊 Document contains {len(document.pages)} page(s)\")\n", "\n", "# Display basic document information\n", "print(\"\\n📋 Document Information:\")\n", "# Check available attributes\n", "print(f\"• Document type: {type(document).__name__}\")\n", "print(f\"• Pages: {len(document.pages)}\")\n", "\n", "# Get counts of different elements\n", "text_count = len(document.texts) if hasattr(document, 'texts') else 0\n", "table_count = len(document.tables) if hasattr(document, 'tables') else 0  \n", "picture_count = len(document.pictures) if hasattr(document, 'pictures') else 0\n", "\n", "print(f\"• Total elements: {text_count + table_count + picture_count}\")\n", "print(f\"• Text elements: {text_count}\")\n", "print(f\"• Tables: {table_count}\")\n", "print(f\"• Pictures: {picture_count}\")"]}, {"cell_type": "code", "execution_count": null, "id": "a06b74db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📝 Text Content (Markdown format):\n", "==================================================\n", "## Deka-ImmobilienEuropa\n", "\n", "## 'RONDO'\n", "\n", "Barthstraße 12-22\n", "\n", "## 80339 München\n", "\n", "<!-- image -->\n", "\n", "## Eckdaten\n", "\n", "<!-- image -->\n", "\n", "| Lage                                                           |\n", "|----------------------------------------------------------------|\n", "| ■ Münchener Westend                                            |\n", "| ■ S- und U-Bahn Anschluss in ca. 8 Gehminuten                  |\n", "| ■ Sehr gute Anbindung an Mittleren Ring und Autobahnen         |\n", "| ■ Autobahn in ca. 5 Fahrminuten                                |\n", "| ■ Flughafen in ca. 32 Fahrminuten                              |\n", "| ■ Hauptbahnhof in ca. 7 Fahrminuten                            |\n", "| ■ Vielfältige Auswahl erstklassiger Restaurants, Café, Bistros |\n", "| ■ Supermärkte und Gastronomien fußläufig erreichbar            |\n", "\n", "## Stichtag: 31.12.2022\n", "\n", "| Grundstücksgröße in m²            | 14.360,0         |\n", "|-----------------------------------|------------------|\n", "| Baujahr (Umbaujahr) / Erwerbsjahr | 2001 (-) / 2001  |\n", "| Halteform                         | Direktinvestment |\n", "| Gutachterlicher Verkehrswert*     | 120.095.000 EUR  |\n", "| Ø Restlaufzeit der Mietverträge** | 2,81 Jahre       |\n", "| Marktüblicher Jahresrohertrag     | 5.480.160 EUR    |\n", "\n", "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.\n", "\n", "<!-- image -->\n", "\n", "## Auslaufende Mietverträge**\n", "\n", "## unbefristet\n", "\n", "|         | m²    | in %(m²)   | in EUR p.a.   |\n", "|---------|-------|------------|---------------|\n", "| 2022    | 3.364 | 12%        | 626.545       |\n", "| 2023    | 2.120 | 8%         | 502.946       |\n", "| 2024    | 4.263 | 16%        | 873.004       |\n", "| 2025    | 5.871 | 22%        | 1.509.277     |\n", "| 2026    |       |            |               |\n", "| 2027    | 8.167 | 30%        | 1.508.853     |\n", "| 2028    | 3.423 | 13%        | 698.129       |\n", "| 2029    |       |            |               |\n", "| ab 2030 |       |            |               |\n", "\n", "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die\n", "\n", "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt.\n", "\n", "## Objektbeschreibung\n", "\n", "##  zurück zum Inhaltsverzeichnis\n", "\n", "## Fläche nach Vermietungssituation\n", "\n", "<!-- image -->\n", "\n", "| Mietverträge      |     m² | in%   |\n", "|-------------------|--------|-------|\n", "| Mietfläche        | 28.39  | 100%  |\n", "| davon vermietet   | 27.208 | 96%   |\n", "| da<PERSON> |  1.182 | 4%    |\n", "| <PERSON><PERSON><PERSON>     | 26     |       |\n", "\n", "| ■ 5-g<PERSON><PERSON><PERSON>ge, moderne Büroimmobilie                         |\n", "|----------------------------------------------------------------|\n", "| ■ Moderne Architektur mit geschwungener Spiegelglasfassade     |\n", "| ■ Bepflanzte Innenhöfe und mietereigene Dachterrassen          |\n", "| ■ G<PERSON><PERSON> Anzahl von Tiefgaragen- und Außenstellplätze           |\n", "| ■ 2-geschossige repräsentative Foyers mit Natursteinböden      |\n", "| ■ 10 Personenaufzüge u. 2 Lastenaufzüge für 6 Eingangsbereiche |\n", "| ■ <PERSON><PERSON><PERSON><PERSON>, zugängliche Kantine mit Außenbereich             |\n", "| ■ Teilweise gekühlte Mietflächen bzw. nachrüstbar              |\n", "| ■ Außenliegender automatischer Sonnenschutz                    |\n", "| ■ Hohlraumböden in Büros und Doppelböden in Flurbereichen      |\n", "| ■ Lichte Raumhöhe Regelgeschoß 2,95m/EG 3,20m, <PERSON><PERSON><PERSON>ß 1,35m |\n", "| ■ Sehr gute Flächeneffizienz                                   |\n", "| ■ Flexible Raumaufteilung für individuelle Büros möglich       |\n", "\n", "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt.\n", "\n", "Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im\n", "\n", "Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und\n", "\n", "Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche\n", "\n", "Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die\n", "\n", "Informationen stellen weder ein <PERSON>, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als\n", "\n", "Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022)\n", "\n", "## Nachhaltigkeitsangaben\n", "\n", "<!-- image -->\n", "\n", "| ■ BREEAM DE: good                                 |\n", "|---------------------------------------------------|\n", "| ■ Primärenergiebedarf (in kWh/m² und Jahr): 268,5 |\n", "| ■ Rating gemäß Energieausweis: D                  |\n", "\n", "## <PERSON><PERSON><PERSON><PERSON>nte Ausgangsdaten laut aktuellen Verkehrswertgutachten\n", "\n", "| Mietfläche in m²                                        | 28.389          |\n", "|---------------------------------------------------------|-----------------|\n", "| TG-Stellplätze                                          | 324 St.         |\n", "| Außenstellplätze                                        | 28 St.          |\n", "| Übliche Gesamtnutzungsdauer                             | 70 Jahre        |\n", "| Restnutzungsdauer                                       | 48,58 Jahre     |\n", "| Marktüblicher Jahresrohertrag                           | 5.480.160 EUR   |\n", "| Nicht umlagefähige Betriebskosten                       | 27.401 EUR      |\n", "| Verwaltungskosten                                       | 82.202 EUR      |\n", "| Instandhaltungskosten                                   | 297.400 EUR     |\n", "| Mietausfallwagnis                                       | 219.206 EUR     |\n", "| Bewirtschaftungskosten gesamt                           | 626.209 EUR     |\n", "| Jahresreinertrag                                        | 4.853.951 EUR   |\n", "| Bodenwert bzw. Bodenwertanteil des Erbbaurechts         | 64.620.000 EUR  |\n", "| Wert der baulichen Anlagen                              | 53.790.000 EUR  |\n", "| Verkehrswert *                                          | 120.095.000 EUR |\n", "| Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK | 23,94           |\n", "| Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK | 21,20           |\n", "| Bodenwertanteil am Verkehrswert                         | 55,62%          |\n", "| Verkehrswert pro m² Mietfläche                          | 4.092,78 EUR    |\n", "\n", "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.\n", "\n", "Mehr Informationen zum Objekt finden Sie unter www.rondo-muenchen.de.\n", "\n", "##  zurück zum Inhaltsverzeichnis\n", "\n", "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt.\n", "\n", "Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit\n", "\n", "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien\n", "==================================================\n"]}], "source": ["# Extract text content as markdown\n", "markdown_content = document.export_to_markdown()\n", "\n", "print(\"📝 Text Content (Markdown format):\")\n", "print(\"=\" * 50)\n", "print(markdown_content)\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "id": "8ffa1dc0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📑 Individual Text Elements:\n", "==================================================\n", "\n", "🔹 Text Element 1:\n", "   Content: Deka-ImmobilienEuropa\n", "   Label: section_header\n", "\n", "🔹 Text Element 2:\n", "   Content: 'RONDO'\n", "   Label: section_header\n", "\n", "🔹 Text Element 3:\n", "   Content: Barthstraße 12-22\n", "   Label: text\n", "\n", "🔹 Text Element 4:\n", "   Content: 80339 München\n", "   Label: section_header\n", "\n", "🔹 Text Element 5:\n", "   Content: <PERSON><PERSON><PERSON><PERSON>\n", "   Label: section_header\n", "\n", "... and 57 more text elements\n", "==================================================\n"]}], "source": ["# Extract and display individual text elements\n", "print(\"📑 Individual Text Elements:\")\n", "print(\"=\" * 50)\n", "\n", "for i, text_element in enumerate(document.texts[:5]):  # Show first 5 text elements\n", "    print(f\"\\n🔹 Text Element {i+1}:\")\n", "    print(f\"   Content: {text_element.text[:100]}{'...' if len(text_element.text) > 100 else ''}\")\n", "    print(f\"   Label: {text_element.label}\")\n", "\n", "if len(document.texts) > 5:\n", "    print(f\"\\n... and {len(document.texts) - 5} more text elements\")\n", "\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "id": "12be42c0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-09-22 23:57:57,514 - WARNING - Usage of TableItem.export_to_markdown() without `doc` argument is deprecated.\n", "2025-09-22 23:57:57,519 - WARNING - Usage of TableItem.export_to_markdown() without `doc` argument is deprecated.\n", "2025-09-22 23:57:57,523 - WARNING - Usage of TableItem.export_to_markdown() without `doc` argument is deprecated.\n", "2025-09-22 23:57:57,526 - WARNING - Usage of TableItem.export_to_markdown() without `doc` argument is deprecated.\n", "2025-09-22 23:57:57,528 - WARNING - Usage of TableItem.export_to_markdown() without `doc` argument is deprecated.\n", "2025-09-22 23:57:57,530 - WARNING - Usage of TableItem.export_to_markdown() without `doc` argument is deprecated.\n", "2025-09-22 23:57:57,535 - WARNING - Usage of TableItem.export_to_markdown() without `doc` argument is deprecated.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📊 Tables Found:\n", "==================================================\n", "\n", "🔹 Table 1:\n", "   Available attributes: ['add_annotation', 'annotations', 'caption_text', 'captions', 'children']...\n", "   Markdown preview: | Lage                                                           |\n", "|----------------------------------------------------------------|\n", "| ■ Münchener Westend                                            |...\n", "\n", "🔹 Table 2:\n", "   Available attributes: ['add_annotation', 'annotations', 'caption_text', 'captions', 'children']...\n", "   Markdown preview: | Grundstücksgröße in m²            | 14.360,0         |\n", "|-----------------------------------|------------------|\n", "| Baujahr (Umbaujahr) / Erwerbsjahr | 2001 (-) / 2001  |\n", "| Halteform                  ...\n", "\n", "🔹 Table 3:\n", "   Available attributes: ['add_annotation', 'annotations', 'caption_text', 'captions', 'children']...\n", "   Markdown preview: |         | m²    | in %(m²)   | in EUR p.a.   |\n", "|---------|-------|------------|---------------|\n", "| 2022    | 3.364 | 12%        | 626.545       |\n", "| 2023    | 2.120 | 8%         | 502.946       |\n", "| 20...\n", "\n", "🔹 Table 4:\n", "   Available attributes: ['add_annotation', 'annotations', 'caption_text', 'captions', 'children']...\n", "   Markdown preview: | Mietverträge      |     m² | in%   |\n", "|-------------------|--------|-------|\n", "| Mietfläche        | 28.39  | 100%  |\n", "| davon vermietet   | 27.208 | 96%   |\n", "| da<PERSON> |  1.182 | 4%    |\n", "| Anz...\n", "\n", "🔹 Table 5:\n", "   Available attributes: ['add_annotation', 'annotations', 'caption_text', 'captions', 'children']...\n", "   Markdown preview: | ■ 5-g<PERSON><PERSON><PERSON><PERSON>, moderne Büroimmobilie                         |\n", "|----------------------------------------------------------------|\n", "| ■ Moderne Architektur mit geschwungener Spiegelglasfassade     |...\n", "\n", "🔹 Table 6:\n", "   Available attributes: ['add_annotation', 'annotations', 'caption_text', 'captions', 'children']...\n", "   Markdown preview: | ■ BREEAM DE: good                                 |\n", "|---------------------------------------------------|\n", "| ■ Primärenergiebedarf (in kWh/m² und Jahr): 268,5 |\n", "| ■ Rating gemäß Energieausweis: D    ...\n", "\n", "🔹 Table 7:\n", "   Available attributes: ['add_annotation', 'annotations', 'caption_text', 'captions', 'children']...\n", "   Markdown preview: | Mietfläche in m²                                        | 28.389          |\n", "|---------------------------------------------------------|-----------------|\n", "| TG-Stellplätze                            ...\n", "==================================================\n"]}], "source": ["# Extract tables if any exist\n", "if document.tables:\n", "    print(\"📊 Tables Found:\")\n", "    print(\"=\" * 50)\n", "    \n", "    for i, table in enumerate(document.tables):\n", "        print(f\"\\n🔹 Table {i+1}:\")\n", "        \n", "        # Check available attributes\n", "        table_attrs = [attr for attr in dir(table) if not attr.startswith('_')]\n", "        print(f\"   Available attributes: {table_attrs[:5]}...\")  # Show first 5 attributes\n", "        \n", "        # Try to get table text content\n", "        if hasattr(table, 'text') and table.text:\n", "            print(f\"   Content preview: {table.text[:100]}{'...' if len(table.text) > 100 else ''}\")\n", "        \n", "        # Try to export as markdown if available\n", "        if hasattr(table, 'export_to_markdown'):\n", "            try:\n", "                table_md = table.export_to_markdown()\n", "                print(f\"   Markdown preview: {table_md[:200]}{'...' if len(table_md) > 200 else ''}\")\n", "            except Exception as e:\n", "                print(f\"   Could not export to markdown: {e}\")\n", "                \n", "else:\n", "    print(\"📊 No tables found in the document\")\n", "\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "id": "ff06fb4d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Content saved to:\n", "   📄 Markdown: extracted_content/extracted_content.md\n", "   📄 JSON: extracted_content/extracted_content.json\n", "   📁 Output directory: extracted_content\n", "\n", "📁 Files in extracted_content:\n", "   • extracted_content.json (210.54 KB)\n", "   • extracted_content.md (9.31 KB)\n"]}], "source": ["# Save extracted content to files\n", "output_dir = \"extracted_content\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Save as markdown\n", "markdown_file = os.path.join(output_dir, \"extracted_content.md\")\n", "with open(markdown_file, 'w', encoding='utf-8') as f:\n", "    f.write(markdown_content)\n", "\n", "# Save as JSON for structured access\n", "json_content = document.export_to_dict()\n", "import json\n", "json_file = os.path.join(output_dir, \"extracted_content.json\")\n", "with open(json_file, 'w', encoding='utf-8') as f:\n", "    json.dump(json_content, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"💾 Content saved to:\")\n", "print(f\"   📄 Markdown: {markdown_file}\")\n", "print(f\"   📄 JSON: {json_file}\")\n", "print(f\"   📁 Output directory: {output_dir}\")\n", "\n", "# Display directory contents\n", "print(f\"\\n📁 Files in {output_dir}:\")\n", "for file in os.listdir(output_dir):\n", "    file_path = os.path.join(output_dir, file)\n", "    size_kb = os.path.getsize(file_path) / 1024\n", "    print(f\"   • {file} ({size_kb:.2f} KB)\")"]}, {"cell_type": "markdown", "id": "5425adf8", "metadata": {}, "source": ["## Summary\n", "\n", "This simple PDF document extractor demonstrates the core capabilities of Docling:\n", "\n", "1. **Easy Setup**: Just install docling with `pip install docling`\n", "2. **Simple API**: Initialize `DocumentConverter()` and call `convert()`\n", "3. **Multiple Formats**: Export to Markdown, JSON, or access structured data\n", "4. **Rich Content**: Extracts text, tables, images, and preserves document structure\n", "5. **Metadata**: Provides information about document elements and their types\n", "\n", "### Key Features Used:\n", "- **Text Extraction**: All text content with structure preservation\n", "- **Table Detection**: Automatic table extraction with structure\n", "- **Markdown Export**: Clean, readable markdown format\n", "- **JSON Export**: Structured data for programmatic access\n", "- **Element Analysis**: Individual text elements with labels and metadata\n", "\n", "### Next Steps:\n", "- Try with different PDF documents\n", "- Explore advanced options like OCR settings\n", "- Use table structure recognition for complex tables\n", "- Integrate with document processing pipelines"]}, {"cell_type": "markdown", "id": "adef03fd", "metadata": {}, "source": ["## Batch Process All PDFs\n", "\n", "Process every `.pdf` under the `assets/` folder and store results in `extracted_content/<pdf-stem>/` containing:\n", "- The original PDF copy\n", "- Extracted Markdown (`extracted_content.md`)\n", "- Extracted JSON (`extracted_content.json`)"]}, {"cell_type": "code", "execution_count": 6, "id": "f1e4d706", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:37:07,346 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:37:07,401 - INFO - Going to convert document batch...\n", "2025-09-23 10:37:07,403 - INFO - Initializing pipeline for StandardPdfPipeline with options hash e647edf348883bed75367b22fbe60347\n", "2025-09-23 10:37:07,417 - INFO - Loading plugin 'docling_defaults'\n", "2025-09-23 10:37:07,421 - INFO - Registered picture descriptions: ['vlm', 'api']\n", "2025-09-23 10:37:07,435 - INFO - Loading plugin 'docling_defaults'\n", "2025-09-23 10:37:07,442 - INFO - Registered ocr engines: ['easyocr', 'ocrmac', 'rapidocr', 'tesserocr', 'tesseract']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Found 137 PDF(s) under assets\n", "\n", "==============================\n", "[1/137] Processing: 01_Objekt_1.pdf\n", "Output dir: extracted_content/01_Objekt_1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:37:07,645 - INFO - Accelerator device: 'cpu'\n", "2025-09-23 10:37:12,283 - INFO - Accelerator device: 'cpu'\n", "2025-09-23 10:37:14,789 - INFO - Accelerator device: 'cpu'\n", "2025-09-23 10:37:15,603 - INFO - Processing document 01_Objekt_1.pdf\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:37:37,885 - INFO - Finished converting document 01_Objekt_1.pdf in 28.90 sec.\n", "2025-09-23 10:37:37,921 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:37:37,924 - INFO - Going to convert document batch...\n", "2025-09-23 10:37:37,925 - INFO - Processing document 02_Objekt_2.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 01_Objekt_1.pdf (pages: 2)\n", "\n", "==============================\n", "[2/137] Processing: 02_Objekt_2.pdf\n", "Output dir: extracted_content/02_Objekt_2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:37:58,829 - INFO - Finished converting document 02_Objekt_2.pdf in 19.26 sec.\n", "2025-09-23 10:37:58,882 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:37:58,886 - INFO - Going to convert document batch...\n", "2025-09-23 10:37:58,888 - INFO - Processing document 03_Objekt_3.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 02_Objekt_2.pdf (pages: 2)\n", "\n", "==============================\n", "[3/137] Processing: 03_Objekt_3.pdf\n", "Output dir: extracted_content/03_Objekt_3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:38:22,784 - INFO - Finished converting document 03_Objekt_3.pdf in 22.25 sec.\n", "2025-09-23 10:38:22,824 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:38:22,827 - INFO - Going to convert document batch...\n", "2025-09-23 10:38:22,828 - INFO - Processing document 04_Objekt_4.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 03_Objekt_3.pdf (pages: 2)\n", "\n", "==============================\n", "[4/137] Processing: 04_Objekt_4.pdf\n", "Output dir: extracted_content/04_Objekt_4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:38:43,575 - INFO - Finished converting document 04_Objekt_4.pdf in 20.75 sec.\n", "2025-09-23 10:38:43,627 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:38:43,631 - INFO - Going to convert document batch...\n", "2025-09-23 10:38:43,633 - INFO - Processing document 05_Objekt_5.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 04_Objekt_4.pdf (pages: 2)\n", "\n", "==============================\n", "[5/137] Processing: 05_Objekt_5.pdf\n", "Output dir: extracted_content/05_Objekt_5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:39:03,701 - INFO - Finished converting document 05_Objekt_5.pdf in 18.43 sec.\n", "2025-09-23 10:39:03,734 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:39:03,736 - INFO - Going to convert document batch...\n", "2025-09-23 10:39:03,737 - INFO - Processing document 06_Objekt_6.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 05_Objekt_5.pdf (pages: 2)\n", "\n", "==============================\n", "[6/137] Processing: 06_Objekt_6.pdf\n", "Output dir: extracted_content/06_Objekt_6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:39:18,540 - INFO - Finished converting document 06_Objekt_6.pdf in 14.81 sec.\n", "2025-09-23 10:39:18,589 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:39:18,592 - INFO - Going to convert document batch...\n", "2025-09-23 10:39:18,594 - INFO - Processing document 07_Objekt_7.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 06_Objekt_6.pdf (pages: 2)\n", "\n", "==============================\n", "[7/137] Processing: 07_Objekt_7.pdf\n", "Output dir: extracted_content/07_Objekt_7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:39:39,808 - INFO - Finished converting document 07_Objekt_7.pdf in 19.51 sec.\n", "2025-09-23 10:39:39,841 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:39:39,845 - INFO - Going to convert document batch...\n", "2025-09-23 10:39:39,847 - INFO - Processing document 08_Objekt_8.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 07_Objekt_7.pdf (pages: 2)\n", "\n", "==============================\n", "[8/137] Processing: 08_Objekt_8.pdf\n", "Output dir: extracted_content/08_Objekt_8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:39:59,390 - INFO - Finished converting document 08_Objekt_8.pdf in 17.89 sec.\n", "2025-09-23 10:39:59,425 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:39:59,428 - INFO - Going to convert document batch...\n", "2025-09-23 10:39:59,429 - INFO - Processing document 09_Objekt_9.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 08_Objekt_8.pdf (pages: 2)\n", "\n", "==============================\n", "[9/137] Processing: 09_Objekt_9.pdf\n", "Output dir: extracted_content/09_Objekt_9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:40:18,051 - INFO - Finished converting document 09_Objekt_9.pdf in 18.63 sec.\n", "2025-09-23 10:40:18,079 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:40:18,080 - INFO - Going to convert document batch...\n", "2025-09-23 10:40:18,081 - INFO - Processing document 100_Objekt_100.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 09_Objekt_9.pdf (pages: 2)\n", "\n", "==============================\n", "[10/137] Processing: 100_Objekt_100.pdf\n", "Output dir: extracted_content/100_Objekt_100\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:40:38,037 - INFO - Finished converting document 100_Objekt_100.pdf in 18.31 sec.\n", "2025-09-23 10:40:38,082 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:40:38,085 - INFO - Going to convert document batch...\n", "2025-09-23 10:40:38,086 - INFO - Processing document 101_Objekt_101.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 100_Objekt_100.pdf (pages: 2)\n", "\n", "==============================\n", "[11/137] Processing: 101_Objekt_101.pdf\n", "Output dir: extracted_content/101_Objekt_101\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:40:58,315 - INFO - Finished converting document 101_Objekt_101.pdf in 18.58 sec.\n", "2025-09-23 10:40:58,358 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:40:58,361 - INFO - Going to convert document batch...\n", "2025-09-23 10:40:58,362 - INFO - Processing document 102_Objekt_102.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 101_Objekt_101.pdf (pages: 2)\n", "\n", "==============================\n", "[12/137] Processing: 102_Objekt_102.pdf\n", "Output dir: extracted_content/102_Objekt_102\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:41:16,538 - INFO - Finished converting document 102_Objekt_102.pdf in 18.18 sec.\n", "2025-09-23 10:41:16,577 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:41:16,581 - INFO - Going to convert document batch...\n", "2025-09-23 10:41:16,582 - INFO - Processing document 103_Objekt_103.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 102_Objekt_102.pdf (pages: 2)\n", "\n", "==============================\n", "[13/137] Processing: 103_Objekt_103.pdf\n", "Output dir: extracted_content/103_Objekt_103\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:41:36,226 - INFO - Finished converting document 103_Objekt_103.pdf in 17.96 sec.\n", "2025-09-23 10:41:36,253 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:41:36,255 - INFO - Going to convert document batch...\n", "2025-09-23 10:41:36,256 - INFO - Processing document 104_Objekt_104.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 103_Objekt_103.pdf (pages: 2)\n", "\n", "==============================\n", "[14/137] Processing: 104_Objekt_104.pdf\n", "Output dir: extracted_content/104_Objekt_104\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:41:55,241 - INFO - Finished converting document 104_Objekt_104.pdf in 18.99 sec.\n", "2025-09-23 10:41:55,276 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:41:55,279 - INFO - Going to convert document batch...\n", "2025-09-23 10:41:55,281 - INFO - Processing document 105_Objekt_105.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 104_Objekt_104.pdf (pages: 2)\n", "\n", "==============================\n", "[15/137] Processing: 105_Objekt_105.pdf\n", "Output dir: extracted_content/105_Objekt_105\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:42:15,330 - INFO - Finished converting document 105_Objekt_105.pdf in 18.36 sec.\n", "2025-09-23 10:42:15,366 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:42:15,369 - INFO - Going to convert document batch...\n", "2025-09-23 10:42:15,371 - INFO - Processing document 106_Objekt_106.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 105_Objekt_105.pdf (pages: 2)\n", "\n", "==============================\n", "[16/137] Processing: 106_Objekt_106.pdf\n", "Output dir: extracted_content/106_Objekt_106\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:42:34,256 - INFO - Finished converting document 106_Objekt_106.pdf in 17.25 sec.\n", "2025-09-23 10:42:34,280 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:42:34,282 - INFO - Going to convert document batch...\n", "2025-09-23 10:42:34,283 - INFO - Processing document 107_Objekt_107.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 106_Objekt_106.pdf (pages: 2)\n", "\n", "==============================\n", "[17/137] Processing: 107_Objekt_107.pdf\n", "Output dir: extracted_content/107_Objekt_107\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:42:52,924 - INFO - Finished converting document 107_Objekt_107.pdf in 18.65 sec.\n", "2025-09-23 10:42:52,968 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:42:52,971 - INFO - Going to convert document batch...\n", "2025-09-23 10:42:52,973 - INFO - Processing document 108_Objekt_108.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 107_Objekt_107.pdf (pages: 2)\n", "\n", "==============================\n", "[18/137] Processing: 108_Objekt_108.pdf\n", "Output dir: extracted_content/108_Objekt_108\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:43:09,523 - INFO - Finished converting document 108_Objekt_108.pdf in 14.90 sec.\n", "2025-09-23 10:43:09,549 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:43:09,551 - INFO - Going to convert document batch...\n", "2025-09-23 10:43:09,552 - INFO - Processing document 109_Objekt_109.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 108_Objekt_108.pdf (pages: 2)\n", "\n", "==============================\n", "[19/137] Processing: 109_Objekt_109.pdf\n", "Output dir: extracted_content/109_Objekt_109\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:43:28,189 - INFO - Finished converting document 109_Objekt_109.pdf in 18.64 sec.\n", "2025-09-23 10:43:28,223 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:43:28,226 - INFO - Going to convert document batch...\n", "2025-09-23 10:43:28,228 - INFO - Processing document 10_Objekt_10.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 109_Objekt_109.pdf (pages: 2)\n", "\n", "==============================\n", "[20/137] Processing: 10_Objekt_10.pdf\n", "Output dir: extracted_content/10_Objekt_10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:43:48,714 - INFO - Finished converting document 10_Objekt_10.pdf in 18.81 sec.\n", "2025-09-23 10:43:48,765 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:43:48,770 - INFO - Going to convert document batch...\n", "2025-09-23 10:43:48,772 - INFO - Processing document 110_Objekt_110.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 10_Objekt_10.pdf (pages: 2)\n", "\n", "==============================\n", "[21/137] Processing: 110_Objekt_110.pdf\n", "Output dir: extracted_content/110_Objekt_110\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:44:07,603 - INFO - Finished converting document 110_Objekt_110.pdf in 17.14 sec.\n", "2025-09-23 10:44:07,638 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:44:07,642 - INFO - Going to convert document batch...\n", "2025-09-23 10:44:07,644 - INFO - Processing document 111_Objekt_111.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 110_Objekt_110.pdf (pages: 2)\n", "\n", "==============================\n", "[22/137] Processing: 111_Objekt_111.pdf\n", "Output dir: extracted_content/111_Objekt_111\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:44:23,596 - INFO - Finished converting document 111_Objekt_111.pdf in 15.96 sec.\n", "2025-09-23 10:44:23,636 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:44:23,639 - INFO - Going to convert document batch...\n", "2025-09-23 10:44:23,640 - INFO - Processing document 112_Objekt_112.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 111_Objekt_111.pdf (pages: 2)\n", "\n", "==============================\n", "[23/137] Processing: 112_Objekt_112.pdf\n", "Output dir: extracted_content/112_Objekt_112\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:44:41,474 - INFO - Finished converting document 112_Objekt_112.pdf in 16.19 sec.\n", "2025-09-23 10:44:41,508 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:44:41,511 - INFO - Going to convert document batch...\n", "2025-09-23 10:44:41,512 - INFO - Processing document 113_Objekt_113.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 112_Objekt_112.pdf (pages: 2)\n", "\n", "==============================\n", "[24/137] Processing: 113_Objekt_113.pdf\n", "Output dir: extracted_content/113_Objekt_113\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:45:01,102 - INFO - Finished converting document 113_Objekt_113.pdf in 19.60 sec.\n", "2025-09-23 10:45:01,143 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:45:01,146 - INFO - Going to convert document batch...\n", "2025-09-23 10:45:01,147 - INFO - Processing document 114_Objekt_114.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 113_Objekt_113.pdf (pages: 2)\n", "\n", "==============================\n", "[25/137] Processing: 114_Objekt_114.pdf\n", "Output dir: extracted_content/114_Objekt_114\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:45:22,601 - INFO - Finished converting document 114_Objekt_114.pdf in 19.80 sec.\n", "2025-09-23 10:45:22,644 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:45:22,647 - INFO - Going to convert document batch...\n", "2025-09-23 10:45:22,649 - INFO - Processing document 115_Objekt_115.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 114_Objekt_114.pdf (pages: 2)\n", "\n", "==============================\n", "[26/137] Processing: 115_Objekt_115.pdf\n", "Output dir: extracted_content/115_Objekt_115\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:45:46,245 - INFO - Finished converting document 115_Objekt_115.pdf in 21.92 sec.\n", "2025-09-23 10:45:46,290 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:45:46,294 - INFO - Going to convert document batch...\n", "2025-09-23 10:45:46,296 - INFO - Processing document 116_Objekt_116.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 115_Objekt_115.pdf (pages: 2)\n", "\n", "==============================\n", "[27/137] Processing: 116_Objekt_116.pdf\n", "Output dir: extracted_content/116_Objekt_116\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:46:05,829 - INFO - Finished converting document 116_Objekt_116.pdf in 19.54 sec.\n", "2025-09-23 10:46:05,879 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:46:05,883 - INFO - Going to convert document batch...\n", "2025-09-23 10:46:05,885 - INFO - Processing document 117_Objekt_117.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 116_Objekt_116.pdf (pages: 2)\n", "\n", "==============================\n", "[28/137] Processing: 117_Objekt_117.pdf\n", "Output dir: extracted_content/117_Objekt_117\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:46:22,807 - INFO - Finished converting document 117_Objekt_117.pdf in 15.24 sec.\n", "2025-09-23 10:46:22,844 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:46:22,848 - INFO - Going to convert document batch...\n", "2025-09-23 10:46:22,849 - INFO - Processing document 118_Objekt_118.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 117_Objekt_117.pdf (pages: 2)\n", "\n", "==============================\n", "[29/137] Processing: 118_Objekt_118.pdf\n", "Output dir: extracted_content/118_Objekt_118\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:46:41,522 - INFO - Finished converting document 118_Objekt_118.pdf in 18.68 sec.\n", "2025-09-23 10:46:41,561 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:46:41,564 - INFO - Going to convert document batch...\n", "2025-09-23 10:46:41,565 - INFO - Processing document 119_Objekt_119.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 118_Objekt_118.pdf (pages: 2)\n", "\n", "==============================\n", "[30/137] Processing: 119_Objekt_119.pdf\n", "Output dir: extracted_content/119_Objekt_119\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:47:01,217 - INFO - Finished converting document 119_Objekt_119.pdf in 18.01 sec.\n", "2025-09-23 10:47:01,260 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:47:01,264 - INFO - Going to convert document batch...\n", "2025-09-23 10:47:01,265 - INFO - Processing document 11_Objekt_11.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 119_Objekt_119.pdf (pages: 2)\n", "\n", "==============================\n", "[31/137] Processing: 11_Objekt_11.pdf\n", "Output dir: extracted_content/11_Objekt_11\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:47:23,719 - INFO - Finished converting document 11_Objekt_11.pdf in 20.80 sec.\n", "2025-09-23 10:47:23,769 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:47:23,773 - INFO - Going to convert document batch...\n", "2025-09-23 10:47:23,775 - INFO - Processing document 120_Objekt_120.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 11_Objekt_11.pdf (pages: 2)\n", "\n", "==============================\n", "[32/137] Processing: 120_Objekt_120.pdf\n", "Output dir: extracted_content/120_Objekt_120\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:47:42,779 - INFO - Finished converting document 120_Objekt_120.pdf in 19.01 sec.\n", "2025-09-23 10:47:42,805 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:47:42,807 - INFO - Going to convert document batch...\n", "2025-09-23 10:47:42,808 - INFO - Processing document 121_Objekt_121.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 120_Objekt_120.pdf (pages: 2)\n", "\n", "==============================\n", "[33/137] Processing: 121_Objekt_121.pdf\n", "Output dir: extracted_content/121_Objekt_121\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:48:03,104 - INFO - Finished converting document 121_Objekt_121.pdf in 18.62 sec.\n", "2025-09-23 10:48:03,150 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:48:03,153 - INFO - Going to convert document batch...\n", "2025-09-23 10:48:03,155 - INFO - Processing document 122_Objekt_122.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 121_Objekt_121.pdf (pages: 2)\n", "\n", "==============================\n", "[34/137] Processing: 122_Objekt_122.pdf\n", "Output dir: extracted_content/122_Objekt_122\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:48:24,458 - INFO - Finished converting document 122_Objekt_122.pdf in 19.60 sec.\n", "2025-09-23 10:48:24,512 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:48:24,516 - INFO - Going to convert document batch...\n", "2025-09-23 10:48:24,517 - INFO - Processing document 123_Objekt_123.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 122_Objekt_122.pdf (pages: 2)\n", "\n", "==============================\n", "[35/137] Processing: 123_Objekt_123.pdf\n", "Output dir: extracted_content/123_Objekt_123\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:48:45,371 - INFO - Finished converting document 123_Objekt_123.pdf in 20.86 sec.\n", "2025-09-23 10:48:45,408 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:48:45,411 - INFO - Going to convert document batch...\n", "2025-09-23 10:48:45,412 - INFO - Processing document 124_Objekt_124.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 123_Objekt_123.pdf (pages: 2)\n", "\n", "==============================\n", "[36/137] Processing: 124_Objekt_124.pdf\n", "Output dir: extracted_content/124_Objekt_124\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:49:05,836 - INFO - Finished converting document 124_Objekt_124.pdf in 18.78 sec.\n", "2025-09-23 10:49:05,861 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:49:05,864 - INFO - Going to convert document batch...\n", "2025-09-23 10:49:05,865 - INFO - Processing document 125_Objekt_125.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 124_Objekt_124.pdf (pages: 2)\n", "\n", "==============================\n", "[37/137] Processing: 125_Objekt_125.pdf\n", "Output dir: extracted_content/125_Objekt_125\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:49:24,592 - INFO - Finished converting document 125_Objekt_125.pdf in 17.08 sec.\n", "2025-09-23 10:49:24,627 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:49:24,630 - INFO - Going to convert document batch...\n", "2025-09-23 10:49:24,632 - INFO - Processing document 126_Objekt_126.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 125_Objekt_125.pdf (pages: 2)\n", "\n", "==============================\n", "[38/137] Processing: 126_Objekt_126.pdf\n", "Output dir: extracted_content/126_Objekt_126\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:49:44,332 - INFO - Finished converting document 126_Objekt_126.pdf in 19.71 sec.\n", "2025-09-23 10:49:44,375 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:49:44,378 - INFO - Going to convert document batch...\n", "2025-09-23 10:49:44,380 - INFO - Processing document 127_Objekt_127.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 126_Objekt_126.pdf (pages: 2)\n", "\n", "==============================\n", "[39/137] Processing: 127_Objekt_127.pdf\n", "Output dir: extracted_content/127_Objekt_127\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:50:02,594 - INFO - Finished converting document 127_Objekt_127.pdf in 16.55 sec.\n", "2025-09-23 10:50:02,629 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:50:02,632 - INFO - Going to convert document batch...\n", "2025-09-23 10:50:02,634 - INFO - Processing document 128_Objekt_128.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 127_Objekt_127.pdf (pages: 2)\n", "\n", "==============================\n", "[40/137] Processing: 128_Objekt_128.pdf\n", "Output dir: extracted_content/128_Objekt_128\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:50:21,580 - INFO - Finished converting document 128_Objekt_128.pdf in 18.95 sec.\n", "2025-09-23 10:50:21,623 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:50:21,626 - INFO - Going to convert document batch...\n", "2025-09-23 10:50:21,628 - INFO - Processing document 129_Objekt_129.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 128_Objekt_128.pdf (pages: 2)\n", "\n", "==============================\n", "[41/137] Processing: 129_Objekt_129.pdf\n", "Output dir: extracted_content/129_Objekt_129\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:50:41,551 - INFO - Finished converting document 129_Objekt_129.pdf in 18.23 sec.\n", "2025-09-23 10:50:41,598 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:50:41,601 - INFO - Going to convert document batch...\n", "2025-09-23 10:50:41,603 - INFO - Processing document 12_Objekt_12.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 129_Objekt_129.pdf (pages: 2)\n", "\n", "==============================\n", "[42/137] Processing: 12_Objekt_12.pdf\n", "Output dir: extracted_content/12_Objekt_12\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:51:03,760 - INFO - Finished converting document 12_Objekt_12.pdf in 20.51 sec.\n", "2025-09-23 10:51:03,801 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:51:03,805 - INFO - Going to convert document batch...\n", "2025-09-23 10:51:03,806 - INFO - Processing document 130_Objekt_130.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 12_Objekt_12.pdf (pages: 2)\n", "\n", "==============================\n", "[43/137] Processing: 130_Objekt_130.pdf\n", "Output dir: extracted_content/130_Objekt_130\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:51:23,023 - INFO - Finished converting document 130_Objekt_130.pdf in 19.22 sec.\n", "2025-09-23 10:51:23,058 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:51:23,061 - INFO - Going to convert document batch...\n", "2025-09-23 10:51:23,062 - INFO - Processing document 131_Objekt_131.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 130_Objekt_130.pdf (pages: 2)\n", "\n", "==============================\n", "[44/137] Processing: 131_Objekt_131.pdf\n", "Output dir: extracted_content/131_Objekt_131\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:51:44,253 - INFO - Finished converting document 131_Objekt_131.pdf in 19.54 sec.\n", "2025-09-23 10:51:44,288 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:51:44,291 - INFO - Going to convert document batch...\n", "2025-09-23 10:51:44,292 - INFO - Processing document 132_Objekt_132.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 131_Objekt_131.pdf (pages: 2)\n", "\n", "==============================\n", "[45/137] Processing: 132_Objekt_132.pdf\n", "Output dir: extracted_content/132_Objekt_132\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:52:01,324 - INFO - Finished converting document 132_Objekt_132.pdf in 15.36 sec.\n", "2025-09-23 10:52:01,369 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:52:01,373 - INFO - Going to convert document batch...\n", "2025-09-23 10:52:01,374 - INFO - Processing document 133_Objekt_133.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 132_Objekt_132.pdf (pages: 2)\n", "\n", "==============================\n", "[46/137] Processing: 133_Objekt_133.pdf\n", "Output dir: extracted_content/133_Objekt_133\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:52:23,415 - INFO - Finished converting document 133_Objekt_133.pdf in 22.05 sec.\n", "2025-09-23 10:52:23,469 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:52:23,473 - INFO - Going to convert document batch...\n", "2025-09-23 10:52:23,474 - INFO - Processing document 134_Objekt_134.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 133_Objekt_133.pdf (pages: 2)\n", "\n", "==============================\n", "[47/137] Processing: 134_Objekt_134.pdf\n", "Output dir: extracted_content/134_Objekt_134\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:52:44,241 - INFO - Finished converting document 134_Objekt_134.pdf in 19.07 sec.\n", "2025-09-23 10:52:44,285 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:52:44,287 - INFO - Going to convert document batch...\n", "2025-09-23 10:52:44,289 - INFO - Processing document 135_Objekt_135.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 134_Objekt_134.pdf (pages: 2)\n", "\n", "==============================\n", "[48/137] Processing: 135_Objekt_135.pdf\n", "Output dir: extracted_content/135_Objekt_135\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:53:07,083 - INFO - Finished converting document 135_Objekt_135.pdf in 21.15 sec.\n", "2025-09-23 10:53:07,118 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:53:07,120 - INFO - Going to convert document batch...\n", "2025-09-23 10:53:07,122 - INFO - Processing document 136_Objekt_136.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 135_Objekt_135.pdf (pages: 2)\n", "\n", "==============================\n", "[49/137] Processing: 136_Objekt_136.pdf\n", "Output dir: extracted_content/136_Objekt_136\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:53:29,327 - INFO - Finished converting document 136_Objekt_136.pdf in 22.21 sec.\n", "2025-09-23 10:53:29,355 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:53:29,358 - INFO - Going to convert document batch...\n", "2025-09-23 10:53:29,359 - INFO - Processing document 137_Objekt_137.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 136_Objekt_136.pdf (pages: 2)\n", "\n", "==============================\n", "[50/137] Processing: 137_Objekt_137.pdf\n", "Output dir: extracted_content/137_Objekt_137\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:53:49,005 - INFO - Finished converting document 137_Objekt_137.pdf in 18.00 sec.\n", "2025-09-23 10:53:49,050 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:53:49,053 - INFO - Going to convert document batch...\n", "2025-09-23 10:53:49,054 - INFO - Processing document 13_Objekt_13.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 137_Objekt_137.pdf (pages: 2)\n", "\n", "==============================\n", "[51/137] Processing: 13_Objekt_13.pdf\n", "Output dir: extracted_content/13_Objekt_13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:54:09,285 - INFO - Finished converting document 13_Objekt_13.pdf in 18.57 sec.\n", "2025-09-23 10:54:09,319 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:54:09,323 - INFO - Going to convert document batch...\n", "2025-09-23 10:54:09,324 - INFO - Processing document 14_Objekt_14.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 13_Objekt_13.pdf (pages: 2)\n", "\n", "==============================\n", "[52/137] Processing: 14_Objekt_14.pdf\n", "Output dir: extracted_content/14_Objekt_14\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:54:27,092 - INFO - Finished converting document 14_Objekt_14.pdf in 17.77 sec.\n", "2025-09-23 10:54:27,124 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:54:27,127 - INFO - Going to convert document batch...\n", "2025-09-23 10:54:27,128 - INFO - Processing document 15_Objekt_15.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 14_Objekt_14.pdf (pages: 2)\n", "\n", "==============================\n", "[53/137] Processing: 15_Objekt_15.pdf\n", "Output dir: extracted_content/15_Objekt_15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:54:45,983 - INFO - Finished converting document 15_Objekt_15.pdf in 17.16 sec.\n", "2025-09-23 10:54:46,026 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:54:46,029 - INFO - Going to convert document batch...\n", "2025-09-23 10:54:46,031 - INFO - Processing document 16_Objekt_16.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 15_Objekt_15.pdf (pages: 2)\n", "\n", "==============================\n", "[54/137] Processing: 16_Objekt_16.pdf\n", "Output dir: extracted_content/16_Objekt_16\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:55:04,476 - INFO - Finished converting document 16_Objekt_16.pdf in 18.45 sec.\n", "2025-09-23 10:55:04,518 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:55:04,520 - INFO - Going to convert document batch...\n", "2025-09-23 10:55:04,521 - INFO - Processing document 17_Objekt_17.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 16_Objekt_16.pdf (pages: 2)\n", "\n", "==============================\n", "[55/137] Processing: 17_Objekt_17.pdf\n", "Output dir: extracted_content/17_Objekt_17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:55:23,380 - INFO - Finished converting document 17_Objekt_17.pdf in 17.21 sec.\n", "2025-09-23 10:55:23,409 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:55:23,412 - INFO - Going to convert document batch...\n", "2025-09-23 10:55:23,413 - INFO - Processing document 18_Objekt_18.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 17_Objekt_17.pdf (pages: 2)\n", "\n", "==============================\n", "[56/137] Processing: 18_Objekt_18.pdf\n", "Output dir: extracted_content/18_Objekt_18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:55:44,753 - INFO - Finished converting document 18_Objekt_18.pdf in 19.69 sec.\n", "2025-09-23 10:55:44,780 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:55:44,783 - INFO - Going to convert document batch...\n", "2025-09-23 10:55:44,784 - INFO - Processing document 19_Objekt_19.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 18_Objekt_18.pdf (pages: 2)\n", "\n", "==============================\n", "[57/137] Processing: 19_Objekt_19.pdf\n", "Output dir: extracted_content/19_Objekt_19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:56:00,499 - INFO - Finished converting document 19_Objekt_19.pdf in 15.72 sec.\n", "2025-09-23 10:56:00,532 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:56:00,536 - INFO - Going to convert document batch...\n", "2025-09-23 10:56:00,538 - INFO - Processing document 20_Objekt_20.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 19_Objekt_19.pdf (pages: 2)\n", "\n", "==============================\n", "[58/137] Processing: 20_Objekt_20.pdf\n", "Output dir: extracted_content/20_Objekt_20\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:56:23,425 - INFO - Finished converting document 20_Objekt_20.pdf in 21.23 sec.\n", "2025-09-23 10:56:23,464 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:56:23,467 - INFO - Going to convert document batch...\n", "2025-09-23 10:56:23,469 - INFO - Processing document 21_Objekt_21.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 20_Objekt_20.pdf (pages: 2)\n", "\n", "==============================\n", "[59/137] Processing: 21_Objekt_21.pdf\n", "Output dir: extracted_content/21_Objekt_21\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:56:42,134 - INFO - Finished converting document 21_Objekt_21.pdf in 18.67 sec.\n", "2025-09-23 10:56:42,170 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:56:42,172 - INFO - Going to convert document batch...\n", "2025-09-23 10:56:42,173 - INFO - Processing document 22_Objekt_22.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 21_Objekt_21.pdf (pages: 2)\n", "\n", "==============================\n", "[60/137] Processing: 22_Objekt_22.pdf\n", "Output dir: extracted_content/22_Objekt_22\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:57:03,643 - INFO - Finished converting document 22_Objekt_22.pdf in 19.78 sec.\n", "2025-09-23 10:57:03,686 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:57:03,689 - INFO - Going to convert document batch...\n", "2025-09-23 10:57:03,690 - INFO - Processing document 23_Objekt_23.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 22_Objekt_22.pdf (pages: 2)\n", "\n", "==============================\n", "[61/137] Processing: 23_Objekt_23.pdf\n", "Output dir: extracted_content/23_Objekt_23\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 10:57:21,126 - INFO - Finished converting document 23_Objekt_23.pdf in 15.79 sec.\n", "2025-09-23 10:57:21,159 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:57:21,162 - INFO - Going to convert document batch...\n", "2025-09-23 10:57:21,163 - INFO - Processing document 24_Objekt_24.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 23_Objekt_23.pdf (pages: 2)\n", "\n", "==============================\n", "[62/137] Processing: 24_Objekt_24.pdf\n", "Output dir: extracted_content/24_Objekt_24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:57:40,057 - INFO - Finished converting document 24_Objekt_24.pdf in 18.90 sec.\n", "2025-09-23 10:57:40,095 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:57:40,098 - INFO - Going to convert document batch...\n", "2025-09-23 10:57:40,100 - INFO - Processing document 25_Objekt_25.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 24_Objekt_24.pdf (pages: 2)\n", "\n", "==============================\n", "[63/137] Processing: 25_Objekt_25.pdf\n", "Output dir: extracted_content/25_Objekt_25\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:58:02,211 - INFO - Finished converting document 25_Objekt_25.pdf in 20.47 sec.\n", "2025-09-23 10:58:02,251 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:58:02,254 - INFO - Going to convert document batch...\n", "2025-09-23 10:58:02,256 - INFO - Processing document 26_Objekt_26.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 25_Objekt_25.pdf (pages: 2)\n", "\n", "==============================\n", "[64/137] Processing: 26_Objekt_26.pdf\n", "Output dir: extracted_content/26_Objekt_26\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:58:18,896 - INFO - Finished converting document 26_Objekt_26.pdf in 16.65 sec.\n", "2025-09-23 10:58:18,934 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:58:18,938 - INFO - Going to convert document batch...\n", "2025-09-23 10:58:18,939 - INFO - Processing document 27_Objekt_27.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 26_Objekt_26.pdf (pages: 2)\n", "\n", "==============================\n", "[65/137] Processing: 27_Objekt_27.pdf\n", "Output dir: extracted_content/27_Objekt_27\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:58:41,459 - INFO - Finished converting document 27_Objekt_27.pdf in 20.86 sec.\n", "2025-09-23 10:58:41,488 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:58:41,490 - INFO - Going to convert document batch...\n", "2025-09-23 10:58:41,491 - INFO - Processing document 28_Objekt_28.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 27_Objekt_27.pdf (pages: 2)\n", "\n", "==============================\n", "[66/137] Processing: 28_Objekt_28.pdf\n", "Output dir: extracted_content/28_Objekt_28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:59:02,227 - INFO - Finished converting document 28_Objekt_28.pdf in 19.05 sec.\n", "2025-09-23 10:59:02,265 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:59:02,267 - INFO - Going to convert document batch...\n", "2025-09-23 10:59:02,268 - INFO - Processing document 29_Objekt_29.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 28_Objekt_28.pdf (pages: 2)\n", "\n", "==============================\n", "[67/137] Processing: 29_Objekt_29.pdf\n", "Output dir: extracted_content/29_Objekt_29\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:59:19,684 - INFO - Finished converting document 29_Objekt_29.pdf in 17.42 sec.\n", "2025-09-23 10:59:19,730 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:59:19,734 - INFO - Going to convert document batch...\n", "2025-09-23 10:59:19,736 - INFO - Processing document 30_Objekt_30.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 29_Objekt_29.pdf (pages: 2)\n", "\n", "==============================\n", "[68/137] Processing: 30_Objekt_30.pdf\n", "Output dir: extracted_content/30_Objekt_30\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 10:59:40,089 - INFO - Finished converting document 30_Objekt_30.pdf in 18.71 sec.\n", "2025-09-23 10:59:40,127 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 10:59:40,129 - INFO - Going to convert document batch...\n", "2025-09-23 10:59:40,130 - INFO - Processing document 31_Objekt_31.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 30_Objekt_30.pdf (pages: 2)\n", "\n", "==============================\n", "[69/137] Processing: 31_Objekt_31.pdf\n", "Output dir: extracted_content/31_Objekt_31\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:00:00,378 - INFO - Finished converting document 31_Objekt_31.pdf in 18.60 sec.\n", "2025-09-23 11:00:00,403 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:00:00,405 - INFO - Going to convert document batch...\n", "2025-09-23 11:00:00,406 - INFO - Processing document 32_Objekt_32.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 31_Objekt_31.pdf (pages: 2)\n", "\n", "==============================\n", "[70/137] Processing: 32_Objekt_32.pdf\n", "Output dir: extracted_content/32_Objekt_32\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:00:18,291 - INFO - Finished converting document 32_Objekt_32.pdf in 17.89 sec.\n", "2025-09-23 11:00:18,332 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:00:18,336 - INFO - Going to convert document batch...\n", "2025-09-23 11:00:18,338 - INFO - Processing document 33_Objekt_33.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 32_Objekt_32.pdf (pages: 2)\n", "\n", "==============================\n", "[71/137] Processing: 33_Objekt_33.pdf\n", "Output dir: extracted_content/33_Objekt_33\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:00:39,261 - INFO - Finished converting document 33_Objekt_33.pdf in 19.27 sec.\n", "2025-09-23 11:00:39,301 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:00:39,303 - INFO - Going to convert document batch...\n", "2025-09-23 11:00:39,305 - INFO - Processing document 34_Objekt_34.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 33_Objekt_33.pdf (pages: 2)\n", "\n", "==============================\n", "[72/137] Processing: 34_Objekt_34.pdf\n", "Output dir: extracted_content/34_Objekt_34\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:01:00,814 - INFO - Finished converting document 34_Objekt_34.pdf in 19.84 sec.\n", "2025-09-23 11:01:00,844 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:01:00,847 - INFO - Going to convert document batch...\n", "2025-09-23 11:01:00,849 - INFO - Processing document 35_Objekt_35.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 34_Objekt_34.pdf (pages: 2)\n", "\n", "==============================\n", "[73/137] Processing: 35_Objekt_35.pdf\n", "Output dir: extracted_content/35_Objekt_35\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:01:18,255 - INFO - Finished converting document 35_Objekt_35.pdf in 17.41 sec.\n", "2025-09-23 11:01:18,297 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:01:18,301 - INFO - Going to convert document batch...\n", "2025-09-23 11:01:18,303 - INFO - Processing document 36_Objekt_36.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 35_Objekt_35.pdf (pages: 2)\n", "\n", "==============================\n", "[74/137] Processing: 36_Objekt_36.pdf\n", "Output dir: extracted_content/36_Objekt_36\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:01:41,302 - INFO - Finished converting document 36_Objekt_36.pdf in 21.31 sec.\n", "2025-09-23 11:01:41,350 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:01:41,353 - INFO - Going to convert document batch...\n", "2025-09-23 11:01:41,354 - INFO - Processing document 37_Objekt_37.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 36_Objekt_36.pdf (pages: 2)\n", "\n", "==============================\n", "[75/137] Processing: 37_Objekt_37.pdf\n", "Output dir: extracted_content/37_Objekt_37\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:02:00,746 - INFO - Finished converting document 37_Objekt_37.pdf in 19.40 sec.\n", "2025-09-23 11:02:00,783 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:02:00,787 - INFO - Going to convert document batch...\n", "2025-09-23 11:02:00,789 - INFO - Processing document 38_Objekt_38.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 37_Objekt_37.pdf (pages: 2)\n", "\n", "==============================\n", "[76/137] Processing: 38_Objekt_38.pdf\n", "Output dir: extracted_content/38_Objekt_38\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:02:19,101 - INFO - Finished converting document 38_Objekt_38.pdf in 16.68 sec.\n", "2025-09-23 11:02:19,134 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:02:19,137 - INFO - Going to convert document batch...\n", "2025-09-23 11:02:19,139 - INFO - Processing document 39_Objekt_39.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 38_Objekt_38.pdf (pages: 2)\n", "\n", "==============================\n", "[77/137] Processing: 39_Objekt_39.pdf\n", "Output dir: extracted_content/39_Objekt_39\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:02:42,178 - INFO - Finished converting document 39_Objekt_39.pdf in 21.38 sec.\n", "2025-09-23 11:02:42,205 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:02:42,207 - INFO - Going to convert document batch...\n", "2025-09-23 11:02:42,208 - INFO - Processing document 40_Objekt_40.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 39_Objekt_39.pdf (pages: 2)\n", "\n", "==============================\n", "[78/137] Processing: 40_Objekt_40.pdf\n", "Output dir: extracted_content/40_Objekt_40\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:03:01,768 - INFO - Finished converting document 40_Objekt_40.pdf in 19.57 sec.\n", "2025-09-23 11:03:01,810 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:03:01,813 - INFO - Going to convert document batch...\n", "2025-09-23 11:03:01,815 - INFO - Processing document 41_Objekt_41.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 40_Objekt_40.pdf (pages: 2)\n", "\n", "==============================\n", "[79/137] Processing: 41_Objekt_41.pdf\n", "Output dir: extracted_content/41_Objekt_41\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:03:20,181 - INFO - Finished converting document 41_Objekt_41.pdf in 16.69 sec.\n", "2025-09-23 11:03:20,211 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:03:20,214 - INFO - Going to convert document batch...\n", "2025-09-23 11:03:20,215 - INFO - Processing document 42_Objekt_42.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 41_Objekt_41.pdf (pages: 2)\n", "\n", "==============================\n", "[80/137] Processing: 42_Objekt_42.pdf\n", "Output dir: extracted_content/42_Objekt_42\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:03:42,896 - INFO - Finished converting document 42_Objekt_42.pdf in 20.98 sec.\n", "2025-09-23 11:03:42,937 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:03:42,941 - INFO - Going to convert document batch...\n", "2025-09-23 11:03:42,943 - INFO - Processing document 43_Objekt_43.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 42_Objekt_42.pdf (pages: 2)\n", "\n", "==============================\n", "[81/137] Processing: 43_Objekt_43.pdf\n", "Output dir: extracted_content/43_Objekt_43\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:04:01,292 - INFO - Finished converting document 43_Objekt_43.pdf in 18.36 sec.\n", "2025-09-23 11:04:01,327 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:04:01,330 - INFO - Going to convert document batch...\n", "2025-09-23 11:04:01,332 - INFO - Processing document 44_Objekt_44.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 43_Objekt_43.pdf (pages: 2)\n", "\n", "==============================\n", "[82/137] Processing: 44_Objekt_44.pdf\n", "Output dir: extracted_content/44_Objekt_44\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:04:22,579 - INFO - Finished converting document 44_Objekt_44.pdf in 19.62 sec.\n", "2025-09-23 11:04:22,622 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:04:22,624 - INFO - Going to convert document batch...\n", "2025-09-23 11:04:22,625 - INFO - Processing document 45_Objekt_45.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 44_Objekt_44.pdf (pages: 2)\n", "\n", "==============================\n", "[83/137] Processing: 45_Objekt_45.pdf\n", "Output dir: extracted_content/45_Objekt_45\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:04:38,821 - INFO - Finished converting document 45_Objekt_45.pdf in 16.20 sec.\n", "2025-09-23 11:04:38,858 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:04:38,861 - INFO - Going to convert document batch...\n", "2025-09-23 11:04:38,862 - INFO - Processing document 46_Objekt_46.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 45_Objekt_45.pdf (pages: 2)\n", "\n", "==============================\n", "[84/137] Processing: 46_Objekt_46.pdf\n", "Output dir: extracted_content/46_Objekt_46\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:04:57,381 - INFO - Finished converting document 46_Objekt_46.pdf in 16.85 sec.\n", "2025-09-23 11:04:57,409 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:04:57,411 - INFO - Going to convert document batch...\n", "2025-09-23 11:04:57,412 - INFO - Processing document 47_Objekt_47.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 46_Objekt_46.pdf (pages: 2)\n", "\n", "==============================\n", "[85/137] Processing: 47_Objekt_47.pdf\n", "Output dir: extracted_content/47_Objekt_47\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:05:14,937 - INFO - Finished converting document 47_Objekt_47.pdf in 15.85 sec.\n", "2025-09-23 11:05:14,988 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:05:14,991 - INFO - Going to convert document batch...\n", "2025-09-23 11:05:14,993 - INFO - Processing document 48_Objekt_48.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 47_Objekt_47.pdf (pages: 2)\n", "\n", "==============================\n", "[86/137] Processing: 48_Objekt_48.pdf\n", "Output dir: extracted_content/48_Objekt_48\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:05:34,522 - INFO - Finished converting document 48_Objekt_48.pdf in 19.54 sec.\n", "2025-09-23 11:05:34,553 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:05:34,556 - INFO - Going to convert document batch...\n", "2025-09-23 11:05:34,558 - INFO - Processing document 49_Objekt_49.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 48_Objekt_48.pdf (pages: 2)\n", "\n", "==============================\n", "[87/137] Processing: 49_Objekt_49.pdf\n", "Output dir: extracted_content/49_Objekt_49\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:05:51,201 - INFO - Finished converting document 49_Objekt_49.pdf in 14.94 sec.\n", "2025-09-23 11:05:51,242 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:05:51,246 - INFO - Going to convert document batch...\n", "2025-09-23 11:05:51,248 - INFO - Processing document 50_Objekt_50.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 49_Objekt_49.pdf (pages: 2)\n", "\n", "==============================\n", "[88/137] Processing: 50_Objekt_50.pdf\n", "Output dir: extracted_content/50_Objekt_50\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:06:10,446 - INFO - Finished converting document 50_Objekt_50.pdf in 19.21 sec.\n", "2025-09-23 11:06:10,475 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:06:10,478 - INFO - Going to convert document batch...\n", "2025-09-23 11:06:10,479 - INFO - Processing document 51_Objekt_51.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 50_Objekt_50.pdf (pages: 2)\n", "\n", "==============================\n", "[89/137] Processing: 51_Objekt_51.pdf\n", "Output dir: extracted_content/51_Objekt_51\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:06:32,468 - INFO - Finished converting document 51_Objekt_51.pdf in 20.35 sec.\n", "2025-09-23 11:06:32,505 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:06:32,508 - INFO - Going to convert document batch...\n", "2025-09-23 11:06:32,509 - INFO - Processing document 52_Objekt_52.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 51_Objekt_51.pdf (pages: 2)\n", "\n", "==============================\n", "[90/137] Processing: 52_Objekt_52.pdf\n", "Output dir: extracted_content/52_Objekt_52\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:06:51,920 - INFO - Finished converting document 52_Objekt_52.pdf in 17.74 sec.\n", "2025-09-23 11:06:51,962 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:06:51,966 - INFO - Going to convert document batch...\n", "2025-09-23 11:06:51,967 - INFO - Processing document 53_Objekt_53.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 52_Objekt_52.pdf (pages: 2)\n", "\n", "==============================\n", "[91/137] Processing: 53_Objekt_53.pdf\n", "Output dir: extracted_content/53_Objekt_53\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:07:09,704 - INFO - Finished converting document 53_Objekt_53.pdf in 17.75 sec.\n", "2025-09-23 11:07:09,748 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:07:09,752 - INFO - Going to convert document batch...\n", "2025-09-23 11:07:09,753 - INFO - Processing document 54_Objekt_54.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 53_Objekt_53.pdf (pages: 2)\n", "\n", "==============================\n", "[92/137] Processing: 54_Objekt_54.pdf\n", "Output dir: extracted_content/54_Objekt_54\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:07:31,904 - INFO - Finished converting document 54_Objekt_54.pdf in 20.47 sec.\n", "2025-09-23 11:07:31,937 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:07:31,940 - INFO - Going to convert document batch...\n", "2025-09-23 11:07:31,941 - INFO - Processing document 55_Objekt_55.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 54_Objekt_54.pdf (pages: 2)\n", "\n", "==============================\n", "[93/137] Processing: 55_Objekt_55.pdf\n", "Output dir: extracted_content/55_Objekt_55\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:07:48,766 - INFO - Finished converting document 55_Objekt_55.pdf in 16.83 sec.\n", "2025-09-23 11:07:48,800 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:07:48,805 - INFO - Going to convert document batch...\n", "2025-09-23 11:07:48,807 - INFO - Processing document 56_Objekt_56.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 55_Objekt_55.pdf (pages: 2)\n", "\n", "==============================\n", "[94/137] Processing: 56_Objekt_56.pdf\n", "Output dir: extracted_content/56_Objekt_56\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:08:09,888 - INFO - Finished converting document 56_Objekt_56.pdf in 19.38 sec.\n", "2025-09-23 11:08:09,918 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:08:09,921 - INFO - Going to convert document batch...\n", "2025-09-23 11:08:09,922 - INFO - Processing document 57_Objekt_57.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 56_Objekt_56.pdf (pages: 2)\n", "\n", "==============================\n", "[95/137] Processing: 57_Objekt_57.pdf\n", "Output dir: extracted_content/57_Objekt_57\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:08:27,887 - INFO - Finished converting document 57_Objekt_57.pdf in 16.33 sec.\n", "2025-09-23 11:08:27,926 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:08:27,929 - INFO - Going to convert document batch...\n", "2025-09-23 11:08:27,931 - INFO - Processing document 58_Objekt_58.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 57_Objekt_57.pdf (pages: 2)\n", "\n", "==============================\n", "[96/137] Processing: 58_Objekt_58.pdf\n", "Output dir: extracted_content/58_Objekt_58\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:08:44,358 - INFO - Finished converting document 58_Objekt_58.pdf in 16.43 sec.\n", "2025-09-23 11:08:44,397 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:08:44,400 - INFO - Going to convert document batch...\n", "2025-09-23 11:08:44,402 - INFO - Processing document 59_Objekt_59.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 58_Objekt_58.pdf (pages: 2)\n", "\n", "==============================\n", "[97/137] Processing: 59_Objekt_59.pdf\n", "Output dir: extracted_content/59_Objekt_59\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:09:05,207 - INFO - Finished converting document 59_Objekt_59.pdf in 19.14 sec.\n", "2025-09-23 11:09:05,243 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:09:05,245 - INFO - Going to convert document batch...\n", "2025-09-23 11:09:05,246 - INFO - Processing document 60_Objekt_60.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 59_Objekt_59.pdf (pages: 2)\n", "\n", "==============================\n", "[98/137] Processing: 60_Objekt_60.pdf\n", "Output dir: extracted_content/60_Objekt_60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:09:28,490 - INFO - Finished converting document 60_Objekt_60.pdf in 21.56 sec.\n", "2025-09-23 11:09:28,541 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:09:28,545 - INFO - Going to convert document batch...\n", "2025-09-23 11:09:28,547 - INFO - Processing document 61_Objekt_61.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 60_Objekt_60.pdf (pages: 2)\n", "\n", "==============================\n", "[99/137] Processing: 61_Objekt_61.pdf\n", "Output dir: extracted_content/61_Objekt_61\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:09:46,632 - INFO - Finished converting document 61_Objekt_61.pdf in 18.09 sec.\n", "2025-09-23 11:09:46,672 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:09:46,676 - INFO - Going to convert document batch...\n", "2025-09-23 11:09:46,678 - INFO - Processing document 62_Objekt_62.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 61_Objekt_61.pdf (pages: 2)\n", "\n", "==============================\n", "[100/137] Processing: 62_Objekt_62.pdf\n", "Output dir: extracted_content/62_Objekt_62\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:10:06,684 - INFO - Finished converting document 62_Objekt_62.pdf in 18.33 sec.\n", "2025-09-23 11:10:06,727 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:10:06,730 - INFO - Going to convert document batch...\n", "2025-09-23 11:10:06,732 - INFO - Processing document 63_Objekt_63.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 62_Objekt_62.pdf (pages: 2)\n", "\n", "==============================\n", "[101/137] Processing: 63_Objekt_63.pdf\n", "Output dir: extracted_content/63_Objekt_63\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:10:32,249 - INFO - Finished converting document 63_Objekt_63.pdf in 23.86 sec.\n", "2025-09-23 11:10:32,283 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:10:32,286 - INFO - Going to convert document batch...\n", "2025-09-23 11:10:32,288 - INFO - Processing document 64_Objekt_64.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 63_Objekt_63.pdf (pages: 2)\n", "\n", "==============================\n", "[102/137] Processing: 64_Objekt_64.pdf\n", "Output dir: extracted_content/64_Objekt_64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:10:53,145 - INFO - Finished converting document 64_Objekt_64.pdf in 20.86 sec.\n", "2025-09-23 11:10:53,177 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:10:53,179 - INFO - Going to convert document batch...\n", "2025-09-23 11:10:53,180 - INFO - Processing document 65_Objekt_65.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 64_Objekt_64.pdf (pages: 2)\n", "\n", "==============================\n", "[103/137] Processing: 65_Objekt_65.pdf\n", "Output dir: extracted_content/65_Objekt_65\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:11:09,959 - INFO - Finished converting document 65_Objekt_65.pdf in 15.10 sec.\n", "2025-09-23 11:11:09,994 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:11:09,997 - INFO - Going to convert document batch...\n", "2025-09-23 11:11:09,999 - INFO - Processing document 66_Objekt_66.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 65_Objekt_65.pdf (pages: 2)\n", "\n", "==============================\n", "[104/137] Processing: 66_Objekt_66.pdf\n", "Output dir: extracted_content/66_Objekt_66\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:11:27,421 - INFO - Finished converting document 66_Objekt_66.pdf in 17.43 sec.\n", "2025-09-23 11:11:27,446 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:11:27,448 - INFO - Going to convert document batch...\n", "2025-09-23 11:11:27,449 - INFO - Processing document 67_Objekt_67.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 66_Objekt_66.pdf (pages: 2)\n", "\n", "==============================\n", "[105/137] Processing: 67_Objekt_67.pdf\n", "Output dir: extracted_content/67_Objekt_67\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:11:45,890 - INFO - Finished converting document 67_Objekt_67.pdf in 16.76 sec.\n", "2025-09-23 11:11:45,938 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:11:45,941 - INFO - Going to convert document batch...\n", "2025-09-23 11:11:45,944 - INFO - Processing document 68_Objekt_68.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 67_Objekt_67.pdf (pages: 2)\n", "\n", "==============================\n", "[106/137] Processing: 68_Objekt_68.pdf\n", "Output dir: extracted_content/68_Objekt_68\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:12:00,401 - INFO - Finished converting document 68_Objekt_68.pdf in 14.47 sec.\n", "2025-09-23 11:12:00,427 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:12:00,430 - INFO - Going to convert document batch...\n", "2025-09-23 11:12:00,431 - INFO - Processing document 69_Objekt_69.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 68_Objekt_68.pdf (pages: 2)\n", "\n", "==============================\n", "[107/137] Processing: 69_Objekt_69.pdf\n", "Output dir: extracted_content/69_Objekt_69\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:12:19,350 - INFO - Finished converting document 69_Objekt_69.pdf in 17.24 sec.\n", "2025-09-23 11:12:19,389 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:12:19,392 - INFO - Going to convert document batch...\n", "2025-09-23 11:12:19,394 - INFO - Processing document 70_Objekt_70.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 69_Objekt_69.pdf (pages: 2)\n", "\n", "==============================\n", "[108/137] Processing: 70_Objekt_70.pdf\n", "Output dir: extracted_content/70_Objekt_70\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:12:39,003 - INFO - Finished converting document 70_Objekt_70.pdf in 17.95 sec.\n", "2025-09-23 11:12:39,027 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:12:39,029 - INFO - Going to convert document batch...\n", "2025-09-23 11:12:39,030 - INFO - Processing document 71_Objekt_71.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 70_Objekt_70.pdf (pages: 2)\n", "\n", "==============================\n", "[109/137] Processing: 71_Objekt_71.pdf\n", "Output dir: extracted_content/71_Objekt_71\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:13:02,537 - INFO - Finished converting document 71_Objekt_71.pdf in 23.51 sec.\n", "2025-09-23 11:13:02,567 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:13:02,570 - INFO - Going to convert document batch...\n", "2025-09-23 11:13:02,571 - INFO - Processing document 72_Objekt_72.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 71_Objekt_71.pdf (pages: 2)\n", "\n", "==============================\n", "[110/137] Processing: 72_Objekt_72.pdf\n", "Output dir: extracted_content/72_Objekt_72\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:13:25,084 - INFO - Finished converting document 72_Objekt_72.pdf in 20.84 sec.\n", "2025-09-23 11:13:25,118 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:13:25,120 - INFO - Going to convert document batch...\n", "2025-09-23 11:13:25,121 - INFO - Processing document 73_Objekt_73.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 72_Objekt_72.pdf (pages: 2)\n", "\n", "==============================\n", "[111/137] Processing: 73_Objekt_73.pdf\n", "Output dir: extracted_content/73_Objekt_73\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:13:46,579 - INFO - Finished converting document 73_Objekt_73.pdf in 19.77 sec.\n", "2025-09-23 11:13:46,617 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:13:46,621 - INFO - Going to convert document batch...\n", "2025-09-23 11:13:46,622 - INFO - Processing document 74_Objekt_74.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 73_Objekt_73.pdf (pages: 2)\n", "\n", "==============================\n", "[112/137] Processing: 74_Objekt_74.pdf\n", "Output dir: extracted_content/74_Objekt_74\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:14:07,847 - INFO - Finished converting document 74_Objekt_74.pdf in 21.23 sec.\n", "2025-09-23 11:14:07,891 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:14:07,894 - INFO - Going to convert document batch...\n", "2025-09-23 11:14:07,896 - INFO - Processing document 75_Objekt_75.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 74_Objekt_74.pdf (pages: 2)\n", "\n", "==============================\n", "[113/137] Processing: 75_Objekt_75.pdf\n", "Output dir: extracted_content/75_Objekt_75\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:14:30,471 - INFO - Finished converting document 75_Objekt_75.pdf in 20.90 sec.\n", "2025-09-23 11:14:30,502 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:14:30,505 - INFO - Going to convert document batch...\n", "2025-09-23 11:14:30,507 - INFO - Processing document 76_Objekt_76.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 75_Objekt_75.pdf (pages: 2)\n", "\n", "==============================\n", "[114/137] Processing: 76_Objekt_76.pdf\n", "Output dir: extracted_content/76_Objekt_76\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:14:49,178 - INFO - Finished converting document 76_Objekt_76.pdf in 16.93 sec.\n", "2025-09-23 11:14:49,228 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:14:49,231 - INFO - Going to convert document batch...\n", "2025-09-23 11:14:49,233 - INFO - Processing document 77_Objekt_77.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 76_Objekt_76.pdf (pages: 2)\n", "\n", "==============================\n", "[115/137] Processing: 77_Objekt_77.pdf\n", "Output dir: extracted_content/77_Objekt_77\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:15:10,029 - INFO - Finished converting document 77_Objekt_77.pdf in 20.81 sec.\n", "2025-09-23 11:15:10,068 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:15:10,072 - INFO - Going to convert document batch...\n", "2025-09-23 11:15:10,073 - INFO - Processing document 78_Objekt_78.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 77_Objekt_77.pdf (pages: 2)\n", "\n", "==============================\n", "[116/137] Processing: 78_Objekt_78.pdf\n", "Output dir: extracted_content/78_Objekt_78\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:15:27,871 - INFO - Finished converting document 78_Objekt_78.pdf in 16.12 sec.\n", "2025-09-23 11:15:27,908 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:15:27,911 - INFO - Going to convert document batch...\n", "2025-09-23 11:15:27,913 - INFO - Processing document 79_Objekt_79.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 78_Objekt_78.pdf (pages: 2)\n", "\n", "==============================\n", "[117/137] Processing: 79_Objekt_79.pdf\n", "Output dir: extracted_content/79_Objekt_79\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:15:48,692 - INFO - Finished converting document 79_Objekt_79.pdf in 19.12 sec.\n", "2025-09-23 11:15:48,733 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:15:48,737 - INFO - Going to convert document batch...\n", "2025-09-23 11:15:48,739 - INFO - Processing document 80_Objekt_80.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 79_Objekt_79.pdf (pages: 2)\n", "\n", "==============================\n", "[118/137] Processing: 80_Objekt_80.pdf\n", "Output dir: extracted_content/80_Objekt_80\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:16:06,501 - INFO - Finished converting document 80_Objekt_80.pdf in 17.77 sec.\n", "2025-09-23 11:16:06,547 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:16:06,551 - INFO - Going to convert document batch...\n", "2025-09-23 11:16:06,553 - INFO - Processing document 81_Objekt_81.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 80_Objekt_80.pdf (pages: 2)\n", "\n", "==============================\n", "[119/137] Processing: 81_Objekt_81.pdf\n", "Output dir: extracted_content/81_Objekt_81\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:16:27,212 - INFO - Finished converting document 81_Objekt_81.pdf in 18.99 sec.\n", "2025-09-23 11:16:27,252 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:16:27,256 - INFO - Going to convert document batch...\n", "2025-09-23 11:16:27,257 - INFO - Processing document 82_Objekt_82.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 81_Objekt_81.pdf (pages: 2)\n", "\n", "==============================\n", "[120/137] Processing: 82_Objekt_82.pdf\n", "Output dir: extracted_content/82_Objekt_82\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:16:45,915 - INFO - Finished converting document 82_Objekt_82.pdf in 18.67 sec.\n", "2025-09-23 11:16:45,948 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:16:45,951 - INFO - Going to convert document batch...\n", "2025-09-23 11:16:45,953 - INFO - Processing document 83_Objekt_83.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 82_Objekt_82.pdf (pages: 2)\n", "\n", "==============================\n", "[121/137] Processing: 83_Objekt_83.pdf\n", "Output dir: extracted_content/83_Objekt_83\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:17:06,686 - INFO - Finished converting document 83_Objekt_83.pdf in 19.02 sec.\n", "2025-09-23 11:17:06,716 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:17:06,719 - INFO - Going to convert document batch...\n", "2025-09-23 11:17:06,721 - INFO - Processing document 84_Objekt_84.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 83_Objekt_83.pdf (pages: 2)\n", "\n", "==============================\n", "[122/137] Processing: 84_Objekt_84.pdf\n", "Output dir: extracted_content/84_Objekt_84\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:17:25,779 - INFO - Finished converting document 84_Objekt_84.pdf in 17.34 sec.\n", "2025-09-23 11:17:25,822 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:17:25,825 - INFO - Going to convert document batch...\n", "2025-09-23 11:17:25,827 - INFO - Processing document 85_Objekt_85.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 84_Objekt_84.pdf (pages: 2)\n", "\n", "==============================\n", "[123/137] Processing: 85_Objekt_85.pdf\n", "Output dir: extracted_content/85_Objekt_85\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:17:45,500 - INFO - Finished converting document 85_Objekt_85.pdf in 19.68 sec.\n", "2025-09-23 11:17:45,541 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:17:45,545 - INFO - Going to convert document batch...\n", "2025-09-23 11:17:45,546 - INFO - Processing document 86_Objekt_86.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 85_Objekt_85.pdf (pages: 2)\n", "\n", "==============================\n", "[124/137] Processing: 86_Objekt_86.pdf\n", "Output dir: extracted_content/86_Objekt_86\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:18:03,998 - INFO - Finished converting document 86_Objekt_86.pdf in 16.79 sec.\n", "2025-09-23 11:18:04,028 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:18:04,031 - INFO - Going to convert document batch...\n", "2025-09-23 11:18:04,032 - INFO - Processing document 87_Objekt_87.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 86_Objekt_86.pdf (pages: 2)\n", "\n", "==============================\n", "[125/137] Processing: 87_Objekt_87.pdf\n", "Output dir: extracted_content/87_Objekt_87\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:18:22,359 - INFO - Finished converting document 87_Objekt_87.pdf in 18.33 sec.\n", "2025-09-23 11:18:22,399 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:18:22,402 - INFO - Going to convert document batch...\n", "2025-09-23 11:18:22,404 - INFO - Processing document 88_Objekt_88.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 87_Objekt_87.pdf (pages: 2)\n", "\n", "==============================\n", "[126/137] Processing: 88_Objekt_88.pdf\n", "Output dir: extracted_content/88_Objekt_88\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:18:44,240 - INFO - Finished converting document 88_Objekt_88.pdf in 20.16 sec.\n", "2025-09-23 11:18:44,287 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:18:44,291 - INFO - Going to convert document batch...\n", "2025-09-23 11:18:44,293 - INFO - Processing document 89_Objekt_89.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 88_Objekt_88.pdf (pages: 2)\n", "\n", "==============================\n", "[127/137] Processing: 89_Objekt_89.pdf\n", "Output dir: extracted_content/89_Objekt_89\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:19:02,496 - INFO - Finished converting document 89_Objekt_89.pdf in 16.50 sec.\n", "2025-09-23 11:19:02,531 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:19:02,535 - INFO - Going to convert document batch...\n", "2025-09-23 11:19:02,537 - INFO - Processing document 90_Objekt_90.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 89_Objekt_89.pdf (pages: 2)\n", "\n", "==============================\n", "[128/137] Processing: 90_Objekt_90.pdf\n", "Output dir: extracted_content/90_Objekt_90\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:19:20,919 - INFO - Finished converting document 90_Objekt_90.pdf in 18.39 sec.\n", "2025-09-23 11:19:20,951 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:19:20,954 - INFO - Going to convert document batch...\n", "2025-09-23 11:19:20,955 - INFO - Processing document 91_Objekt_91.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 90_Objekt_90.pdf (pages: 2)\n", "\n", "==============================\n", "[129/137] Processing: 91_Objekt_91.pdf\n", "Output dir: extracted_content/91_Objekt_91\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:19:42,063 - INFO - Finished converting document 91_Objekt_91.pdf in 19.39 sec.\n", "2025-09-23 11:19:42,111 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:19:42,114 - INFO - Going to convert document batch...\n", "2025-09-23 11:19:42,116 - INFO - Processing document 92_Objekt_92.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 91_Objekt_91.pdf (pages: 2)\n", "\n", "==============================\n", "[130/137] Processing: 92_Objekt_92.pdf\n", "Output dir: extracted_content/92_Objekt_92\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:20:01,558 - INFO - Finished converting document 92_Objekt_92.pdf in 17.78 sec.\n", "2025-09-23 11:20:01,615 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:20:01,619 - INFO - Going to convert document batch...\n", "2025-09-23 11:20:01,622 - INFO - Processing document 93_Objekt_93.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 92_Objekt_92.pdf (pages: 2)\n", "\n", "==============================\n", "[131/137] Processing: 93_Objekt_93.pdf\n", "Output dir: extracted_content/93_Objekt_93\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-23 11:20:17,704 - INFO - Finished converting document 93_Objekt_93.pdf in 16.09 sec.\n", "2025-09-23 11:20:17,742 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:20:17,745 - INFO - Going to convert document batch...\n", "2025-09-23 11:20:17,746 - INFO - Processing document 94_Objekt_94.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 93_Objekt_93.pdf (pages: 2)\n", "\n", "==============================\n", "[132/137] Processing: 94_Objekt_94.pdf\n", "Output dir: extracted_content/94_Objekt_94\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:20:38,066 - INFO - Finished converting document 94_Objekt_94.pdf in 18.64 sec.\n", "2025-09-23 11:20:38,102 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:20:38,105 - INFO - Going to convert document batch...\n", "2025-09-23 11:20:38,106 - INFO - Processing document 95_Objekt_95.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 94_Objekt_94.pdf (pages: 2)\n", "\n", "==============================\n", "[133/137] Processing: 95_Objekt_95.pdf\n", "Output dir: extracted_content/95_Objekt_95\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:20:54,696 - INFO - Finished converting document 95_Objekt_95.pdf in 16.60 sec.\n", "2025-09-23 11:20:54,732 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:20:54,735 - INFO - Going to convert document batch...\n", "2025-09-23 11:20:54,737 - INFO - Processing document 96_Objekt_96.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 95_Objekt_95.pdf (pages: 2)\n", "\n", "==============================\n", "[134/137] Processing: 96_Objekt_96.pdf\n", "Output dir: extracted_content/96_Objekt_96\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:21:12,966 - INFO - Finished converting document 96_Objekt_96.pdf in 16.52 sec.\n", "2025-09-23 11:21:12,990 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:21:12,993 - INFO - Going to convert document batch...\n", "2025-09-23 11:21:12,994 - INFO - Processing document 97_Objekt_97.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 96_Objekt_96.pdf (pages: 2)\n", "\n", "==============================\n", "[135/137] Processing: 97_Objekt_97.pdf\n", "Output dir: extracted_content/97_Objekt_97\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:21:29,683 - INFO - Finished converting document 97_Objekt_97.pdf in 16.69 sec.\n", "2025-09-23 11:21:29,723 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:21:29,726 - INFO - Going to convert document batch...\n", "2025-09-23 11:21:29,727 - INFO - Processing document 98_Objekt_98.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 97_Objekt_97.pdf (pages: 2)\n", "\n", "==============================\n", "[136/137] Processing: 98_Objekt_98.pdf\n", "Output dir: extracted_content/98_Objekt_98\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:21:49,217 - INFO - Finished converting document 98_Objekt_98.pdf in 17.78 sec.\n", "2025-09-23 11:21:49,258 - INFO - detected formats: [<InputFormat.PDF: 'pdf'>]\n", "2025-09-23 11:21:49,262 - INFO - Going to convert document batch...\n", "2025-09-23 11:21:49,263 - INFO - Processing document 99_Objekt_99.pdf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 98_Objekt_98.pdf (pages: 2)\n", "\n", "==============================\n", "[137/137] Processing: 99_Objekt_99.pdf\n", "Output dir: extracted_content/99_Objekt_99\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "/home/<USER>/dev/BA/.venv/lib/python3.13/site-packages/torch/utils/data/dataloader.py:666: UserWarning: 'pin_memory' argument is set as true but no accelerator is found, then device pinned memory won't be used.\n", "  warnings.warn(warn_msg)\n", "2025-09-23 11:22:08,138 - INFO - Finished converting document 99_Objekt_99.pdf in 17.21 sec.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Done: 99_Objekt_99.pdf (pages: 2)\n", "\n", "==============================\n", "<PERSON><PERSON>\n", "==============================\n", "Processed: 137 / 137 PDF(s)\n", "• 01_Objekt_1.pdf -> extracted_content/01_Objekt_1\n", "• 02_Objekt_2.pdf -> extracted_content/02_Objekt_2\n", "• 03_Objekt_3.pdf -> extracted_content/03_Objekt_3\n", "• 04_Objekt_4.pdf -> extracted_content/04_Objekt_4\n", "• 05_Objekt_5.pdf -> extracted_content/05_Objekt_5\n", "• 06_Objekt_6.pdf -> extracted_content/06_Objekt_6\n", "• 07_Objekt_7.pdf -> extracted_content/07_Objekt_7\n", "• 08_Objekt_8.pdf -> extracted_content/08_Objekt_8\n", "• 09_Objekt_9.pdf -> extracted_content/09_Objekt_9\n", "• 100_Objekt_100.pdf -> extracted_content/100_Objekt_100\n", "• 101_Objekt_101.pdf -> extracted_content/101_Objekt_101\n", "• 102_Objekt_102.pdf -> extracted_content/102_Objekt_102\n", "• 103_Objekt_103.pdf -> extracted_content/103_Objekt_103\n", "• 104_Objekt_104.pdf -> extracted_content/104_Objekt_104\n", "• 105_Objekt_105.pdf -> extracted_content/105_Objekt_105\n", "• 106_Objekt_106.pdf -> extracted_content/106_Objekt_106\n", "• 107_Objekt_107.pdf -> extracted_content/107_Objekt_107\n", "• 108_Objekt_108.pdf -> extracted_content/108_Objekt_108\n", "• 109_Objekt_109.pdf -> extracted_content/109_Objekt_109\n", "• 10_Objekt_10.pdf -> extracted_content/10_Objekt_10\n", "• 110_Objekt_110.pdf -> extracted_content/110_Objekt_110\n", "• 111_Objekt_111.pdf -> extracted_content/111_Objekt_111\n", "• 112_Objekt_112.pdf -> extracted_content/112_Objekt_112\n", "• 113_Objekt_113.pdf -> extracted_content/113_Objekt_113\n", "• 114_Objekt_114.pdf -> extracted_content/114_Objekt_114\n", "• 115_Objekt_115.pdf -> extracted_content/115_Objekt_115\n", "• 116_Objekt_116.pdf -> extracted_content/116_Objekt_116\n", "• 117_Objekt_117.pdf -> extracted_content/117_Objekt_117\n", "• 118_Objekt_118.pdf -> extracted_content/118_Objekt_118\n", "• 119_Objekt_119.pdf -> extracted_content/119_Objekt_119\n", "• 11_Objekt_11.pdf -> extracted_content/11_Objekt_11\n", "• 120_Objekt_120.pdf -> extracted_content/120_Objekt_120\n", "• 121_Objekt_121.pdf -> extracted_content/121_Objekt_121\n", "• 122_Objekt_122.pdf -> extracted_content/122_Objekt_122\n", "• 123_Objekt_123.pdf -> extracted_content/123_Objekt_123\n", "• 124_Objekt_124.pdf -> extracted_content/124_Objekt_124\n", "• 125_Objekt_125.pdf -> extracted_content/125_Objekt_125\n", "• 126_Objekt_126.pdf -> extracted_content/126_Objekt_126\n", "• 127_Objekt_127.pdf -> extracted_content/127_Objekt_127\n", "• 128_Objekt_128.pdf -> extracted_content/128_Objekt_128\n", "• 129_Objekt_129.pdf -> extracted_content/129_Objekt_129\n", "• 12_Objekt_12.pdf -> extracted_content/12_Objekt_12\n", "• 130_Objekt_130.pdf -> extracted_content/130_Objekt_130\n", "• 131_Objekt_131.pdf -> extracted_content/131_Objekt_131\n", "• 132_Objekt_132.pdf -> extracted_content/132_Objekt_132\n", "• 133_Objekt_133.pdf -> extracted_content/133_Objekt_133\n", "• 134_Objekt_134.pdf -> extracted_content/134_Objekt_134\n", "• 135_Objekt_135.pdf -> extracted_content/135_Objekt_135\n", "• 136_Objekt_136.pdf -> extracted_content/136_Objekt_136\n", "• 137_Objekt_137.pdf -> extracted_content/137_Objekt_137\n", "• 13_Objekt_13.pdf -> extracted_content/13_Objekt_13\n", "• 14_Objekt_14.pdf -> extracted_content/14_Objekt_14\n", "• 15_Objekt_15.pdf -> extracted_content/15_Objekt_15\n", "• 16_Objekt_16.pdf -> extracted_content/16_Objekt_16\n", "• 17_Objekt_17.pdf -> extracted_content/17_Objekt_17\n", "• 18_Objekt_18.pdf -> extracted_content/18_Objekt_18\n", "• 19_Objekt_19.pdf -> extracted_content/19_Objekt_19\n", "• 20_Objekt_20.pdf -> extracted_content/20_Objekt_20\n", "• 21_Objekt_21.pdf -> extracted_content/21_Objekt_21\n", "• 22_Objekt_22.pdf -> extracted_content/22_Objekt_22\n", "• 23_Objekt_23.pdf -> extracted_content/23_Objekt_23\n", "• 24_Objekt_24.pdf -> extracted_content/24_Objekt_24\n", "• 25_Objekt_25.pdf -> extracted_content/25_Objekt_25\n", "• 26_Objekt_26.pdf -> extracted_content/26_Objekt_26\n", "• 27_Objekt_27.pdf -> extracted_content/27_Objekt_27\n", "• 28_Objekt_28.pdf -> extracted_content/28_Objekt_28\n", "• 29_Objekt_29.pdf -> extracted_content/29_Objekt_29\n", "• 30_Objekt_30.pdf -> extracted_content/30_Objekt_30\n", "• 31_Objekt_31.pdf -> extracted_content/31_Objekt_31\n", "• 32_Objekt_32.pdf -> extracted_content/32_Objekt_32\n", "• 33_Objekt_33.pdf -> extracted_content/33_Objekt_33\n", "• 34_Objekt_34.pdf -> extracted_content/34_Objekt_34\n", "• 35_Objekt_35.pdf -> extracted_content/35_Objekt_35\n", "• 36_Objekt_36.pdf -> extracted_content/36_Objekt_36\n", "• 37_Objekt_37.pdf -> extracted_content/37_Objekt_37\n", "• 38_Objekt_38.pdf -> extracted_content/38_Objekt_38\n", "• 39_Objekt_39.pdf -> extracted_content/39_Objekt_39\n", "• 40_Objekt_40.pdf -> extracted_content/40_Objekt_40\n", "• 41_Objekt_41.pdf -> extracted_content/41_Objekt_41\n", "• 42_Objekt_42.pdf -> extracted_content/42_Objekt_42\n", "• 43_Objekt_43.pdf -> extracted_content/43_Objekt_43\n", "• 44_Objekt_44.pdf -> extracted_content/44_Objekt_44\n", "• 45_Objekt_45.pdf -> extracted_content/45_Objekt_45\n", "• 46_Objekt_46.pdf -> extracted_content/46_Objekt_46\n", "• 47_Objekt_47.pdf -> extracted_content/47_Objekt_47\n", "• 48_Objekt_48.pdf -> extracted_content/48_Objekt_48\n", "• 49_Objekt_49.pdf -> extracted_content/49_Objekt_49\n", "• 50_Objekt_50.pdf -> extracted_content/50_Objekt_50\n", "• 51_Objekt_51.pdf -> extracted_content/51_Objekt_51\n", "• 52_Objekt_52.pdf -> extracted_content/52_Objekt_52\n", "• 53_Objekt_53.pdf -> extracted_content/53_Objekt_53\n", "• 54_Objekt_54.pdf -> extracted_content/54_Objekt_54\n", "• 55_Objekt_55.pdf -> extracted_content/55_Objekt_55\n", "• 56_Objekt_56.pdf -> extracted_content/56_Objekt_56\n", "• 57_Objekt_57.pdf -> extracted_content/57_Objekt_57\n", "• 58_Objekt_58.pdf -> extracted_content/58_Objekt_58\n", "• 59_Objekt_59.pdf -> extracted_content/59_Objekt_59\n", "• 60_Objekt_60.pdf -> extracted_content/60_Objekt_60\n", "• 61_Objekt_61.pdf -> extracted_content/61_Objekt_61\n", "• 62_Objekt_62.pdf -> extracted_content/62_Objekt_62\n", "• 63_Objekt_63.pdf -> extracted_content/63_Objekt_63\n", "• 64_Objekt_64.pdf -> extracted_content/64_Objekt_64\n", "• 65_Objekt_65.pdf -> extracted_content/65_Objekt_65\n", "• 66_Objekt_66.pdf -> extracted_content/66_Objekt_66\n", "• 67_Objekt_67.pdf -> extracted_content/67_Objekt_67\n", "• 68_Objekt_68.pdf -> extracted_content/68_Objekt_68\n", "• 69_Objekt_69.pdf -> extracted_content/69_Objekt_69\n", "• 70_Objekt_70.pdf -> extracted_content/70_Objekt_70\n", "• 71_Objekt_71.pdf -> extracted_content/71_Objekt_71\n", "• 72_Objekt_72.pdf -> extracted_content/72_Objekt_72\n", "• 73_Objekt_73.pdf -> extracted_content/73_Objekt_73\n", "• 74_Objekt_74.pdf -> extracted_content/74_Objekt_74\n", "• 75_Objekt_75.pdf -> extracted_content/75_Objekt_75\n", "• 76_Objekt_76.pdf -> extracted_content/76_Objekt_76\n", "• 77_Objekt_77.pdf -> extracted_content/77_Objekt_77\n", "• 78_Objekt_78.pdf -> extracted_content/78_Objekt_78\n", "• 79_Objekt_79.pdf -> extracted_content/79_Objekt_79\n", "• 80_Objekt_80.pdf -> extracted_content/80_Objekt_80\n", "• 81_Objekt_81.pdf -> extracted_content/81_Objekt_81\n", "• 82_Objekt_82.pdf -> extracted_content/82_Objekt_82\n", "• 83_Objekt_83.pdf -> extracted_content/83_Objekt_83\n", "• 84_Objekt_84.pdf -> extracted_content/84_Objekt_84\n", "• 85_Objekt_85.pdf -> extracted_content/85_Objekt_85\n", "• 86_Objekt_86.pdf -> extracted_content/86_Objekt_86\n", "• 87_Objekt_87.pdf -> extracted_content/87_Objekt_87\n", "• 88_Objekt_88.pdf -> extracted_content/88_Objekt_88\n", "• 89_Objekt_89.pdf -> extracted_content/89_Objekt_89\n", "• 90_Objekt_90.pdf -> extracted_content/90_Objekt_90\n", "• 91_Objekt_91.pdf -> extracted_content/91_Objekt_91\n", "• 92_Objekt_92.pdf -> extracted_content/92_Objekt_92\n", "• 93_Objekt_93.pdf -> extracted_content/93_Objekt_93\n", "• 94_Objekt_94.pdf -> extracted_content/94_Objekt_94\n", "• 95_Objekt_95.pdf -> extracted_content/95_Objekt_95\n", "• 96_Objekt_96.pdf -> extracted_content/96_Objekt_96\n", "• 97_Objekt_97.pdf -> extracted_content/97_Objekt_97\n", "• 98_Objekt_98.pdf -> extracted_content/98_Objekt_98\n", "• 99_Objekt_99.pdf -> extracted_content/99_Objekt_99\n"]}], "source": ["# Batch convert all PDFs in assets/\n", "from pathlib import Path\n", "import os\n", "import shutil\n", "import json\n", "from datetime import datetime\n", "\n", "assets_dir = Path(\"assets\")\n", "output_root = Path(\"extracted_content\")\n", "output_root.mkdir(parents=True, exist_ok=True)\n", "\n", "if not assets_dir.exists():\n", "    raise FileNotFoundError(f\"Assets directory not found: {assets_dir.resolve()}\")\n", "\n", "pdf_files = sorted([p for p in assets_dir.rglob(\"*.pdf\") if p.is_file()])\n", "print(f\"Found {len(pdf_files)} PDF(s) under {assets_dir}\")\n", "\n", "if not pdf_files:\n", "    print(\"No PDFs to process. Add files to the assets/ folder.\")\n", "\n", "results = []\n", "errors = []\n", "\n", "# Reuse existing converter if present, else create one\n", "try:\n", "    converter\n", "except NameError:\n", "    from docling.document_converter import DocumentConverter\n", "    converter = DocumentConverter()\n", "\n", "for idx, pdf_path in enumerate(pdf_files, start=1):\n", "    rel = pdf_path.relative_to(assets_dir)\n", "    stem = pdf_path.stem\n", "    # Keep subfolder structure under extracted_content\n", "    target_dir = output_root / rel.parent / stem\n", "    target_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    print(\"\\n==============================\")\n", "    print(f\"[{idx}/{len(pdf_files)}] Processing: {rel}\")\n", "    print(f\"Output dir: {target_dir}\")\n", "\n", "    try:\n", "        # Convert\n", "        result = converter.convert(str(pdf_path))\n", "        document = result.document\n", "\n", "        # Export markdown\n", "        md_text = document.export_to_markdown()\n", "        md_file = target_dir / \"extracted_content.md\"\n", "        with md_file.open(\"w\", encoding=\"utf-8\") as f:\n", "            f.write(md_text)\n", "\n", "        # Export JSON\n", "        json_data = document.export_to_dict()\n", "        json_file = target_dir / \"extracted_content.json\"\n", "        with json_file.open(\"w\", encoding=\"utf-8\") as f:\n", "            json.dump(json_data, f, indent=2, ensure_ascii=False)\n", "\n", "        # Copy original PDF into the folder\n", "        pdf_copy = target_dir / f\"{stem}.pdf\"\n", "        # If source and destination are same path, skip copy\n", "        if pdf_path.resolve() != pdf_copy.resolve():\n", "            shutil.copy2(pdf_path, pdf_copy)\n", "\n", "        page_count = len(getattr(document, \"pages\", []))\n", "        results.append({\n", "            \"pdf\": str(rel),\n", "            \"output_dir\": str(target_dir.relative_to(output_root.parent)),\n", "            \"pages\": page_count,\n", "            \"markdown\": str(md_file),\n", "            \"json\": str(json_file)\n", "        })\n", "        print(f\"✅ Done: {rel} (pages: {page_count})\")\n", "    except Exception as e:\n", "        errors.append({\"pdf\": str(rel), \"error\": str(e)})\n", "        print(f\"❌ Error processing {rel}: {e}\")\n", "\n", "# Summary\n", "print(\"\\n==============================\")\n", "print(\"Batch Summary\")\n", "print(\"==============================\")\n", "print(f\"Processed: {len(results)} / {len(pdf_files)} PDF(s)\")\n", "if results:\n", "    for r in results:\n", "        print(f\"• {r['pdf']} -> {r['output_dir']}\")\n", "if errors:\n", "    print(\"\\nErrors:\")\n", "    for err in errors:\n", "        print(f\"• {err['pdf']}: {err['error']}\")"]}], "metadata": {"kernelspec": {"display_name": "ba (3.13.7)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}