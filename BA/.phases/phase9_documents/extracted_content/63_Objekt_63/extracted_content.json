{"schema_name": "DoclingDocument", "version": "1.7.0", "name": "63_Obje<PERSON>_63", "origin": {"mimetype": "application/pdf", "binary_hash": 9359361118216847943, "filename": "63_Objekt_63.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/pictures/0"}, {"$ref": "#/texts/4"}, {"$ref": "#/pictures/1"}, {"$ref": "#/pictures/2"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/17"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/44"}, {"$ref": "#/tables/2"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/49"}, {"$ref": "#/pictures/4"}, {"$ref": "#/tables/3"}, {"$ref": "#/tables/4"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/pictures/5"}, {"$ref": "#/tables/5"}, {"$ref": "#/texts/59"}, {"$ref": "#/tables/6"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/48"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 49.076, "t": 796.2509588867186, "r": 141.056, "b": 788.2739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Deka-ImmobilienEuropa", "text": "Deka-ImmobilienEuropa", "level": 1}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 49.076, "t": 782.3309588867187, "r": 139.641, "b": 774.3539588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "'Kaufhaus Gerngross'", "text": "'Kaufhaus Gerngross'", "level": 1}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 768.3299588867187, "r": 128.243, "b": 761.5229588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "Mariahilferstraße 42-48", "text": "Mariahilferstraße 42-48"}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 755.2509588867186, "r": 85.265, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "1070 Wien", "text": "1070 Wien"}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 595.8309588867187, "r": 79.193, "b": 589.5559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "level": 1}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 476.0, "t": 771.0136922200521, "r": 510.6666666666667, "b": 759.0136922200521, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 733.1189588867187, "r": 286.753, "b": 726.4649588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 723.3989588867187, "r": 286.753, "b": 716.7449588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 713.6789588867186, "r": 286.753, "b": 707.0249588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 703.9589588867186, "r": 286.753, "b": 697.3049588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 694.2389588867187, "r": 286.753, "b": 687.5849588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 742.9509588867187, "r": 298.292, "b": 736.6759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "Lage", "text": "Lage", "level": 1}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 732.6309588867186, "r": 442.852, "b": 726.3559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "<PERSON>hr gute Sichtlage im 7. Bezirk, Landmarkbuilding", "text": "<PERSON>hr gute Sichtlage im 7. Bezirk, Landmarkbuilding"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 722.9109588867186, "r": 346.843, "b": 716.6359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "Beste Einkaufslage", "text": "Beste Einkaufslage"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 713.1909588867187, "r": 476.536, "b": 706.9159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 57]}], "orig": "Sehr gute Anbindungen an ÖPNV sowie den Individualverkehr", "text": "Sehr gute Anbindungen an ÖPNV sowie den Individualverkehr"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 703.4709588867187, "r": 409.09, "b": 697.1959588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 33]}], "orig": "Westbahnhof in wenigen Gehminuten", "text": "Westbahnhof in wenigen Gehminuten"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 693.7509588867186, "r": 471.89, "b": 687.4759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 57]}], "orig": "Exklusive Einkaufsmöglichkeiten in unmittelbarer Umgebung", "text": "Exklusive Einkaufsmöglichkeiten in unmittelbarer Umgebung"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 455.036, "t": 596.3109588867187, "r": 521.469, "b": 590.0359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "Stichtag: 31.12.2022", "text": "Stichtag: 31.12.2022", "level": 1}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 530.9519588867187, "r": 266.508, "b": 526.9099588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 106]}, {"page_no": 1, "bbox": {"l": 48.476, "t": 524.8319588867187, "r": 272.824, "b": 520.7899588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [107, 218]}], "orig": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden", "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 518.7129588867186, "r": 277.557, "b": 514.6709588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 110]}, {"page_no": 1, "bbox": {"l": 48.476, "t": 512.5929588867186, "r": 123.664, "b": 508.55095888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [111, 150]}], "orig": "Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Si<PERSON> bitte den jeweiligen Jahres-/Halbjahresberichten.", "text": "Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Si<PERSON> bitte den jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 499.23095888671867, "r": 239.14, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "text": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "level": 1}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 499.23095888671867, "r": 405.309, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Verkehrswerthistorie in EUR in TSD.*", "text": "Verkehrswerthistorie in EUR in TSD.*"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 172.937, "t": 471.7639588867187, "r": 207.418, "b": 467.61095888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "Handel: 95,74 %", "text": "Handel: 95,74 %"}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 215.594, "t": 471.7639588867187, "r": 242.777, "b": 467.61095888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "Büro: 2,58 %", "text": "Büro: 2,58 %"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 172.937, "t": 437.1679588867187, "r": 207.721, "b": 433.0159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "Sonstige: 1,68 %", "text": "Sonstige: 1,68 %"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 305.121, "t": 476.4999588867187, "r": 323.643, "b": 471.9389588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "161.325", "text": "161.325"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 328.256, "t": 475.4349588867187, "r": 346.778, "b": 470.8739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "166.790", "text": "166.790"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 361.928, "t": 478.9799588867187, "r": 380.45, "b": 474.41895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "170.140", "text": "170.140"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 389.015, "t": 478.6149588867187, "r": 407.537, "b": 474.05395888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "170.695", "text": "170.695"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 420.712, "t": 474.82795888671865, "r": 439.234, "b": 470.26695888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "155.365", "text": "155.365"}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 450.433, "t": 470.07795888671865, "r": 468.955, "b": 465.51695888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "140.320", "text": "140.320"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 478.224, "t": 470.20695888671867, "r": 496.746, "b": 465.6459588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "138.930", "text": "138.930"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 298.581, "t": 434.0019588867187, "r": 301.442, "b": 429.4419588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 285.763, "t": 448.0549588867187, "r": 301.445, "b": 443.4939588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "50.000", "text": "50.000"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.918, "t": 462.1079588867187, "r": 301.44, "b": 457.5469588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "100.000", "text": "100.000"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.918, "t": 476.1599588867187, "r": 301.44, "b": 471.59895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "150.000", "text": "150.000"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.913, "t": 490.2199588867187, "r": 301.435, "b": 485.65895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "200.000", "text": "200.000"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 315.459, "t": 428.0119588867187, "r": 326.841, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2016", "text": "2016"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 346.214, "t": 428.0119588867187, "r": 357.596, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2017", "text": "2017"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 376.97, "t": 428.0119588867187, "r": 388.352, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2018", "text": "2018"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 407.725, "t": 428.0119588867187, "r": 419.107, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2019", "text": "2019"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 438.48, "t": 428.0119588867187, "r": 449.862, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2020", "text": "2020"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 469.235, "t": 428.0119588867187, "r": 480.617, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2021", "text": "2021"}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 498.565, "t": 428.0119588867187, "r": 512.788, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "2022*", "text": "2022*"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 405.99095888671866, "r": 144.55, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Auslaufende Mietverträge**", "text": "Auslaufende Mietverträge**", "level": 1}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 294.5519588867187, "r": 250.624, "b": 290.50995888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 99]}], "orig": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die", "text": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die"}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 288.43295888671867, "r": 270.351, "b": 284.39095888671864, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 105]}, {"page_no": 1, "bbox": {"l": 48.476, "t": 282.3119588867187, "r": 220.984, "b": 278.26995888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [106, 192]}], "orig": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt.", "text": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt."}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 269.4309588867186, "r": 116.035, "b": 263.15595888671874, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "Objektbeschreibung", "text": "Objektbeschreibung", "level": 1}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 149.65995888671864, "r": 116.985, "b": 144.93795888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": " zurück zum Inhaltsverzeichnis", "text": " zurück zum Inhaltsverzeichnis", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 405.99095888671866, "r": 394.671, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "Fläche nach Vermietungssituation", "text": "Fläche nach Vermietungssituation", "level": 1}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 382.534, "t": 381.38295888671865, "r": 477.923, "b": 377.23095888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 46]}], "orig": "Fläche ist mehr als 12 Monate vermietet (69 %)", "text": "Fläche ist mehr als 12 Monate vermietet (69 %)"}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 382.534, "t": 368.12795888671866, "r": 493.896, "b": 363.9759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "Fläche ist maximal weitere 12 Monate vermietet (17 %)", "text": "Fläche ist maximal weitere 12 Monate vermietet (17 %)"}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 382.534, "t": 354.8739588867187, "r": 437.133, "b": 350.7209588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Fläche im Leerstand (13 %)", "text": "Fläche im Leerstand (13 %)"}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 382.534, "t": 341.6189588867187, "r": 450.726, "b": 337.46695888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 34]}], "orig": "Fläche unbefristet vermietet (1 %)", "text": "Fläche unbefristet vermietet (1 %)"}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 142.87195888671863, "r": 288.357, "b": 138.8299588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 112]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt.", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt."}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 137.59195888671866, "r": 500.133, "b": 98.50995888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1405]}], "orig": "Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit", "text": "Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit"}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 796.2509588867186, "r": 141.056, "b": 761.5229588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 66]}], "orig": "Deka-ImmobilienEuropa 'Kaufhaus Gerngross' Mariahilferstraße 42-48", "text": "Deka-ImmobilienEuropa 'Kaufhaus Gerngross' Mariahilferstraße 42-48"}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 755.2509588867186, "r": 85.265, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "1070 Wien", "text": "1070 Wien"}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.839, "t": 708.1479588867187, "r": 129.469, "b": 701.8729588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "Nachhaltigkeitsangaben", "text": "Nachhaltigkeitsangaben", "level": 1}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 663.5109588867186, "r": 279.67, "b": 657.2359588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 64]}], "orig": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "text": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "level": 1}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 472.15195888671866, "r": 388.568, "b": 455.87095888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 369]}], "orig": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.", "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 354.25995888671866, "r": 116.986, "b": 349.5379588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": " zurück zum Inhaltsverzeichnis", "text": " zurück zum Inhaltsverzeichnis", "level": 1}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 346.03195888671866, "r": 288.357, "b": 341.9899588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 112]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt.", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt."}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 340.7519588867187, "r": 500.133, "b": 301.66995888671863, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1405]}], "orig": "Informationen stellen weder ein <PERSON>, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und", "text": "Informationen stellen weder ein <PERSON>, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und"}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 393.596, "t": 465.41395888671866, "r": 456.543, "b": 402.4009588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 166]}], "orig": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien", "text": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 45.62689208984375, "t": 746.2975997924805, "r": 231.04296875, "b": 605.7036285400391, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/5"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 470.7153625488281, "t": 802.1427993774414, "r": 524.6509399414062, "b": 750.8764953613281, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 279.3397521972656, "t": 742.5298614501953, "r": 524.890869140625, "b": 636.7347106933594, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 46.09502410888672, "t": 499.8514709472656, "r": 525.0121459960938, "b": 414.6170654296875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 285.5513610839844, "t": 392.9180908203125, "r": 495.9518127441406, "b": 328.7520751953125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 2, "bbox": {"l": 470.7985534667969, "t": 802.0629348754883, "r": 524.8853149414062, "b": 750.3274917602539, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 279.3397521972656, "t": 742.5298614501953, "r": 524.890869140625, "b": 636.7347106933594, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 282.476, "t": 108.56140000000005, "r": 442.852, "b": 115.32440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Sehr gute Sichtlage im 7. Bezirk, Landmarkbuilding", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 118.28140000000008, "r": 344.815, "b": 125.04440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Beste Einkaufslage", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 128.0014000000001, "r": 476.536, "b": 134.76440000000002, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Sehr gute Anbindungen an ÖPNV sowie den Individualverkehr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 137.72140000000013, "r": 405.182, "b": 144.48440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Westbahnhof in wenigen Gehminuten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 147.44140000000004, "r": 471.89, "b": 154.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Exklusive Einkaufsmöglichkeiten in unmittelbarer Umgebung", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 98.72940000000006, "r": 298.292, "b": 105.00440000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Lage", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 6, "num_cols": 1, "grid": [[{"bbox": {"l": 282.476, "t": 98.72940000000006, "r": 298.292, "b": 105.00440000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Lage", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 108.56140000000005, "r": 442.852, "b": 115.32440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Sehr gute Sichtlage im 7. Bezirk, Landmarkbuilding", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 118.28140000000008, "r": 344.815, "b": 125.04440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Beste Einkaufslage", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 128.0014000000001, "r": 476.536, "b": 134.76440000000002, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Sehr gute Anbindungen an ÖPNV sowie den Individualverkehr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 137.72140000000013, "r": 405.182, "b": 144.48440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Westbahnhof in wenigen Gehminuten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 147.44140000000004, "r": 471.89, "b": 154.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Exklusive Einkaufsmöglichkeiten in unmittelbarer Umgebung", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.359256744384766, "t": 591.0231475830078, "r": 524.8744506835938, "b": 531.0519409179688, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 306.083, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.888,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 376.965, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1904 (1980/1997/2010) / 2004", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 336.07, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "138.930.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 314.086, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4,72 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 333.915, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Direktinvestment", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.365.168 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 6, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 306.083, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.888,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 376.965, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1904 (1980/1997/2010) / 2004", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 333.915, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Direktinvestment", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 336.07, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "138.930.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 314.086, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4,72 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.365.168 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.616905212402344, "t": 399.4733581542969, "r": 275.2342529296875, "b": 296.23431396484375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 131.516, "t": 445.8894000000001, "r": 140.31, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 464.60940000000005, "r": 140.246, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.297", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 464.60940000000005, "r": 181.192, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "20%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 464.60940000000005, "r": 273.319, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.151.055", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 473.84940000000006, "r": 140.246, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.105", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 473.84940000000006, "r": 181.194, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "8%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 473.84940000000006, "r": 273.32, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "548.738", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 483.08940000000007, "r": 140.248, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "324", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 483.08940000000007, "r": 181.194, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 483.08940000000007, "r": 273.32, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "225.081", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 492.3294000000001, "r": 140.246, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.781", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 492.3294000000001, "r": 181.194, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "7%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 492.3294000000001, "r": 273.32, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "647.412", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 501.5694000000001, "r": 140.246, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.073", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 501.5694000000001, "r": 181.192, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "19%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 501.5694000000001, "r": 273.319, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.673.413", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 520.0494000000001, "r": 140.246, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.759", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 520.0494000000001, "r": 181.192, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "18%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 520.0494000000001, "r": 273.319, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.224.540", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 529.2894000000001, "r": 140.246, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.274", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 529.2894000000001, "r": 181.192, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "27%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 529.2894000000001, "r": 273.319, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.375.974", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 538.5294000000001, "r": 140.248, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "372", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 538.5294000000001, "r": 181.194, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 255.598, "t": 538.5294000000001, "r": 273.327, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "7.191", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 538.0494000000001, "r": 81.787, "b": 544.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "unbefristet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}], "num_rows": 11, "num_cols": 4, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 131.516, "t": 445.8894000000001, "r": 140.31, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 464.60940000000005, "r": 140.246, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.297", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 464.60940000000005, "r": 181.192, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "20%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 464.60940000000005, "r": 273.319, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.151.055", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 473.84940000000006, "r": 140.246, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.105", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 473.84940000000006, "r": 181.194, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "8%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 473.84940000000006, "r": 273.32, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "548.738", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 483.08940000000007, "r": 140.248, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "324", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 483.08940000000007, "r": 181.194, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 483.08940000000007, "r": 273.32, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "225.081", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 492.3294000000001, "r": 140.246, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.781", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 492.3294000000001, "r": 181.194, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "7%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 492.3294000000001, "r": 273.32, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "647.412", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 501.5694000000001, "r": 140.246, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.073", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 501.5694000000001, "r": 181.192, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "19%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 501.5694000000001, "r": 273.319, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.673.413", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 520.0494000000001, "r": 140.246, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.759", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 520.0494000000001, "r": 181.192, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "18%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 520.0494000000001, "r": 273.319, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.224.540", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 529.2894000000001, "r": 140.246, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.274", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 529.2894000000001, "r": 181.192, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "27%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 529.2894000000001, "r": 273.319, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.375.974", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 538.0494000000001, "r": 81.787, "b": 544.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "unbefristet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 538.5294000000001, "r": 140.248, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "372", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 538.5294000000001, "r": 181.194, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 255.598, "t": 538.5294000000001, "r": 273.327, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "7.191", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 279.7021484375, "t": 324.5972900390625, "r": 525.03369140625, "b": 276.4705810546875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 282.478, "t": 520.0494000000001, "r": 326.258, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.481, "t": 520.0494000000001, "r": 429.275, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.28, "t": 520.0494000000001, "r": 515.518, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.478, "t": 529.2894000000001, "r": 314.452, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.518, "t": 529.2894000000001, "r": 429.205, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "31.097", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.556, "t": 529.2894000000001, "r": 515.509, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.48, "t": 538.5294000000001, "r": 331.516, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.52, "t": 538.5294000000001, "r": 429.208, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26.985", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.523, "t": 538.5294000000001, "r": 515.519, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "87%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 547.7694000000001, "r": 429.206, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.111", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.516, "t": 547.7694000000001, "r": 515.512, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "13%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 421.316, "t": 557.2494000000002, "r": 429.21, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "29", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 5, "num_cols": 3, "grid": [[{"bbox": {"l": 282.478, "t": 520.0494000000001, "r": 326.258, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.481, "t": 520.0494000000001, "r": 429.275, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.28, "t": 520.0494000000001, "r": 515.518, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.478, "t": 529.2894000000001, "r": 314.452, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.518, "t": 529.2894000000001, "r": 429.205, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "31.097", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.556, "t": 529.2894000000001, "r": 515.509, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.48, "t": 538.5294000000001, "r": 331.516, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.52, "t": 538.5294000000001, "r": 429.208, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26.985", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.523, "t": 538.5294000000001, "r": 515.519, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "87%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 547.7694000000001, "r": 429.206, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.111", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.516, "t": 547.7694000000001, "r": 515.512, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "13%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 421.316, "t": 557.2494000000002, "r": 429.21, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "29", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.224117279052734, "t": 263.15863037109375, "r": 524.7843627929688, "b": 148.97882080078125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 581.9694000000001, "r": 230.906, "b": 588.9754, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ 6-geschossiges, attraktives und modernes Shoppingcenter", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 591.4494000000001, "r": 167.582, "b": 598.6954000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Vorgehängte Metallfassade ab 1. OG", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 600.6894000000001, "r": 177.93, "b": 607.9354000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ 2 Dachterrassen mit Gastronomiebetrieb", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 609.9294000000001, "r": 211.359, "b": 617.1754000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Wintergartenartige Stahl-Glaskonstruktion im 6.OG", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 619.1694000000001, "r": 244.929, "b": 626.4154000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Öffentliches Parkhaus mit Zugängen grenzt direkt an Gebäude", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 628.4094000000001, "r": 242.653, "b": 635.6554000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Offener 'Lichthof' durchzieht das Erdgeschoss bis zum 5. OG", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 637.6494, "r": 217.439, "b": 644.8954000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Erschließung durch Rolltreppen und Personenaufzüge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 646.8894, "r": 217.605, "b": 654.1354000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Ladehof und Lastenaufzüge für die Warenanlieferung", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 656.1294, "r": 126.058, "b": 663.3754000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Gebäude ist klimatisiert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 665.3694, "r": 162.874, "b": 672.6154000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Wärmeversorgung über Fernwärme", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 674.6094, "r": 201.867, "b": 681.8554000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Brandschutzmeldesystem im gesamten Gebäude", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 683.8494000000001, "r": 166.472, "b": 691.0954, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Große Schaufenster in der EG-Fläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 12, "num_cols": 1, "grid": [[{"bbox": {"l": 48.836, "t": 581.9694000000001, "r": 230.906, "b": 588.9754, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ 6-geschossiges, attraktives und modernes Shoppingcenter", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 591.4494000000001, "r": 167.582, "b": 598.6954000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Vorgehängte Metallfassade ab 1. OG", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 600.6894000000001, "r": 177.93, "b": 607.9354000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ 2 Dachterrassen mit Gastronomiebetrieb", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 609.9294000000001, "r": 211.359, "b": 617.1754000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Wintergartenartige Stahl-Glaskonstruktion im 6.OG", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 619.1694000000001, "r": 244.929, "b": 626.4154000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Öffentliches Parkhaus mit Zugängen grenzt direkt an Gebäude", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 628.4094000000001, "r": 242.653, "b": 635.6554000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Offener 'Lichthof' durchzieht das Erdgeschoss bis zum 5. OG", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 637.6494, "r": 217.439, "b": 644.8954000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Erschließung durch Rolltreppen und Personenaufzüge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 646.8894, "r": 217.605, "b": 654.1354000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Ladehof und Lastenaufzüge für die Warenanlieferung", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 656.1294, "r": 126.058, "b": 663.3754000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Gebäude ist klimatisiert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 665.3694, "r": 162.874, "b": 672.6154000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Wärmeversorgung über Fernwärme", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 674.6094, "r": 201.867, "b": 681.8554000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Brandschutzmeldesystem im gesamten Gebäude", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 683.8494000000001, "r": 166.472, "b": 691.0954, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Große Schaufenster in der EG-Fläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.9577751159668, "t": 702.1164093017578, "r": 525.3843994140625, "b": 670.36669921875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 162.068, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ BREEAM Europe Commercial: good", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 204.296, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): 157,7", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 195.327, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: keine <PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 3, "num_cols": 1, "grid": [[{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 162.068, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ BREEAM Europe Commercial: good", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 204.296, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): 157,7", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 195.327, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: keine <PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.42855453491211, "t": 657.68115234375, "r": 525.2669067382812, "b": 472.8797607421875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 207.44940000000008, "r": 391.12, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "60 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.436, "t": 226.64940000000013, "r": 391.003, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "37,67 J<PERSON>re", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 368.996, "t": 187.88940000000002, "r": 390.684, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "31.427", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 197.96940000000006, "r": 391.12, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 265.5294000000001, "r": 117.433, "b": 271.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.237, "t": 265.5294000000001, "r": 391.037, "b": 271.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "295.840 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 275.24940000000004, "r": 391.036, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "294.607 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 284.96940000000006, "r": 391.035, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.400.615 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.365.168 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 246.08940000000007, "r": 391.036, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "626.039 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.237, "t": 255.8094000000001, "r": 391.037, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "184.129 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 322.6514000000001, "r": 95.253, "b": 328.92740000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 322.2894000000001, "r": 390.67, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "138.930.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23,53", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19,05", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.964.553 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 303.8094000000001, "r": 390.672, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "73.600.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 313.0494000000001, "r": 390.67, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "107.330.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.876, "t": 350.0094000000001, "r": 390.707, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "52,45%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 348.956, "t": 359.4894000000001, "r": 390.677, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.464,95 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 19, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 368.996, "t": 187.88940000000002, "r": 390.684, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "31.427", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 197.96940000000006, "r": 391.12, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 207.44940000000008, "r": 391.12, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "60 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.436, "t": 226.64940000000013, "r": 391.003, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "37,67 J<PERSON>re", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.365.168 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 246.08940000000007, "r": 391.036, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "626.039 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.237, "t": 255.8094000000001, "r": 391.037, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "184.129 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 265.5294000000001, "r": 117.433, "b": 271.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.237, "t": 265.5294000000001, "r": 391.037, "b": 271.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "295.840 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 275.24940000000004, "r": 391.036, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "294.607 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 284.96940000000006, "r": 391.035, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.400.615 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.964.553 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 303.8094000000001, "r": 390.672, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "73.600.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 313.0494000000001, "r": 390.67, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "107.330.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 322.6514000000001, "r": 95.253, "b": 328.92740000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 322.2894000000001, "r": 390.67, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "138.930.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23,53", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19,05", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.876, "t": 350.0094000000001, "r": 390.707, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "52,45%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 348.956, "t": 359.4894000000001, "r": 390.677, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.464,95 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 1}, "2": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 2}}}