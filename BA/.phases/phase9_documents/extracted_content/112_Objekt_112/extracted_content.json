{"schema_name": "DoclingDocument", "version": "1.7.0", "name": "112_Objekt_112", "origin": {"mimetype": "application/pdf", "binary_hash": 4237429783593772321, "filename": "112_Objekt_112.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/pictures/0"}, {"$ref": "#/texts/3"}, {"$ref": "#/pictures/1"}, {"$ref": "#/pictures/2"}, {"$ref": "#/texts/15"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/33"}, {"$ref": "#/pictures/4"}, {"$ref": "#/texts/34"}, {"$ref": "#/tables/0"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/36"}, {"$ref": "#/pictures/5"}, {"$ref": "#/texts/42"}, {"$ref": "#/tables/2"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/groups/1"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/pictures/6"}, {"$ref": "#/tables/3"}, {"$ref": "#/texts/62"}, {"$ref": "#/tables/4"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/pictures/2"}, "children": [{"$ref": "#/texts/5"}], "content_layer": "body", "name": "group", "label": "list"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 49.076, "t": 796.2509588867186, "r": 141.056, "b": 788.2739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Deka-ImmobilienEuropa", "text": "Deka-ImmobilienEuropa"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 768.3299588867187, "r": 100.171, "b": 761.5229588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "Via Broletto 16", "text": "Via Broletto 16", "level": 1}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 755.2509588867186, "r": 99.064, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "20126 Mailand", "text": "20126 Mailand", "level": 1}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 595.8309588867187, "r": 79.193, "b": 589.5559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "level": 1}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 476.0, "t": 771.0136922200521, "r": 510.6666666666667, "b": 759.0136922200521, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 733.1189588867187, "r": 470.436, "b": 726.3559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 59]}], "orig": "■ Im CBD, fast am Piazza Cordusio, Dom-Platz 300 m entfernt", "text": "■ Im CBD, fast am Piazza Cordusio, Dom-Platz 300 m entfernt", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 723.3989588867187, "r": 286.753, "b": 716.7449588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 713.6789588867186, "r": 286.753, "b": 707.0249588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 703.9589588867186, "r": 286.753, "b": 697.3049588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 694.2389588867187, "r": 286.753, "b": 687.5849588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 742.9509588867187, "r": 298.292, "b": 736.6759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "Lage", "text": "Lage", "level": 1}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 722.9109588867186, "r": 373.869, "b": 716.6359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "Sehr gute ÖPNV-Anbindung", "text": "Sehr gute ÖPNV-Anbindung"}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 713.1909588867187, "r": 441.53, "b": 706.9159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 49]}], "orig": "Viele Einkaufsmöglichkeiten in unmittelbarer Nähe", "text": "Viele Einkaufsmöglichkeiten in unmittelbarer Nähe"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 703.4709588867187, "r": 477.762, "b": 697.1959588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 58]}], "orig": "Reichhaltiges gastronomisches Angebot in direkter Umgebung", "text": "Reichhaltiges gastronomisches Angebot in direkter Umgebung"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 693.7509588867186, "r": 457.245, "b": 687.4759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Flughäfen Linate in ca. 20, Malpensa in 60 Fahrminuten", "text": "Flughäfen Linate in ca. 20, Malpensa in 60 Fahrminuten"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 455.036, "t": 596.3109588867187, "r": 521.469, "b": 590.0359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "Stichtag: 31.12.2022", "text": "Stichtag: 31.12.2022", "level": 1}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 499.23095888671867, "r": 405.309, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Verkehrswerthistorie in EUR in TSD.*", "text": "Verkehrswerthistorie in EUR in TSD.*", "level": 1}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 450.432, "t": 483.61795888671867, "r": 468.954, "b": 479.0569588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "116.655", "text": "116.655"}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 478.223, "t": 453.45495888671866, "r": 496.746, "b": 448.8939588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "113.380", "text": "113.380"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.912, "t": 434.0039588867187, "r": 301.434, "b": 429.44295888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "111.000", "text": "111.000"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.912, "t": 443.3739588867187, "r": 301.434, "b": 438.81295888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "112.000", "text": "112.000"}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.912, "t": 452.7449588867187, "r": 301.434, "b": 448.1839588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "113.000", "text": "113.000"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.912, "t": 462.1149588867187, "r": 301.434, "b": 457.55395888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "114.000", "text": "114.000"}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.912, "t": 471.4849588867187, "r": 301.434, "b": 466.92395888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "115.000", "text": "115.000"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.912, "t": 480.8549588867187, "r": 301.434, "b": 476.29395888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "116.000", "text": "116.000"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.913, "t": 490.2199588867187, "r": 301.435, "b": 485.65895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "117.000", "text": "117.000"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 315.459, "t": 428.0119588867187, "r": 326.841, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2016", "text": "2016"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 346.214, "t": 428.0119588867187, "r": 357.596, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2017", "text": "2017"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 376.97, "t": 428.0119588867187, "r": 388.352, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2018", "text": "2018"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 407.725, "t": 428.0119588867187, "r": 419.107, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2019", "text": "2019"}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 438.48, "t": 428.0119588867187, "r": 449.862, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2020", "text": "2020"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 469.235, "t": 428.0119588867187, "r": 480.617, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2021", "text": "2021"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 498.565, "t": 428.0119588867187, "r": 512.788, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "2022*", "text": "2022*"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 405.99095888671866, "r": 394.671, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "Fläche nach Vermietungssituation", "text": "Fläche nach Vermietungssituation", "level": 1}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 390.154, "t": 361.50095888671865, "r": 488.146, "b": 357.34895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 47]}], "orig": "Fläche ist mehr als 12 Monate vermietet (100 %)", "text": "Fläche ist mehr als 12 Monate vermietet (100 %)"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/tables/1"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 530.9519588867187, "r": 277.557, "b": 508.55095888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 369]}], "orig": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.", "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 499.23095888671867, "r": 239.14, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "text": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "level": 1}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 482.14195888671867, "r": 218.832, "b": 477.9899588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "Büro: 80,90 %", "text": "Büro: 80,90 %"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 468.30395888671865, "r": 220.911, "b": 464.15195888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "Handel: 8,28 %", "text": "Handel: 8,28 %"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 454.4659588867187, "r": 223.824, "b": 450.3139588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "Sonstige: 8,21 %", "text": "Sonstige: 8,21 %"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 440.62795888671866, "r": 213.309, "b": 436.4749588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "Kfz: 2,20 %", "text": "Kfz: 2,20 %"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 426.7899588867187, "r": 234.273, "b": 422.6369588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "Lager/Logistik: 0,40 %", "text": "Lager/Logistik: 0,40 %"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 405.99095888671866, "r": 144.55, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Auslaufende Mietverträge**", "text": "Auslaufende Mietverträge**", "level": 1}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 303.63095888671864, "r": 81.787, "b": 297.35595888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "unbefristet", "text": "unbefristet", "level": 1}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 294.5519588867187, "r": 250.624, "b": 290.50995888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 99]}], "orig": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die", "text": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die"}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 288.43295888671867, "r": 270.351, "b": 284.39095888671864, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 105]}], "orig": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung", "text": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung"}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 282.3119588867187, "r": 220.984, "b": 278.26995888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 86]}], "orig": "der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt.", "text": "der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt."}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 269.4309588867186, "r": 116.035, "b": 263.15595888671874, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "Objektbeschreibung", "text": "Objektbeschreibung", "level": 1}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 259.7109588867187, "r": 134.8, "b": 252.7049588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "■ 7 oberirdische Stockwerke", "text": "■ 7 oberirdische Stockwerke", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 250.23095888671867, "r": 136.836, "b": 242.9849588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "■ Terrasse im 6. und 7. Stock", "text": "■ Terrasse im 6. und 7. Stock", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 240.99095888671866, "r": 101.922, "b": 233.74495888671868, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "■ Zwei Innenhöfe", "text": "■ Zwei Innenhöfe", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 231.75095888671865, "r": 175.935, "b": 224.50495888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 41]}], "orig": "■ Tiefgarage (30 Autos, 3 Kraftfahrzeuge)", "text": "■ Tiefgarage (30 Autos, 3 Kraftfahrzeuge)", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 222.51095888671864, "r": 183.824, "b": 215.26495888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "■ Ein Büroeingang mit repräsentativer Lobby", "text": "■ Ein Büroeingang mit repräsentativer Lobby", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 213.27095888671863, "r": 86.326, "b": 206.02495888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "■ 5 Aufzüge", "text": "■ 5 Aufzüge", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 204.03095888671874, "r": 150.595, "b": 196.78495888671864, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "■ 1 gemeinsamer Konferenzraum", "text": "■ 1 gemeinsamer Konferenzraum", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 194.79095888671873, "r": 172.721, "b": 187.54495888671863, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "■ 2 Mieteinheiten als Bar und Restaurant", "text": "■ 2 Mieteinheiten als Bar und Restaurant", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 185.55095888671872, "r": 204.169, "b": 178.30495888671862, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 47]}], "orig": "■ Geothermische Energie für Kühlung und Heizung", "text": "■ Geothermische Energie für Kühlung und Heizung", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 177.37995888671867, "r": 116.985, "b": 172.6579588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": " zurück zum Inhaltsverzeichnis", "text": " zurück zum Inhaltsverzeichnis", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 170.59195888671866, "r": 500.133, "b": 126.22995888671869, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1518]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt. Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt. Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die"}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 49.076, "t": 796.2509588867186, "r": 141.056, "b": 788.2739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Deka-ImmobilienEuropa", "text": "Deka-ImmobilienEuropa"}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 768.3299588867187, "r": 100.171, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "Via Broletto 16 20126 Mailand", "text": "Via Broletto 16 20126 Mailand", "level": 1}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.839, "t": 708.1479588867187, "r": 129.469, "b": 701.8729588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "Nachhaltigkeitsangaben", "text": "Nachhaltigkeitsangaben", "level": 1}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 663.5109588867186, "r": 279.67, "b": 657.2359588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 64]}], "orig": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "text": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "level": 1}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 472.15195888671866, "r": 388.568, "b": 455.87095888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 369]}], "orig": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.", "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 354.25995888671866, "r": 116.986, "b": 349.5379588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": " zurück zum Inhaltsverzeichnis", "text": " zurück zum Inhaltsverzeichnis", "level": 1}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 346.03195888671866, "r": 288.357, "b": 341.9899588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 112]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt.", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt."}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 340.7519588867187, "r": 500.133, "b": 301.66995888671863, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1405]}], "orig": "Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche", "text": "Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche"}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 393.596, "t": 465.41395888671866, "r": 456.543, "b": 402.4009588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 166]}], "orig": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien", "text": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 45.75118637084961, "t": 746.0152206420898, "r": 267.3518981933594, "b": 680.6081695556641, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/4"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 470.78887939453125, "t": 802.0875473022461, "r": 524.6519775390625, "b": 750.9003448486328, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/groups/0"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 279.3341369628906, "t": 742.3217239379883, "r": 524.9978637695312, "b": 636.7871398925781, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 280.4220886230469, "t": 501.08929443359375, "r": 524.91455078125, "b": 420.98394775390625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 286.7435302734375, "t": 393.90313720703125, "r": 352.2178955078125, "b": 328.83905029296875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 95.30937957763672, "t": 487.0232849121094, "r": 235.01136779785156, "b": 417.8869323730469, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 2, "bbox": {"l": 470.8296813964844, "t": 801.9934883117676, "r": 524.885498046875, "b": 750.3796844482422, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 279.4228515625, "t": 324.60943603515625, "r": 525.1217651367188, "b": 276.49835205078125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 282.473, "t": 520.0494000000001, "r": 326.253, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.477, "t": 520.0494000000001, "r": 429.27, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.275, "t": 520.0494000000001, "r": 515.513, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 529.2894000000001, "r": 314.45, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 529.2894000000001, "r": 429.206, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.635", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.557, "t": 529.2894000000001, "r": 515.51, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 538.5294000000001, "r": 331.512, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 538.5294000000001, "r": 429.206, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.635", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.557, "t": 538.5294000000001, "r": 515.51, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 425.276, "t": 557.2494000000002, "r": 429.213, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 5, "num_cols": 3, "grid": [[{"bbox": {"l": 282.473, "t": 520.0494000000001, "r": 326.253, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.477, "t": 520.0494000000001, "r": 429.27, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.275, "t": 520.0494000000001, "r": 515.513, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 529.2894000000001, "r": 314.45, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 529.2894000000001, "r": 429.206, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.635", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.557, "t": 529.2894000000001, "r": 515.51, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 538.5294000000001, "r": 331.512, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 538.5294000000001, "r": 429.206, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.635", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.557, "t": 538.5294000000001, "r": 515.51, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 425.276, "t": 557.2494000000002, "r": 429.213, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/35"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.286109924316406, "t": 590.5147399902344, "r": 524.8837280273438, "b": 531.4830017089844, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [{"$ref": "#/texts/35"}], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 306.083, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.005,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 359.214, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1871,1965 (2019) / 2021", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 336.07, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "113.380.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 314.086, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4,33 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 338.31, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Beteiligungsgesell.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.077.596 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 6, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 306.083, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.005,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 359.214, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1871,1965 (2019) / 2021", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 338.31, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Beteiligungsgesell.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 336.07, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "113.380.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 314.086, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4,33 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.077.596 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.78061294555664, "t": 399.5735168457031, "r": 275.5047302246094, "b": 300.5343017578125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 131.516, "t": 445.8894000000001, "r": 140.31, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 473.84940000000006, "r": 140.246, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.191", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 473.84940000000006, "r": 181.192, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "16%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.673, "t": 473.84940000000006, "r": 273.317, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "559.559", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 483.08940000000007, "r": 140.248, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "168", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 483.08940000000007, "r": 181.194, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 251.633, "t": 483.08940000000007, "r": 273.32, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "80.000", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 492.3294000000001, "r": 140.246, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.087", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 492.3294000000001, "r": 181.192, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "27%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 492.3294000000001, "r": 273.319, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.156.963", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 510.8094000000001, "r": 140.246, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.998", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 510.8094000000001, "r": 181.192, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "52%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 510.8094000000001, "r": 273.319, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2.149.925", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 520.0494000000001, "r": 140.248, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "191", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 520.0494000000001, "r": 181.194, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 520.0494000000001, "r": 273.32, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "131.149", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": false, "row_section": true, "fillable": false}], "num_rows": 10, "num_cols": 4, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 131.516, "t": 445.8894000000001, "r": 140.31, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 473.84940000000006, "r": 140.246, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.191", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 473.84940000000006, "r": 181.192, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "16%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.673, "t": 473.84940000000006, "r": 273.317, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "559.559", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 483.08940000000007, "r": 140.248, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "168", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 483.08940000000007, "r": 181.194, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 251.633, "t": 483.08940000000007, "r": 273.32, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "80.000", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 492.3294000000001, "r": 140.246, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.087", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 492.3294000000001, "r": 181.192, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "27%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 492.3294000000001, "r": 273.319, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.156.963", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 510.8094000000001, "r": 140.246, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.998", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 510.8094000000001, "r": 181.192, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "52%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 510.8094000000001, "r": 273.319, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2.149.925", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 520.0494000000001, "r": 140.248, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "191", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 520.0494000000001, "r": 181.194, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 520.0494000000001, "r": 273.32, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "131.149", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": false, "row_section": true, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.84785461425781, "t": 702.0831146240234, "r": 525.4113159179688, "b": 670.4891204833984, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 150.592, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ LEED for Core & Shell: platinum", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 232.491, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): keine <PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 195.327, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: keine <PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 3, "num_cols": 1, "grid": [[{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 150.592, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ LEED for Core & Shell: platinum", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 232.491, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): keine <PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 195.327, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: keine <PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.448768615722656, "t": 657.6707611083984, "r": 525.244384765625, "b": 472.9071350097656, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "70 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.277, "t": 226.64940000000013, "r": 391.008, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.077.596 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 187.88940000000002, "r": 390.686, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.635", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 373.316, "t": 197.96940000000006, "r": 391.118, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "30 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 207.44940000000008, "r": 391.12, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 275.24940000000004, "r": 391.036, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "163.104 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 284.96940000000006, "r": 391.036, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "582.137 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.495.459 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 246.08940000000007, "r": 391.036, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "265.044 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 255.8094000000001, "r": 391.039, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "81.552 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 265.5304000000001, "r": 117.433, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 265.5304000000001, "r": 391.039, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "72.437 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "32,26", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "27,65", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.876, "t": 350.0094000000001, "r": 390.707, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "51,57%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 303.8094000000001, "r": 390.672, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "58.150.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 313.0494000000001, "r": 390.672, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "39.520.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 322.6494000000001, "r": 95.253, "b": 328.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 322.2894000000001, "r": 390.67, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "113.380.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 359.4894000000001, "r": 390.675, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "14.768,83 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 19, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 187.88940000000002, "r": 390.686, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "7.635", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 373.316, "t": 197.96940000000006, "r": 391.118, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "30 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 207.44940000000008, "r": 391.12, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "70 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.277, "t": 226.64940000000013, "r": 391.008, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.077.596 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 246.08940000000007, "r": 391.036, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "265.044 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 255.8094000000001, "r": 391.039, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "81.552 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 265.5304000000001, "r": 117.433, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 265.5304000000001, "r": 391.039, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "72.437 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 275.24940000000004, "r": 391.036, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "163.104 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 284.96940000000006, "r": 391.036, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "582.137 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.495.459 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 303.8094000000001, "r": 390.672, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "58.150.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 313.0494000000001, "r": 390.672, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "39.520.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 322.6494000000001, "r": 95.253, "b": 328.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 322.2894000000001, "r": 390.67, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "113.380.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "32,26", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "27,65", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.876, "t": 350.0094000000001, "r": 390.707, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "51,57%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 359.4894000000001, "r": 390.675, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "14.768,83 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 1}, "2": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 2}}}