{"schema_name": "DoclingDocument", "version": "1.7.0", "name": "38_Ob<PERSON><PERSON>_38", "origin": {"mimetype": "application/pdf", "binary_hash": 17310508210614267464, "filename": "38_Objekt_38.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/pictures/0"}, {"$ref": "#/texts/4"}, {"$ref": "#/pictures/1"}, {"$ref": "#/pictures/2"}, {"$ref": "#/texts/17"}, {"$ref": "#/tables/0"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/45"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}, {"$ref": "#/pictures/4"}, {"$ref": "#/tables/2"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/pictures/5"}, {"$ref": "#/tables/3"}, {"$ref": "#/texts/70"}, {"$ref": "#/tables/4"}, {"$ref": "#/texts/71"}, {"$ref": "#/texts/72"}, {"$ref": "#/texts/73"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 49.076, "t": 796.2509588867186, "r": 141.056, "b": 788.2739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Deka-ImmobilienEuropa", "text": "Deka-ImmobilienEuropa", "level": 1}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 49.076, "t": 782.3309588867187, "r": 120.689, "b": 774.3539588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 19]}], "orig": "'<PERSON>'", "text": "'<PERSON>'", "level": 1}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 768.3299588867187, "r": 210.446, "b": 761.5229588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 45]}], "orig": "Friedrichstraße 147+148 / Georgenstraße 24+25", "text": "Friedrichstraße 147+148 / Georgenstraße 24+25"}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 755.2509588867186, "r": 90.904, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "10117 Berlin", "text": "10117 Berlin", "level": 1}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 595.8309588867187, "r": 79.193, "b": 589.5559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "<PERSON><PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON><PERSON>", "level": 1}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 476.0, "t": 771.0136922200521, "r": 510.6666666666667, "b": 759.0136922200521, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 733.1189588867187, "r": 286.753, "b": 726.4649588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 723.3989588867187, "r": 286.753, "b": 716.7449588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 713.6789588867186, "r": 286.753, "b": 707.0249588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 703.9589588867186, "r": 286.753, "b": 697.3049588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 684.5189588867187, "r": 286.753, "b": 677.8649588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "■", "text": "■"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 742.9509588867187, "r": 298.292, "b": 736.6759588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "Lage", "text": "Lage", "level": 1}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 732.6309588867186, "r": 382.271, "b": 726.3559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "Exponierte Lage \"Berlin-Mitte\"", "text": "Exponierte Lage \"Berlin-Mitte\""}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 722.9109588867186, "r": 468.385, "b": 716.6359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 57]}], "orig": "\"Museumsinsel\" und Regierungsviertel fußläufig erreichbar", "text": "\"Museumsinsel\" und Regierungsviertel fußläufig erreichbar"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 713.1909588867187, "r": 375.29, "b": 706.9159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 28]}], "orig": "\"Spree\" fußlä<PERSON>ig erreichbar", "text": "\"Spree\" fußlä<PERSON>ig erreichbar"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 703.4709588867187, "r": 492.509, "b": 697.1959588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 64]}], "orig": "Direkte Anbindung zum S-, U- und Regionalbahnhof 'Friedrichstr.'", "text": "Direkte Anbindung zum S-, U- und Regionalbahnhof 'Friedrichstr.'"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 288.476, "t": 684.0309588867186, "r": 402.058, "b": 677.7559588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Flughafen BER in ca.  35 Fahrminuten", "text": "Flughafen BER in ca.  35 Fahrminuten"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 455.036, "t": 596.3109588867187, "r": 521.469, "b": 590.0359588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "Stichtag: 31.12.2022", "text": "Stichtag: 31.12.2022", "level": 1}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/tables/0"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 530.9519588867187, "r": 277.557, "b": 508.55095888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 369]}], "orig": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.", "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 499.23095888671867, "r": 239.14, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "text": "Nutzungsarten der Immobilie nach marktüblichen Mieten", "level": 1}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 499.23095888671867, "r": 405.309, "b": 492.9559588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Verkehrswerthistorie in EUR in TSD.*", "text": "Verkehrswerthistorie in EUR in TSD.*"}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 480.4129588867187, "r": 218.832, "b": 476.25995888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "Büro: 68,26 %", "text": "Büro: 68,26 %"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 463.1149588867187, "r": 223.52, "b": 458.96195888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "Handel: 26,14 %", "text": "Handel: 26,14 %"}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 445.8169588867187, "r": 213.309, "b": 441.6649588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "Kfz: 4,23 %", "text": "Kfz: 4,23 %"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 189.04, "t": 428.5189588867187, "r": 234.273, "b": 424.3669588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "Lager/Logistik: 1,37 %", "text": "Lager/Logistik: 1,37 %"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 305.121, "t": 460.46895888671867, "r": 323.643, "b": 455.9079588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "130.365", "text": "130.365"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 328.256, "t": 460.9879588867187, "r": 346.778, "b": 456.4269588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "144.255", "text": "144.255"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 361.928, "t": 466.1339588867187, "r": 380.45, "b": 461.57295888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "155.560", "text": "155.560"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 389.015, "t": 467.3839588867187, "r": 407.537, "b": 462.82295888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "163.445", "text": "163.445"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 420.712, "t": 473.3379588867187, "r": 439.234, "b": 468.77695888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "187.605", "text": "187.605"}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 450.433, "t": 478.8739588867187, "r": 468.955, "b": 474.3139588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "214.550", "text": "214.550"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 478.224, "t": 482.5279588867187, "r": 496.746, "b": 477.96695888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "228.490", "text": "228.490"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 298.581, "t": 433.9949588867187, "r": 301.442, "b": 429.4339588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 285.763, "t": 445.2379588867187, "r": 301.445, "b": 440.6769588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "50.000", "text": "50.000"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.918, "t": 456.48095888671867, "r": 301.44, "b": 451.9199588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "100.000", "text": "100.000"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.918, "t": 467.72395888671866, "r": 301.44, "b": 463.1629588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "150.000", "text": "150.000"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.918, "t": 478.96695888671866, "r": 301.44, "b": 474.4059588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "200.000", "text": "200.000"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.913, "t": 490.2199588867187, "r": 301.435, "b": 485.65895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "250.000", "text": "250.000"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 315.459, "t": 428.0119588867187, "r": 326.841, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2016", "text": "2016"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 346.214, "t": 428.0119588867187, "r": 357.596, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2017", "text": "2017"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 376.97, "t": 428.0119588867187, "r": 388.352, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2018", "text": "2018"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 407.725, "t": 428.0119588867187, "r": 419.107, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2019", "text": "2019"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 438.48, "t": 428.0119588867187, "r": 449.862, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2020", "text": "2020"}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 469.235, "t": 428.0119588867187, "r": 480.617, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2021", "text": "2021"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 498.565, "t": 428.0119588867187, "r": 512.788, "b": 423.4509588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "2022*", "text": "2022*"}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 405.99095888671866, "r": 144.55, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Auslaufende Mietverträge**", "text": "Auslaufende Mietverträge**", "level": 1}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 132.356, "t": 303.1509588867186, "r": 140.25, "b": 296.87595888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "90", "text": "90"}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 294.5519588867187, "r": 250.624, "b": 290.50995888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 99]}], "orig": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die", "text": "**Zum Schutz der Mieter erfolgt keine <PERSON>, sofern weniger als zwei Mieter/ Objekt, oder wenn die"}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 288.43295888671867, "r": 270.351, "b": 284.39095888671864, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 105]}, {"page_no": 1, "bbox": {"l": 48.476, "t": 282.3119588867187, "r": 220.984, "b": 278.26995888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [106, 192]}], "orig": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt.", "text": "Mieteinnahmen aus der Immobilie zu 75 % oder mehr von einem einzigen Mieter stammen, oder die Darstellung der auslaufenden Mietverträge Rückschlüsse auf einzelne Mieter/ Mietzahlungen zulässt."}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 269.4309588867186, "r": 116.035, "b": 263.15595888671874, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "Objektbeschreibung", "text": "Objektbeschreibung", "level": 1}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 259.7109588867187, "r": 245.036, "b": 252.7049588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 61]}], "orig": "■ 4 Büro- und Einzelhandelsgebäude in 8-geschossiger Bauweise", "text": "■ 4 Büro- und Einzelhandelsgebäude in 8-geschossiger Bauweise", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 250.23095888671867, "r": 147.21, "b": 242.9849588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "■ Teilweise Terrassen vorhanden", "text": "■ Teilweise Terrassen vorhanden", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 240.99095888671866, "r": 137.251, "b": 233.74495888671868, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "■ PKW-Stellplätze vorhanden", "text": "■ PKW-Stellplätze vorhanden", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 231.75095888671865, "r": 212.899, "b": 224.50495888671867, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "■ 4 unterschiedlich gestaltete repräsentative Eingänge", "text": "■ 4 unterschiedlich gestaltete repräsentative Eingänge", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 222.51095888671864, "r": 170.099, "b": 215.26495888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "■ 2 Personenaufzüge pro Objektzugang", "text": "■ 2 Personenaufzüge pro Objektzugang", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 213.27095888671863, "r": 219.96, "b": 206.02495888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "■ Zentrale Lüftungsanlage mit unterstützender Kühlung.", "text": "■ Zentrale Lüftungsanlage mit unterstützender Kühlung.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 204.03095888671874, "r": 147.472, "b": 196.78495888671864, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "■ Außenliegender Sonnenschutz", "text": "■ Außenliegender Sonnenschutz", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 194.79095888671873, "r": 257.913, "b": 187.54495888671863, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 61]}], "orig": "■ Zusammenlegung von Flächen in benachbarten Gebäuden möglich", "text": "■ Zusammenlegung von Flächen in benachbarten Gebäuden möglich", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 185.55095888671872, "r": 185.107, "b": 178.30495888671862, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "■ Kleinteilige Vermietung ab 130 m² möglich", "text": "■ Kleinteilige Vermietung ab 130 m² möglich", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.836, "t": 176.3109588867187, "r": 228.443, "b": 169.0649588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 58]}], "orig": "■ EG und teilweise UG mit attraktiven Einzelhandelsflächen", "text": "■ EG und teilweise UG mit attraktiven Einzelhandelsflächen", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 168.13995888671866, "r": 116.985, "b": 163.41795888671868, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": " zurück zum Inhaltsverzeichnis", "text": " zurück zum Inhaltsverzeichnis", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 48.476, "t": 161.35195888671865, "r": 500.133, "b": 116.98995888671868, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1518]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt. Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt. Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die"}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 282.476, "t": 405.99095888671866, "r": 394.671, "b": 399.7159588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "Fläche nach Vermietungssituation", "text": "Fläche nach Vermietungssituation", "level": 1}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 382.534, "t": 379.17395888671865, "r": 477.923, "b": 375.02195888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 46]}], "orig": "Fläche ist mehr als 12 Monate vermietet (72 %)", "text": "Fläche ist mehr als 12 Monate vermietet (72 %)"}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 382.534, "t": 361.50095888671865, "r": 437.133, "b": 357.34895888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "Fläche im Leerstand (17 %)", "text": "Fläche im Leerstand (17 %)"}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 382.534, "t": 343.82795888671865, "r": 493.896, "b": 339.67595888671866, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "Fläche ist maximal weitere 12 Monate vermietet (11 %)", "text": "Fläche ist maximal weitere 12 Monate vermietet (11 %)"}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 49.076, "t": 796.2509588867186, "r": 141.056, "b": 788.2739588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Deka-ImmobilienEuropa", "text": "Deka-ImmobilienEuropa", "level": 1}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 49.076, "t": 782.3309588867187, "r": 120.689, "b": 774.3539588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 19]}], "orig": "'<PERSON>'", "text": "'<PERSON>'", "level": 1}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 768.3299588867187, "r": 210.446, "b": 748.4439588867186, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 58]}], "orig": "Friedrichstraße 147+148 / Georgenstraße 24+25 10117 Berlin", "text": "Friedrichstraße 147+148 / Georgenstraße 24+25 10117 Berlin"}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.839, "t": 708.1479588867187, "r": 129.469, "b": 701.8729588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "Nachhaltigkeitsangaben", "text": "Nachhaltigkeitsangaben", "level": 1}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.836, "t": 663.5109588867186, "r": 279.67, "b": 657.2359588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 64]}], "orig": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "text": "Wertrelevante Ausgangsdaten laut aktuellen Verkehrswertgutachten", "level": 1}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 472.15195888671866, "r": 388.568, "b": 455.87095888671865, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 369]}], "orig": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten.", "text": "* <PERSON><PERSON><PERSON>, dass gemäß Kapitalanlagegesetzbuch (KAGB) für eine Immobilie zwei Verkehrswertgutachten zu erstellen sind, entspricht der ausgewiesene Verkehrswert dem arithmetischen Mittel der Verkehrswerte aus beiden Verkehrswertgutachten der Immobilie. Ausführliche Erläuterungen zur Bewertungsthematik entnehmen Sie bitte den jeweiligen Jahres-/Halbjahresberichten."}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 354.25995888671866, "r": 116.986, "b": 349.5379588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": " zurück zum Inhaltsverzeichnis", "text": " zurück zum Inhaltsverzeichnis", "level": 1}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 346.03195888671866, "r": 288.357, "b": 341.9899588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 112]}], "orig": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt.", "text": "Die in diesem Dokument enthaltenen Daten, Informationen und Aussagen wurden nach bestem Wissen zusammengestellt."}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 48.476, "t": 340.7519588867187, "r": 500.133, "b": 301.66995888671863, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1405]}], "orig": "Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche", "text": "Zustimmung nicht erlaubt. Die kommerzielle Verwendung, Weitergabe oder sonstige Nutzung  der Daten, insbesondere im Hinblick auf die Vermietung, die Veräußerung oder sonst eine Geschäftsangelegenheit in Bezug auf die Immobilien ist ausdrücklich untersagt. Diese Darstellungen wurden von der Deka Immobilien Investment GmbH nur zum Zwecke der Information des jeweiligen Nutzers erstellt. Die Informationen stellen weder ein Angebot, eine Einladung zur Zeichnung oder zum Erwerb von Finanzinstrumenten noch eine Empfehlung zum Erwerb dar. Die Informationen oder Dokumente sind nicht als Grundlage für irgendeine vertragliche oder anderweitige Verpflichtung gedacht, noch ersetzen sie eine (Rechts- und / oder Steuer-)Beratung. (Stand: Dezember 2022) Für die Publikumsfonds im Geschäftsfeld Immobilien erfolgt einmal jährlich eine veröffentlichte Aktualisierung der Daten. Das vierteljährliche Update dient ausschließlich zur internen Verwendung. Für die Spezialfonds im Geschäftsfeld Immobilien erfolgt eine vierteljährliche Aktualisierung der Daten auch für die externe Verwendung. Es wird keine Gewähr für die Richtigkeit, Vollständigkeit und Angemessenheit der Daten, Aussagen und Einschätzungen übernommen, auch wenn die Deka Immobilien Investment GmbH nur solche Daten verwendet, die sie als zuverlässig erachtet. Jegliche weitere Verwendung/Publikation dieser Daten ist ohne ausdrückliche schriftliche"}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 393.596, "t": 465.41395888671866, "r": 456.543, "b": 402.4009588867187, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 166]}], "orig": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien", "text": "Deka Immobilien Investment GmbH Lyoner Straße 13 60528 Frankfurt Postfach 11 05 23 60040 Frankfurt Telefon:  (0 69) 71 47-0 <EMAIL> www.deka.de/immobilien"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 45.81003189086914, "t": 745.5943832397461, "r": 206.0788116455078, "b": 605.6540679931641, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/5"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 470.6877136230469, "t": 802.1867294311523, "r": 524.7041015625, "b": 750.8697204589844, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 279.2940368652344, "t": 742.0382995605469, "r": 524.9090576171875, "b": 636.5859832763672, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 44.843421936035156, "t": 502.96966552734375, "r": 525.2124633789062, "b": 415.6844787597656, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 284.6658935546875, "t": 394.9205627441406, "r": 495.3824157714844, "b": 328.3477783203125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 2, "bbox": {"l": 470.7862243652344, "t": 802.0457420349121, "r": 524.88427734375, "b": 750.3241424560547, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/18"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.25865936279297, "t": 590.4962768554688, "r": 524.9788208007812, "b": 531.2734375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [{"$ref": "#/texts/18"}], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 306.083, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.605,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 328.032, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2002 (-) / 2011", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 336.07, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "228.490.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 310.127, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4,1 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 333.915, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Direktinvestment", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.423.048 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 6, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 255.08940000000007, "r": 122.531, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Grundstücksgröße in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 255.08940000000007, "r": 306.083, "b": 261.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.605,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 264.5694000000001, "r": 152.691, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Baujahr (Umbaujahr) / Erwerbsjahr", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 264.5694000000001, "r": 328.032, "b": 270.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2002 (-) / 2011", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 273.8094000000001, "r": 79.334, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Halteform", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 273.8094000000001, "r": 333.915, "b": 280.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Direktinvestment", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 283.0494000000001, "r": 142.053, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Gutachterlicher Verkehrswert*", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 283.0494000000001, "r": 336.07, "b": 289.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "228.490.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 292.6494000000001, "r": 151.411, "b": 298.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Ø Restlaufzeit der Mietverträge**", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 292.2894000000001, "r": 310.127, "b": 298.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4,1 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 301.5294000000001, "r": 140.492, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 301.5294000000001, "r": 328.155, "b": 307.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.423.048 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 44.59791564941406, "t": 399.4718017578125, "r": 275.4685974121094, "b": 298.5386962890625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 131.516, "t": 445.8894000000001, "r": 140.31, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 455.3694000000001, "r": 140.248, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "622", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 455.3694000000001, "r": 181.194, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "3%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 455.3694000000001, "r": 273.32, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "173.866", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 464.60940000000005, "r": 140.246, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.072", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 464.60940000000005, "r": 181.192, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "10%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 464.60940000000005, "r": 273.319, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.252.919", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 473.84940000000006, "r": 140.246, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.278", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 473.84940000000006, "r": 181.192, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "17%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 473.84940000000006, "r": 273.319, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.165.914", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 483.08940000000007, "r": 140.246, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.992", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 483.08940000000007, "r": 181.192, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "30%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 483.08940000000007, "r": 273.319, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.842.346", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 492.3294000000001, "r": 140.246, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.760", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 492.3294000000001, "r": 181.194, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "9%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 492.3294000000001, "r": 273.32, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "677.040", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 501.5694000000001, "r": 140.248, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "789", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 501.5694000000001, "r": 181.194, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "4%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 501.5694000000001, "r": 273.32, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "436.002", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 510.8094000000001, "r": 140.246, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.022", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 510.8094000000001, "r": 181.194, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 510.8094000000001, "r": 273.32, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "287.059", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 520.0494000000001, "r": 140.248, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "910", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 520.0494000000001, "r": 181.194, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 520.0494000000001, "r": 273.32, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "305.575", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 529.2894000000001, "r": 140.246, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.306", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 529.2894000000001, "r": 181.192, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "17%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 529.2894000000001, "r": 273.319, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.817.716", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 538.0494000000001, "r": 81.787, "b": 544.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "unbefristet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}], "num_rows": 11, "num_cols": 4, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 131.516, "t": 445.8894000000001, "r": 140.31, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 150.476, "t": 445.8894000000001, "r": 181.148, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in %(m²)", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 237.716, "t": 445.8894000000001, "r": 273.398, "b": 452.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "in EUR p.a.", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 454.8894000000001, "r": 64.646, "b": 461.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2022", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 455.3694000000001, "r": 140.248, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "622", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 455.3694000000001, "r": 181.194, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "3%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 455.3694000000001, "r": 273.32, "b": 461.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "173.866", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 464.1294000000001, "r": 64.646, "b": 470.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2023", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 464.60940000000005, "r": 140.246, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.072", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 464.60940000000005, "r": 181.192, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "10%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 464.60940000000005, "r": 273.319, "b": 470.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.252.919", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 473.3694000000001, "r": 64.646, "b": 479.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2024", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 473.84940000000006, "r": 140.246, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.278", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 473.84940000000006, "r": 181.192, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "17%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 473.84940000000006, "r": 273.319, "b": 480.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.165.914", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 482.60940000000005, "r": 64.646, "b": 488.8844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2025", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 483.08940000000007, "r": 140.246, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.992", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 483.08940000000007, "r": 181.192, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "30%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 483.08940000000007, "r": 273.319, "b": 489.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.842.346", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 491.84940000000006, "r": 64.646, "b": 498.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2026", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 492.3294000000001, "r": 140.246, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.760", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 492.3294000000001, "r": 181.194, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "9%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 492.3294000000001, "r": 273.32, "b": 498.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "677.040", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 501.08940000000007, "r": 64.646, "b": 507.3644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2027", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 501.5694000000001, "r": 140.248, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "789", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 501.5694000000001, "r": 181.194, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "4%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 501.5694000000001, "r": 273.32, "b": 507.84440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "436.002", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 510.3294000000001, "r": 64.646, "b": 516.6044, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2028", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 510.8094000000001, "r": 140.246, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.022", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 510.8094000000001, "r": 181.194, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 510.8094000000001, "r": 273.32, "b": 517.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "287.059", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 519.5694000000001, "r": 64.646, "b": 525.8444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2029", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 128.396, "t": 520.0494000000001, "r": 140.248, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "910", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 170.156, "t": 520.0494000000001, "r": 181.194, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 247.675, "t": 520.0494000000001, "r": 273.32, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "305.575", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 528.8094000000001, "r": 74.122, "b": 535.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ab 2030", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 122.516, "t": 529.2894000000001, "r": 140.246, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.306", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 166.196, "t": 529.2894000000001, "r": 181.192, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "17%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 241.796, "t": 529.2894000000001, "r": 273.319, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.817.716", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 538.0494000000001, "r": 81.787, "b": 544.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "unbefristet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 279.8406677246094, "t": 324.5262451171875, "r": 525.0650024414062, "b": 276.5263671875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 282.473, "t": 520.0494000000001, "r": 326.253, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.477, "t": 520.0494000000001, "r": 429.27, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.275, "t": 520.0494000000001, "r": 515.513, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.478, "t": 529.2894000000001, "r": 314.452, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.518, "t": 529.2894000000001, "r": 429.205, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.880", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.556, "t": 529.2894000000001, "r": 515.509, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 538.5294000000001, "r": 331.512, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.516, "t": 538.5294000000001, "r": 429.204, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19.838", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.519, "t": 538.5294000000001, "r": 515.514, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "83%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 547.7694000000001, "r": 429.206, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.042", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.516, "t": 547.7694000000001, "r": 515.512, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "17%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 421.316, "t": 557.2494000000002, "r": 429.21, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "48", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 5, "num_cols": 3, "grid": [[{"bbox": {"l": 282.473, "t": 520.0494000000001, "r": 326.253, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietverträge", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 420.477, "t": 520.0494000000001, "r": 429.27, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "m²", "column_header": true, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.275, "t": 520.0494000000001, "r": 515.513, "b": 526.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "in%", "column_header": true, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.478, "t": 529.2894000000001, "r": 314.452, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.518, "t": 529.2894000000001, "r": 429.205, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.880", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 496.556, "t": 529.2894000000001, "r": 515.509, "b": 535.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "100%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 538.5294000000001, "r": 331.512, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon vermietet", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 407.516, "t": 538.5294000000001, "r": 429.204, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19.838", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.519, "t": 538.5294000000001, "r": 515.514, "b": 544.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "83%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 547.7694000000001, "r": 338.359, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "davon <PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 411.476, "t": 547.7694000000001, "r": 429.206, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4.042", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 500.516, "t": 547.7694000000001, "r": 515.512, "b": 554.0444, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "17%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 282.476, "t": 557.2494000000002, "r": 325.032, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON>", "column_header": false, "row_header": true, "row_section": false, "fillable": false}, {"bbox": {"l": 421.316, "t": 557.2494000000002, "r": 429.21, "b": 563.5244, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "48", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.952457427978516, "t": 702.3019104003906, "r": 525.4631958007812, "b": 670.4833068847656, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 124.877, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ BREEAM DE: very good", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 204.296, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): 361,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 152.914, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: E", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 3, "num_cols": 1, "grid": [[{"bbox": {"l": 48.836, "t": 143.60140000000013, "r": 124.877, "b": 150.25540000000012, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ BREEAM DE: very good", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 153.32140000000004, "r": 204.296, "b": 159.97540000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Primärenergiebedarf (in kWh/m² und Jahr): 361,0", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 163.04140000000007, "r": 152.914, "b": 169.69540000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "■ Rating gemäß Energieausweis: E", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}, {"self_ref": "#/tables/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 44.43865966796875, "t": 657.5963134765625, "r": 525.2673950195312, "b": 472.8907470703125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "70 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.436, "t": 226.64940000000013, "r": 391.003, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "50,41 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.423.048 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 368.996, "t": 187.88940000000002, "r": 390.684, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.777", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 369.356, "t": 197.96940000000006, "r": 391.117, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "201 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 207.44940000000008, "r": 391.12, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 275.24940000000004, "r": 391.036, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "376.922 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 284.96940000000006, "r": 391.036, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "705.750 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "8.717.298 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.196, "t": 246.08940000000007, "r": 391.04, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "47.115 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 255.8094000000001, "r": 391.039, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "47.115 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 265.5304000000001, "r": 117.433, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.237, "t": 265.5304000000001, "r": 391.037, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "234.598 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26,12", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "24,16", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.876, "t": 350.0094000000001, "r": 390.707, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "38,68%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 303.8094000000001, "r": 390.672, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "88.080.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 313.0494000000001, "r": 390.672, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "75.570.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 322.6494000000001, "r": 95.253, "b": 328.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 322.2894000000001, "r": 390.67, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "228.490.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 348.956, "t": 359.4894000000001, "r": 390.677, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.576,48 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], "num_rows": 19, "num_cols": 2, "grid": [[{"bbox": {"l": 48.836, "t": 188.24940000000004, "r": 98.666, "b": 194.52440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Mietfläche in m²", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 368.996, "t": 187.88940000000002, "r": 390.684, "b": 194.16440000000011, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.777", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 197.96940000000006, "r": 90.896, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "TG-Stellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 369.356, "t": 197.96940000000006, "r": 391.117, "b": 204.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "201 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 207.44940000000008, "r": 99.047, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Außenstellplätze", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 377.276, "t": 207.44940000000008, "r": 391.12, "b": 213.72440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "0 St.", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 216.9294000000001, "r": 142.132, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Übliche Gesamtnutzungsdauer", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.276, "t": 216.9294000000001, "r": 391.008, "b": 223.20440000000008, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "70 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 226.64940000000013, "r": 107.095, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Rest<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.436, "t": 226.64940000000013, "r": 391.003, "b": 232.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "50,41 Jahre", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 236.36940000000004, "r": 140.492, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Marktüblicher Jahresrohertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 345.356, "t": 236.36940000000004, "r": 391.035, "b": 242.64440000000013, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.423.048 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 246.08940000000007, "r": 154.511, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Nicht umlagefähige Betriebskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.196, "t": 246.08940000000007, "r": 391.04, "b": 252.36440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "47.115 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 255.8094000000001, "r": 107.123, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verwaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 355.195, "t": 255.8094000000001, "r": 391.039, "b": 262.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "47.115 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 265.5304000000001, "r": 117.433, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Instandhaltungskosten", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.237, "t": 265.5304000000001, "r": 391.037, "b": 271.8054000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "234.598 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 275.24940000000004, "r": 103.421, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 275.24940000000004, "r": 391.036, "b": 281.5244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "376.922 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 284.96940000000006, "r": 145.494, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bewirtschaftungskosten gesamt", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 351.236, "t": 284.96940000000006, "r": 391.036, "b": 291.24440000000004, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "705.750 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 294.6894000000001, "r": 97.413, "b": 300.96440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Jahresreinertrag", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 344.996, "t": 294.3294000000001, "r": 390.675, "b": 300.60440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "8.717.298 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 304.1694000000001, "r": 204.43, "b": 310.4444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwert bzw. Bodenwertanteil des Erbbaurechts", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 303.8094000000001, "r": 390.672, "b": 310.0844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "88.080.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 313.4094000000001, "r": 134.232, "b": 319.6844000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Wert der baulichen Anlagen", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 341.036, "t": 313.0494000000001, "r": 390.672, "b": 319.3244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "75.570.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 322.6494000000001, "r": 95.253, "b": 328.9244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert *", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 337.076, "t": 322.2894000000001, "r": 390.67, "b": 328.5644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "228.490.000 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 331.8894000000001, "r": 220.916, "b": 338.16440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag exkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 331.52940000000007, "r": 390.686, "b": 337.8044000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26,12", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 341.1294000000001, "r": 219.717, "b": 347.40440000000007, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert / Jahresrohertrag inkl. nicht umlagef. BWK", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 372.956, "t": 340.7694000000001, "r": 390.686, "b": 347.04440000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "24,16", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 350.3694000000001, "r": 152.711, "b": 356.6444000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Bodenwertanteil am Verkehrswert", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 365.876, "t": 350.0094000000001, "r": 390.707, "b": 356.28440000000006, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "38,68%", "column_header": false, "row_header": false, "row_section": false, "fillable": false}], [{"bbox": {"l": 48.836, "t": 359.84940000000006, "r": 145.854, "b": 366.1244000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Verkehrswert pro m² Mietfläche", "column_header": false, "row_header": false, "row_section": false, "fillable": false}, {"bbox": {"l": 348.956, "t": 359.4894000000001, "r": 390.677, "b": 365.7644000000001, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "9.576,48 EUR", "column_header": false, "row_header": false, "row_section": false, "fillable": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 1}, "2": {"size": {"width": 595.2001953125, "height": 841.6803588867188}, "page_no": 2}}}