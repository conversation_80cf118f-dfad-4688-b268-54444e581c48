{"source_document": "Deka-ImmobilienGlobal Objektfactsheets 31122022 (1).pdf", "created_date": "2025-08-26T16:29:42.166295", "total_buildings": 45, "enhanced_sample": 5, "buildings": [{"title": "Objekt 1", "address": "Building 1", "start_page": 4, "end_page": 5, "page_count": 2, "level": 1, "output_file": "01_Objekt_1.pdf", "output_path": "output_buildings\\01_Objekt_1.pdf", "actual_pages": 2}, {"title": "Objekt 2", "address": "Building 2", "start_page": 6, "end_page": 7, "page_count": 2, "level": 1, "output_file": "02_Objekt_2.pdf", "output_path": "output_buildings\\02_Objekt_2.pdf", "actual_pages": 2}, {"title": "Objekt 3", "address": "Building 3", "start_page": 8, "end_page": 9, "page_count": 2, "level": 1, "output_file": "03_Objekt_3.pdf", "output_path": "output_buildings\\03_Objekt_3.pdf", "actual_pages": 2}, {"title": "Objekt 4", "address": "Building 4", "start_page": 10, "end_page": 11, "page_count": 2, "level": 1, "output_file": "04_Objekt_4.pdf", "output_path": "output_buildings\\04_Objekt_4.pdf", "actual_pages": 2}, {"title": "Objekt 5", "address": "Building 5", "start_page": 12, "end_page": 13, "page_count": 2, "level": 1, "output_file": "05_Objekt_5.pdf", "output_path": "output_buildings\\05_Objekt_5.pdf", "actual_pages": 2}, {"title": "Objekt 6", "address": "Building 6", "start_page": 14, "end_page": 15, "page_count": 2, "level": 1, "output_file": "06_Objekt_6.pdf", "output_path": "output_buildings\\06_Objekt_6.pdf", "actual_pages": 2}, {"title": "Objekt 7", "address": "Building 7", "start_page": 16, "end_page": 17, "page_count": 2, "level": 1, "output_file": "07_Objekt_7.pdf", "output_path": "output_buildings\\07_Objekt_7.pdf", "actual_pages": 2}, {"title": "Objekt 8", "address": "Building 8", "start_page": 18, "end_page": 19, "page_count": 2, "level": 1, "output_file": "08_Objekt_8.pdf", "output_path": "output_buildings\\08_Objekt_8.pdf", "actual_pages": 2}, {"title": "Objekt 9", "address": "Building 9", "start_page": 20, "end_page": 21, "page_count": 2, "level": 1, "output_file": "09_Objekt_9.pdf", "output_path": "output_buildings\\09_Objekt_9.pdf", "actual_pages": 2}, {"title": "Objekt 10", "address": "Building 10", "start_page": 22, "end_page": 23, "page_count": 2, "level": 1, "output_file": "10_Objekt_10.pdf", "output_path": "output_buildings\\10_Objekt_10.pdf", "actual_pages": 2}, {"title": "Objekt 11", "address": "Building 11", "start_page": 24, "end_page": 25, "page_count": 2, "level": 1, "output_file": "11_Objekt_11.pdf", "output_path": "output_buildings\\11_Objekt_11.pdf", "actual_pages": 2}, {"title": "Objekt 12", "address": "Building 12", "start_page": 26, "end_page": 27, "page_count": 2, "level": 1, "output_file": "12_Objekt_12.pdf", "output_path": "output_buildings\\12_Objekt_12.pdf", "actual_pages": 2}, {"title": "Objekt 13", "address": "Building 13", "start_page": 28, "end_page": 29, "page_count": 2, "level": 1, "output_file": "13_Objekt_13.pdf", "output_path": "output_buildings\\13_Objekt_13.pdf", "actual_pages": 2}, {"title": "Objekt 14", "address": "Building 14", "start_page": 30, "end_page": 31, "page_count": 2, "level": 1, "output_file": "14_Objekt_14.pdf", "output_path": "output_buildings\\14_Objekt_14.pdf", "actual_pages": 2}, {"title": "Objekt 15", "address": "Building 15", "start_page": 32, "end_page": 33, "page_count": 2, "level": 1, "output_file": "15_Objekt_15.pdf", "output_path": "output_buildings\\15_Objekt_15.pdf", "actual_pages": 2}, {"title": "Objekt 16", "address": "Building 16", "start_page": 34, "end_page": 35, "page_count": 2, "level": 1, "output_file": "16_Objekt_16.pdf", "output_path": "output_buildings\\16_Objekt_16.pdf", "actual_pages": 2}, {"title": "Objekt 17", "address": "Building 17", "start_page": 36, "end_page": 37, "page_count": 2, "level": 1, "output_file": "17_Objekt_17.pdf", "output_path": "output_buildings\\17_Objekt_17.pdf", "actual_pages": 2}, {"title": "Objekt 18", "address": "Building 18", "start_page": 38, "end_page": 39, "page_count": 2, "level": 1, "output_file": "18_Objekt_18.pdf", "output_path": "output_buildings\\18_Objekt_18.pdf", "actual_pages": 2}, {"title": "Objekt 19", "address": "Building 19", "start_page": 40, "end_page": 41, "page_count": 2, "level": 1, "output_file": "19_Objekt_19.pdf", "output_path": "output_buildings\\19_Objekt_19.pdf", "actual_pages": 2}, {"title": "Objekt 20", "address": "Building 20", "start_page": 42, "end_page": 43, "page_count": 2, "level": 1, "output_file": "20_Objekt_20.pdf", "output_path": "output_buildings\\20_Objekt_20.pdf", "actual_pages": 2}, {"title": "Objekt 21", "address": "Building 21", "start_page": 44, "end_page": 45, "page_count": 2, "level": 1, "output_file": "21_Objekt_21.pdf", "output_path": "output_buildings\\21_Objekt_21.pdf", "actual_pages": 2}, {"title": "Objekt 22", "address": "Building 22", "start_page": 46, "end_page": 47, "page_count": 2, "level": 1, "output_file": "22_Objekt_22.pdf", "output_path": "output_buildings\\22_Objekt_22.pdf", "actual_pages": 2}, {"title": "Objekt 23", "address": "Building 23", "start_page": 48, "end_page": 49, "page_count": 2, "level": 1, "output_file": "23_Objekt_23.pdf", "output_path": "output_buildings\\23_Objekt_23.pdf", "actual_pages": 2}, {"title": "Objekt 24", "address": "Building 24", "start_page": 50, "end_page": 51, "page_count": 2, "level": 1, "output_file": "24_Objekt_24.pdf", "output_path": "output_buildings\\24_Objekt_24.pdf", "actual_pages": 2}, {"title": "Objekt 25", "address": "Building 25", "start_page": 52, "end_page": 53, "page_count": 2, "level": 1, "output_file": "25_Objekt_25.pdf", "output_path": "output_buildings\\25_Objekt_25.pdf", "actual_pages": 2}, {"title": "Objekt 26", "address": "Building 26", "start_page": 54, "end_page": 55, "page_count": 2, "level": 1, "output_file": "26_Objekt_26.pdf", "output_path": "output_buildings\\26_Objekt_26.pdf", "actual_pages": 2}, {"title": "Objekt 27", "address": "Building 27", "start_page": 56, "end_page": 57, "page_count": 2, "level": 1, "output_file": "27_Objekt_27.pdf", "output_path": "output_buildings\\27_Objekt_27.pdf", "actual_pages": 2}, {"title": "Objekt 28", "address": "Building 28", "start_page": 58, "end_page": 59, "page_count": 2, "level": 1, "output_file": "28_Objekt_28.pdf", "output_path": "output_buildings\\28_Objekt_28.pdf", "actual_pages": 2}, {"title": "Objekt 29", "address": "Building 29", "start_page": 60, "end_page": 61, "page_count": 2, "level": 1, "output_file": "29_Objekt_29.pdf", "output_path": "output_buildings\\29_Objekt_29.pdf", "actual_pages": 2}, {"title": "Objekt 30", "address": "Building 30", "start_page": 62, "end_page": 63, "page_count": 2, "level": 1, "output_file": "30_Objekt_30.pdf", "output_path": "output_buildings\\30_Objekt_30.pdf", "actual_pages": 2}, {"title": "Objekt 31", "address": "Building 31", "start_page": 64, "end_page": 65, "page_count": 2, "level": 1, "output_file": "31_Objekt_31.pdf", "output_path": "output_buildings\\31_Objekt_31.pdf", "actual_pages": 2}, {"title": "Objekt 32", "address": "Building 32", "start_page": 66, "end_page": 67, "page_count": 2, "level": 1, "output_file": "32_Objekt_32.pdf", "output_path": "output_buildings\\32_Objekt_32.pdf", "actual_pages": 2}, {"title": "Objekt 33", "address": "Building 33", "start_page": 68, "end_page": 69, "page_count": 2, "level": 1, "output_file": "33_Objekt_33.pdf", "output_path": "output_buildings\\33_Objekt_33.pdf", "actual_pages": 2}, {"title": "Objekt 34", "address": "Building 34", "start_page": 70, "end_page": 71, "page_count": 2, "level": 1, "output_file": "34_Objekt_34.pdf", "output_path": "output_buildings\\34_Objekt_34.pdf", "actual_pages": 2}, {"title": "Objekt 35", "address": "Building 35", "start_page": 72, "end_page": 73, "page_count": 2, "level": 1, "output_file": "35_Objekt_35.pdf", "output_path": "output_buildings\\35_Objekt_35.pdf", "actual_pages": 2}, {"title": "Objekt 36", "address": "Building 36", "start_page": 74, "end_page": 75, "page_count": 2, "level": 1, "output_file": "36_Objekt_36.pdf", "output_path": "output_buildings\\36_Objekt_36.pdf", "actual_pages": 2}, {"title": "Objekt 37", "address": "Building 37", "start_page": 76, "end_page": 77, "page_count": 2, "level": 1, "output_file": "37_Objekt_37.pdf", "output_path": "output_buildings\\37_Objekt_37.pdf", "actual_pages": 2}, {"title": "Objekt 38", "address": "Building 38", "start_page": 78, "end_page": 79, "page_count": 2, "level": 1, "output_file": "38_Objekt_38.pdf", "output_path": "output_buildings\\38_Objekt_38.pdf", "actual_pages": 2}, {"title": "Objekt 39", "address": "Building 39", "start_page": 80, "end_page": 81, "page_count": 2, "level": 1, "output_file": "39_Objekt_39.pdf", "output_path": "output_buildings\\39_Objekt_39.pdf", "actual_pages": 2}, {"title": "Objekt 40", "address": "Building 40", "start_page": 82, "end_page": 83, "page_count": 2, "level": 1, "output_file": "40_Objekt_40.pdf", "output_path": "output_buildings\\40_Objekt_40.pdf", "actual_pages": 2}, {"title": "Objekt 42", "address": "Building 42", "start_page": 84, "end_page": 85, "page_count": 2, "level": 1, "output_file": "41_Objekt_42.pdf", "output_path": "output_buildings\\41_Objekt_42.pdf", "actual_pages": 2}, {"title": "Objekt 43", "address": "Building 43", "start_page": 86, "end_page": 87, "page_count": 2, "level": 1, "output_file": "42_Objekt_43.pdf", "output_path": "output_buildings\\42_Objekt_43.pdf", "actual_pages": 2}, {"title": "Objekt 44", "address": "Building 44", "start_page": 88, "end_page": 89, "page_count": 2, "level": 1, "output_file": "43_Objekt_44.pdf", "output_path": "output_buildings\\43_Objekt_44.pdf", "actual_pages": 2}, {"title": "Objekt 45", "address": "Building 45", "start_page": 90, "end_page": 91, "page_count": 2, "level": 1, "output_file": "44_Objekt_45.pdf", "output_path": "output_buildings\\44_Objekt_45.pdf", "actual_pages": 2}, {"title": "Objekt 46", "address": "Building 46", "start_page": 92, "end_page": 93, "page_count": 2, "level": 1, "output_file": "45_Objekt_46.pdf", "output_path": "output_buildings\\45_Objekt_46.pdf", "actual_pages": 2}], "enhanced_buildings_sample": [{"title": "Objekt 1", "address": "5 Gehminuten zur Straßenbahn / Buslinie", "start_page": 4, "end_page": 5, "page_count": 2, "level": 1, "output_file": "01_Objekt_1.pdf", "output_path": "output_buildings\\01_Objekt_1.pdf", "actual_pages": 2, "extracted_details": {"extracted_address": "5 Gehminuten zur Straßenbahn / Buslinie", "extracted_city": "Köln", "property_type": "Logistik", "area_sqm": "300", "text_sample": "Deka-ImmobilienGlobal\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\nEckdaten\nm²\nin % (m²)\nin EUR p.a.\n1.812\n11%\n483.198\n2.833\n17%\n768.722\n543\n3%\n144.716\n523\n3%\n153.688\n373\n2%\n112.431\n3.070\n19%\n888.714\nMietverträge\nm²\nin %\n7.244\n44%\n2.350.853\nMietfläche\n16.732\n100%\ndavon vermietet\n16.398\n98%\ndavon leerstehend\n334\n2%\n<PERSON><PERSON>hl Mieter\n21\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n'Kranhaus Süd'\nIm Zollhafen 24 \n50678 Köln\nLage\nRenommierte Lage im Rheinauhafen\nUnmittelbare Nähe zum Dom und zur Altstadt \nEinkaufsmöglichkeiten in unmittelbarer Umgebung..."}, "city": "Köln", "country": "Deutschland"}, {"title": "Objekt 2", "address": "'Quartier Leipziger Platz 1'", "start_page": 6, "end_page": 7, "page_count": 2, "level": 1, "output_file": "02_Objekt_2.pdf", "output_path": "output_buildings\\02_Objekt_2.pdf", "actual_pages": 2, "extracted_details": {"extracted_address": "'Quartier Leipziger Platz 1'", "extracted_city": "Berlin", "property_type": "Logistik", "text_sample": "Deka-ImmobilienGlobal\n■\n■\n■\n■\n■\n■\n■\nEckdaten\nm²\nin % (m²)\nin EUR p.a.\n1.816\n8%\n514.471\n4.314\n20%\n1.268.524\n2.742\n13%\n848.675\n2.724\n13%\n883.066\n2.942\n14%\n1.033.838\n935\n4%\n315.748\n619\n3%\n360.916\nMietverträge\nm²\nin %\n5.465\n25%\n2.121.962\nMietfläche\n22.435\n100%\n126\n1%\ndavon vermietet\n21.683\n97%\ndavon leerstehend\n752\n3%\nAnzahl Mieter\n37\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n'Quartier Leipziger Platz 1'\nLeipziger Platz 1-3 / Potsdamer Platz 8-9 \n10117 Berlin\nLage\nBenachbart zum Potsdamer Platz\nAdresse Leipziger Pl..."}, "city": "Berlin", "country": "Deutschland"}, {"title": "Objekt 3", "address": "Speditionstraße 21+23", "start_page": 8, "end_page": 9, "page_count": 2, "level": 1, "output_file": "03_Objekt_3.pdf", "output_path": "output_buildings\\03_Objekt_3.pdf", "actual_pages": 2, "extracted_details": {"extracted_address": "Speditionstraße 21+23", "extracted_city": "Frankfurt", "property_type": "Bürogebäude", "area_sqm": "500", "text_sample": "Deka-ImmobilienGlobal\n■\n■\n■\n■\n■\n■\n■\n■\n■\nEckdaten\nm²\nin % (m²)\nin EUR p.a.\n33\n4.536\n2.548\n12%\n898.252\n6.249\n29%\n1.684.754\n1.547\n7%\n514.076\n3.288\n15%\n1.000.748\n1.042\n5%\n352.054\n1.032\n5%\n370.341\n3.176\n15%\n1.159.974\nMietverträge\nm²\nin %\n2.833\n13%\n803.900\nMietfläche\n21.801\n100%\ndavon vermietet\n21.748\n100%\ndavon le<PERSON>\n54\n0%\nAnzahl Mieter\n15\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n'Hafenspitze'\nSpeditionstraße 21+23 \n40221 Düsseldorf\nLage\nBevorzugte Lage im Düsseldorfer Medienhafen\nGute Erreichbarkeit zur Inne..."}, "city": "Frankfurt", "country": "Deutschland"}, {"title": "Objekt 4", "address": "Friedrichstraße 5,7,9 / Arnulf-Klett-Platz 7 Lautenschlagerstraße 2,4 / Kronenstraße 20,22,22A,24,26", "start_page": 10, "end_page": 11, "page_count": 2, "level": 1, "output_file": "04_Objekt_4.pdf", "output_path": "output_buildings\\04_Objekt_4.pdf", "actual_pages": 2, "extracted_details": {"extracted_address": "Friedrichstraße 5,7,9 / Arnulf-Klett-Platz 7 Lautenschlagerstraße 2,4 / Kronenstraße 20,22,22A,24,26", "extracted_city": "Frankfurt", "property_type": "Logistik", "text_sample": "Deka-ImmobilienGlobal\n■\n■\n■\n■\n■\n■\n■\nEckdaten\nm²\nin % (m²)\nin EUR p.a.\n666\n1%\n110.989\n2.123\n4%\n729.268\n12.328\n26%\n2.568.741\n14.139\n29%\n2.829.302\n3.022\n6%\n520.080\n663\n1%\n144.076\n604\n1%\n132.288\nMietverträge\nm²\nin %\n14.778\n31%\n1.330.073\nMietfläche\n50.269\n100%\ndavon vermietet\n48.323\n96%\ndavon le<PERSON>\n1.946\n4%\nAnzahl Mieter\n49\n■\n■\n■\n■\n■\n■\n■\n■\n■\n'Zeppelin Carré'\nFriedrichstraße 5,7,9 / Arnulf-Klett-Platz 7 Lautenschlagerstraße 2,4 / Kronenstraße 20,22,22A,24,26\n70173 Stuttgart\nLage\nExponierte Innen..."}, "city": "Frankfurt", "country": "Deutschland"}, {"title": "Objekt 5", "address": "Parkplatz ist mit großformatigen Pflanzbecken untergliedert", "start_page": 12, "end_page": 13, "page_count": 2, "level": 1, "output_file": "05_Objekt_5.pdf", "output_path": "output_buildings\\05_Objekt_5.pdf", "actual_pages": 2, "extracted_details": {"extracted_address": "Parkplatz ist mit großformatigen Pflanzbecken untergliedert", "extracted_city": "Frankfurt", "text_sample": "Deka-ImmobilienGlobal\n■\n■\n■\n■\n■\nEckdaten\nm²\nin % (m²)\nin EUR p.a.\n8.949\n35%\n1.291.749\n9.335\n37%\n1.220.020\n4.983\n20%\n624.836\nMietverträge\nm²\nin %\n2.039\n8%\n306.786\nMietfläche\n25.307\n100%\n633\ndavon vermietet\n25.307\n100%\ndavon le<PERSON>tehend\nAnzahl Mieter\n10\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n■\n'Plein Westermaat'\nHet Plein 60,70 / 80,90,110,130,150,180,200,220 \n7559 SR Hengelo\nLage\nZentrale Lage \"Buren\" zwischen Almelo und Enschede\nRenommierte Lage im Einkaufsgebiet \"Westermaat\"\nStichtag: 31.12.2022\nGrundstücksgröß..."}, "city": "Frankfurt", "country": "Deutschland"}]}