{"cells": [{"cell_type": "code", "execution_count": 12, "id": "aac409e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Environment and imports configured successfully\n"]}], "source": ["# Environment Setup and Imports\n", "import os\n", "import dotenv\n", "from pathlib import Path\n", "from typing import Union, Dict, Any, List\n", "from dataclasses import dataclass\n", "\n", "# Pydantic AI imports\n", "from pydantic import BaseModel, Field\n", "from pydantic_ai import Agent, RunContext, ModelRetry\n", "from pydantic_ai.models.openai import OpenAIModel\n", "from pydantic_ai.providers.openrouter import OpenRouterProvider\n", "\n", "# RDF and SPARQL validation imports\n", "from rdflib.plugins.sparql import prepareQuery\n", "from rdflib.exceptions import ParserError\n", "\n", "# Load environment variables\n", "dotenv.load_dotenv()\n", "OR_API_KEY = os.getenv(\"OR_API_KEY\")\n", "\n", "# Initialize the model\n", "model = OpenAIModel(\n", "    'openai/gpt-4o-mini',\n", "    provider=OpenRouterProvider(api_key=OR_API_KEY),\n", ")\n", "\n", "print(\"✅ Environment and imports configured successfully\")"]}, {"cell_type": "code", "execution_count": 13, "id": "5ec435a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Core data models and validation functions defined\n"]}], "source": ["# Core Data Models and Validation\n", "\n", "class SPARQLValidationError(Exception):\n", "    \"\"\"Custom exception for SPARQL validation errors.\"\"\"\n", "    \n", "    def __init__(self, message: str, query: str = None, line: int = None, column: int = None, \n", "                 parser_message: str = None, exception_type: str = None):\n", "        self.query = query\n", "        self.line = line\n", "        self.column = column\n", "        self.parser_message = parser_message\n", "        self.exception_type = exception_type\n", "        \n", "        error_parts = [message]\n", "        \n", "        if exception_type:\n", "            error_parts.append(f\"Exception type: {exception_type}\")\n", "        \n", "        if query:\n", "            query_preview = query.strip()\n", "            if len(query_preview) > 200:\n", "                query_preview = query_preview[:200] + \"...\"\n", "            error_parts.append(f\"Query: {repr(query_preview)}\")\n", "        \n", "        if line is not None:\n", "            error_parts.append(f\"Line: {line}\")\n", "        \n", "        if column is not None:\n", "            error_parts.append(f\"Column: {column}\")\n", "        \n", "        if parser_message:\n", "            error_parts.append(f\"Parser message: {parser_message}\")\n", "        \n", "        detailed_message = \" | \".join(error_parts)\n", "        super().__init__(detailed_message)\n", "\n", "\n", "class QueryInputError(Exception):\n", "    \"\"\"Exception raised for invalid query input (empty, None, wrong type).\"\"\"\n", "    pass\n", "\n", "\n", "def validate_sparql_query(query: str) -> str:\n", "    \"\"\"\n", "    Validate the syntax of a SPARQL query without executing it.\n", "    \n", "    Args:\n", "        query (str): The SPARQL query string to validate\n", "    \n", "    Returns:\n", "        str: The type of SPARQL query (SELECT, ASK, CONSTRUCT, DESCRIBE, UNKNOWN)\n", "    \n", "    Raises:\n", "        QueryInputError: If query is empty, None, or not a string\n", "        SPARQLValidationError: If query syntax is invalid\n", "    \"\"\"\n", "    if not query or not isinstance(query, str):\n", "        if query is None:\n", "            raise QueryInputError(\"Query cannot be None\")\n", "        elif not isinstance(query, str):\n", "            raise QueryInputError(f\"Query must be a string, got {type(query).__name__}: {repr(query)}\")\n", "        else:\n", "            raise QueryInputError(\"Query cannot be empty string\")\n", "    \n", "    try:\n", "        # Attempt to parse the SPARQL query using RDFLib\n", "        prepared_query = prepareQuery(query)\n", "        \n", "        # Extract the query type from the query string\n", "        query_type = _extract_query_type(query.strip())\n", "        \n", "        return query_type\n", "        \n", "    except Exception as e:\n", "        # Extract detailed information from the exception\n", "        line = getattr(e, 'lineno', None)\n", "        column = getattr(e, 'col', None)\n", "        parser_message = getattr(e, 'msg', None)\n", "        exception_type = type(e).__name__\n", "        \n", "        raise SPARQLValidationError(\n", "            message=f\"SPARQL syntax validation failed: {str(e)}\",\n", "            query=query,\n", "            line=line,\n", "            column=column,\n", "            parser_message=parser_message,\n", "            exception_type=exception_type\n", "        )\n", "\n", "\n", "def _extract_query_type(query: str) -> str:\n", "    \"\"\"Extract the SPARQL query type from the query string.\"\"\"\n", "    if not query:\n", "        return 'UNKNOWN'\n", "        \n", "    query_upper = query.upper().strip()\n", "    lines = query_upper.split('\\n')\n", "    \n", "    for line in lines:\n", "        line = line.strip()\n", "        if not line or line.startswith('PREFIX'):\n", "            continue\n", "            \n", "        if line.startswith('SELECT'):\n", "            return 'SELECT'\n", "        elif line.startswith('ASK'):\n", "            return 'ASK'\n", "        elif line.startswith('CONSTRUCT'):\n", "            return 'CONSTRUCT'\n", "        elif line.startswith('DESCRIBE'):\n", "            return 'DESCRIBE'\n", "    \n", "    # Fallback: check the entire query string for keywords\n", "    if 'SELECT' in query_upper:\n", "        return 'SELECT'\n", "    elif 'ASK' in query_upper:\n", "        return 'ASK'\n", "    elif 'CONSTRUCT' in query_upper:\n", "        return 'CONSTRUCT'\n", "    elif 'DESCRIBE' in query_upper:\n", "        return 'DESCRIBE'\n", "    else:\n", "        return 'UNKNOWN'\n", "\n", "\n", "# Pydantic models for structured output\n", "class SPARQLQuery(BaseModel):\n", "    \"\"\"A valid SPARQL query with its type and validation status.\"\"\"\n", "    query: str = Field(description=\"The SPARQL query string\")\n", "    query_type: str = Field(description=\"Type of SPARQL query (SELECT, ASK, CONSTRUCT, DESCRIBE)\")\n", "    description: str = Field(description=\"Brief description of what the query does\")\n", "\n", "class SPARQLError(BaseModel):\n", "    \"\"\"An error response when a valid SPARQL query cannot be generated.\"\"\"\n", "    error_message: str = Field(description=\"Description of why a SPARQL query cannot be generated\")\n", "    suggestions: str = Field(description=\"Suggestions for improving the request\")\n", "\n", "# Union type for SPARQL agent output\n", "SPARQLOutput = Union[SPARQLQuery, SPARQLError]\n", "\n", "print(\"✅ Core data models and validation functions defined\")"]}, {"cell_type": "code", "execution_count": 14, "id": "de77217d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ TTL analyzer imported successfully\n", "✅ TTL analysis system configured\n"]}], "source": ["# TTL Analysis System - Simple Import\n", "\n", "import sys\n", "sys.path.append('.')  # Add current directory to path\n", "\n", "try:\n", "    from ttl_sparql_auto_discovery import analyze_ttl_file\n", "    print(\"✅ TTL analyzer imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Could not import TTL analyzer: {e}\")\n", "    \n", "    def analyze_ttl_file(ttl_file_path: str) -> str:\n", "        \"\"\"Fallback analyzer for testing purposes\"\"\"\n", "        return f\"\"\"# TTL Analysis for {ttl_file_path}\n", "        \n", "## File Statistics\n", "- This is a mock analysis\n", "- In production, this would analyze the actual TTL file\n", "        \n", "## RDF Classes (Types)\n", "- Example classes would be listed here\n", "        \n", "## RDF Properties (Relationships)\n", "- Example properties would be listed here\n", "        \n", "File not found, using fallback analysis.\"\"\"\n", "\n", "\n", "print(\"✅ TTL analysis system configured\")"]}, {"cell_type": "code", "execution_count": 15, "id": "8bc35528", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ SPARQL agent ready\n"]}], "source": ["# Simple SPARQL Agent\n", "\n", "# Single agent that handles everything - much simpler approach\n", "sparql_agent = Agent[str, SPARQLOutput](\n", "    model,\n", "    deps_type=str,  # Will receive the user query and TTL analysis\n", "    output_type=SPARQLOutput,\n", "    system_prompt=\"\"\"\n", "    You are a SPARQL query generator. Create syntactically correct SPARQL queries based on:\n", "    1. The user's natural language request  \n", "    2. The TTL file analysis provided in the context\n", "    \n", "    Use the actual prefixes, classes, and properties from the TTL analysis.\n", "    Always follow SPARQL 1.1 syntax.\n", "    \n", "    If you can't create a valid query, return a SPARQLError with suggestions.\n", "    \"\"\"\n", ")\n", "\n", "@sparql_agent.output_validator  \n", "async def validate_sparql_output(ctx: RunContext[str], output: SPARQLOutput) -> SPARQLOutput:\n", "    \"\"\"Validate the generated SPARQL query.\"\"\"\n", "    if isinstance(output, SPARQLError):\n", "        return output\n", "    \n", "    try:\n", "        # Validate syntax\n", "        query_type = validate_sparql_query(output.query)\n", "        output.query_type = query_type\n", "        return output\n", "        \n", "    except (SPARQLValidationError, QueryInputError) as e:\n", "        raise ModelRetry(f'SPARQL validation failed: {e}') from e\n", "\n", "print(\"✅ SPARQL agent ready\")"]}, {"cell_type": "code", "execution_count": 16, "id": "67996f72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Simple SPARQL Generator Demo\n", "========================================\n", "Usage:\n", "result = await generate_sparql_query('path/to/file.ttl', 'your query')\n", "\n", "Ready to use! 🚀\n"]}], "source": ["# Simple Demo and Testing\n", "\n", "async def demo():\n", "    \"\"\"Simple demo function\"\"\"\n", "    print(\"🎯 Simple SPARQL Generator Demo\")\n", "    print(\"=\"*40)\n", "    \n", "    # You can test with any TTL file like this:\n", "    # result = await generate_sparql_query(\"your_file.ttl\", \"Find all people\")\n", "    # print(result)\n", "    \n", "    print(\"Usage:\")\n", "    print(\"result = await generate_sparql_query('path/to/file.ttl', 'your query')\")\n", "    print(\"\\nReady to use! 🚀\")\n", "\n", "# Run the demo\n", "await demo()"]}, {"cell_type": "code", "execution_count": 17, "id": "c004b4c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Simple SPARQL generator ready!\n"]}], "source": ["# Simple Main Function\n", "\n", "async def generate_sparql_query(ttl_file_path: str, user_query: str) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Generate a SPARQL query for a TTL file - simple and direct approach.\n", "    \n", "    Args:\n", "        ttl_file_path: Path to the TTL file\n", "        user_query: What you want to query\n", "        \n", "    Returns:\n", "        Dictionary with the result\n", "    \"\"\"\n", "    try:\n", "        # 1. Analyze the TTL file\n", "        print(f\"🔍 Analyzing: {ttl_file_path}\")\n", "        ttl_analysis = analyze_ttl_file(ttl_file_path)\n", "        \n", "        # 2. Create the prompt with context\n", "        full_prompt = f\"\"\"\n", "TTL File Analysis:\n", "{ttl_analysis}\n", "\n", "User Query: {user_query}\n", "\n", "Generate a SPARQL query based on the above context.\n", "\"\"\"\n", "        \n", "        # 3. Get the result\n", "        result = await sparql_agent.run(full_prompt, deps=ttl_analysis)\n", "        \n", "        # 4. Return clean result\n", "        if isinstance(result.output, SPARQLQuery):\n", "            return {\n", "                \"success\": True,\n", "                \"query\": result.output.query,\n", "                \"query_type\": result.output.query_type,\n", "                \"description\": result.output.description\n", "            }\n", "        else:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": result.output.error_message,\n", "                \"suggestions\": result.output.suggestions\n", "            }\n", "            \n", "    except Exception as e:\n", "        return {\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": f\"Error: {e}\",\n", "            \"suggestions\": \"Check your TTL file path and try again\"\n", "        }\n", "\n", "print(\"✅ Simple SPARQL generator ready!\")"]}, {"cell_type": "code", "execution_count": 18, "id": "8057d8aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 Simplified TTL-Aware SPARQL Generator\n", "==================================================\n", "✅ All components loaded successfully!\n", "\n", "📋 What you have:\n", "- Simple SPARQL agent with validation\n", "- TTL file analysis integration\n", "- Clean error handling\n", "- Direct, non-over-engineered approach\n", "\n", "🚀 Usage:\n", "result = await generate_sparql_query('path/to/file.ttl', 'find all people')\n", "\n", "💡 The system now:\n", "1. Analyzes your TTL file structure\n", "2. Generates contextually appropriate SPARQL queries\n", "3. Validates syntax before returning\n", "4. Provides clear error messages\n", "\n", "Ready for testing! 🎉\n"]}], "source": ["# Simple Test and Demo\n", "\n", "print(\"🎯 Simplified TTL-Aware SPARQL Generator\")\n", "print(\"=\"*50)\n", "print(\"✅ All components loaded successfully!\")\n", "print()\n", "print(\"📋 What you have:\")\n", "print(\"- Simple SPARQL agent with validation\")\n", "print(\"- TTL file analysis integration\") \n", "print(\"- Clean error handling\")\n", "print(\"- Direct, non-over-engineered approach\")\n", "print()\n", "print(\"🚀 Usage:\")\n", "print(\"result = await generate_sparql_query('path/to/file.ttl', 'find all people')\")\n", "print()\n", "print(\"💡 The system now:\")\n", "print(\"1. Analyzes your TTL file structure\")\n", "print(\"2. Generates contextually appropriate SPARQL queries\")\n", "print(\"3. Validates syntax before returning\")\n", "print(\"4. Provides clear error messages\")\n", "print()\n", "print(\"Ready for testing! 🎉\")"]}, {"cell_type": "code", "execution_count": 19, "id": "c3220486", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Test scenarios and demonstrations ready\n"]}], "source": ["# Test Scenarios and Demonstrations\n", "\n", "async def demo_ttl_sparql_system():\n", "    \"\"\"Comprehensive demonstration of the TTL-aware SPARQL system\"\"\"\n", "    \n", "    print(\"🚀 TTL-Aware SPARQL Query System Demo\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Test scenarios with different query types\n", "    test_scenarios = [\n", "        {\n", "            \"description\": \"Basic data exploration\",\n", "            \"query\": \"Find all subjects, predicates, and objects in the dataset\"\n", "        },\n", "        {\n", "            \"description\": \"Class instance query\", \n", "            \"query\": \"Get the first 10 entities of each class type\"\n", "        },\n", "        {\n", "            \"description\": \"Property exploration\",\n", "            \"query\": \"Show me what properties are available and how they're used\"\n", "        },\n", "        {\n", "            \"description\": \"Boolean query\",\n", "            \"query\": \"Check if there are any people or persons in this dataset\"\n", "        },\n", "        {\n", "            \"description\": \"Complex filtering\",\n", "            \"query\": \"Find entities that have names or labels containing specific text\"\n", "        }\n", "    ]\n", "    \n", "    # Example TTL file path (can be modified for actual testing)\n", "    # For demo purposes, we'll use a hypothetical file path\n", "    ttl_file_path = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\BA\\\\.phases\\\\phase4_ttl_sparql_ai\\\\assets\\\\example.ttl\"\n", "\n", "    print(f\"📁 Target TTL file: {ttl_file_path}\")\n", "    print(\"ℹ️  Note: For actual testing, please provide a real TTL file path\")\n", "    print()\n", "    \n", "    for i, scenario in enumerate(test_scenarios, 1):\n", "        print(f\"\\n{i}. Test Scenario: {scenario['description']}\")\n", "        print(\"-\" * 40)\n", "        print(f\"User Query: '{scenario['query']}'\")\n", "        print()\n", "        \n", "        try:\n", "            # Generate query for this scenario\n", "            result = await generate_ttl_aware_sparql_query(\n", "                ttl_file_path=ttl_file_path,\n", "                user_query=scenario['query'],\n", "                verbose=False\n", "            )\n", "            \n", "            # Print the result\n", "            print_query_result(result)\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error in scenario {i}: {e}\")\n", "        \n", "        print(\"\\n\" + \"=\"*60)\n", "\n", "\n", "# Interactive testing function\n", "async def test_with_custom_ttl(ttl_file_path: str, user_queries: List[str]):\n", "    \"\"\"\n", "    Test the system with a custom TTL file and queries.\n", "    \n", "    Args:\n", "        ttl_file_path: Path to your TTL file\n", "        user_queries: List of natural language queries to test\n", "    \"\"\"\n", "    \n", "    print(f\"🧪 Testing with TTL file: {ttl_file_path}\")\n", "    print(\"=\" * 60)\n", "    \n", "    for i, query in enumerate(user_queries, 1):\n", "        print(f\"\\n{i}. Query: '{query}'\")\n", "        print(\"-\" * 40)\n", "        \n", "        result = await generate_ttl_aware_sparql_query(\n", "            ttl_file_path=ttl_file_path,\n", "            user_query=query,\n", "            verbose=True  # Show full context for debugging\n", "        )\n", "        \n", "        print_query_result(result)\n", "        \n", "        # Show TTL context if available for debugging\n", "        if result.get('ttl_context') and not result.get('success'):\n", "            print(f\"\\n📋 TTL Analysis Context:\")\n", "            print(result['ttl_context'][:500] + \"...\" if len(result['ttl_context']) > 500 else result['ttl_context'])\n", "        \n", "        print(\"\\n\" + \"=\"*60)\n", "\n", "\n", "# Quick validation test\n", "async def validate_system_components():\n", "    \"\"\"Quick test to validate that all system components are working\"\"\"\n", "    \n", "    print(\"🔧 System Components Validation\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Test 1: SPARQL validation\n", "    test_queries = [\n", "        (\"Valid SELECT\", \"SELECT ?s ?p ?o WHERE { ?s ?p ?o . } LIMIT 10\"),\n", "        (\"Invalid syntax\", \"SELET ?s WHERE { ?s ?p ?o . }\")\n", "    ]\n", "    \n", "    for name, query in test_queries:\n", "        try:\n", "            query_type = validate_sparql_query(query)\n", "            print(f\"✅ {name}: {query_type}\")\n", "        except Exception as e:\n", "            print(f\"❌ {name}: {e}\")\n", "    \n", "    # Test 2: TTL Context creation\n", "    try:\n", "        mock_context = TTLContext.from_file(\"non_existent_file.ttl\")\n", "        print(f\"✅ TTL Context: Created for non-existent file\")\n", "    except Exception as e:\n", "        print(f\"❌ TTL Context: {e}\")\n", "    \n", "    # Test 3: Agent initialization\n", "    try:\n", "        # Test that agents are properly configured\n", "        print(f\"✅ SPARQL Tool Agent: {type(sparql_tool_agent).__name__}\")\n", "        print(f\"✅ Parent Agent: {type(ttl_sparql_parent_agent).__name__}\")\n", "    except Exception as e:\n", "        print(f\"❌ Agent initialization: {e}\")\n", "    \n", "    print(\"\\n🎯 All components validated!\")\n", "\n", "print(\"✅ Test scenarios and demonstrations ready\")"]}, {"cell_type": "code", "execution_count": 20, "id": "31e5b89a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 TTL-Aware SPARQL Agent System - Ready to Use!\n", "============================================================\n", "💡 Usage Examples:\n", "\n", "# Basic Usage:\n", "result = await generate_ttl_aware_sparql_query(\n", "    ttl_file_path=\"your_file.ttl\", \n", "    user_query=\"Find all people in the dataset\"\n", ")\n", "\n", "# Advanced Usage with verbose output:\n", "result = await generate_ttl_aware_sparql_query(\n", "    ttl_file_path=\"ontology.ttl\",\n", "    user_query=\"What are the main classes and their relationships?\",\n", "    verbose=True\n", ")\n", "\n", "# Custom testing:\n", "await test_with_custom_ttl(\n", "    \"your_file.ttl\", \n", "    [\"Query 1\", \"Query 2\", \"Query 3\"]\n", ")\n", "\n", "\n", "🚀 Available Functions:\n", "- generate_ttl_aware_sparql_query() - Main query generation\n", "- demo_ttl_sparql_system() - Run demonstration scenarios\n", "- test_with_custom_ttl() - Test with your TTL files\n", "- validate_system_components() - Check system health\n", "\n", "⭐ Features:\n", "✅ Automatic TTL analysis and context awareness\n", "✅ Multi-agent architecture with specialized roles\n", "✅ SPARQL syntax validation and error handling\n", "✅ Retry mechanism for improved reliability\n", "✅ Support for SELECT, CONSTRUCT, ASK, DESCRIBE queries\n", "✅ Generic - works with any RDF vocabulary\n", "\n", "🎮 Try running:\n", "await demo_ttl_sparql_system()  # For demo scenarios\n", "await validate_system_components()  # For system check\n", "\n", "System initialized and ready! 🎉\n"]}], "source": ["# Example Usage and Demonstration\n", "\n", "print(\"🎯 TTL-Aware SPARQL Agent System - Ready to Use!\")\n", "print(\"=\" * 60)\n", "\n", "# Example usage patterns\n", "example_usage = \"\"\"\n", "# Basic Usage:\n", "result = await generate_ttl_aware_sparql_query(\n", "    ttl_file_path=\"your_file.ttl\", \n", "    user_query=\"Find all people in the dataset\"\n", ")\n", "\n", "# Advanced Usage with verbose output:\n", "result = await generate_ttl_aware_sparql_query(\n", "    ttl_file_path=\"ontology.ttl\",\n", "    user_query=\"What are the main classes and their relationships?\",\n", "    verbose=True\n", ")\n", "\n", "# Custom testing:\n", "await test_with_custom_ttl(\n", "    \"your_file.ttl\", \n", "    [\"Query 1\", \"Query 2\", \"Query 3\"]\n", ")\n", "\"\"\"\n", "\n", "print(\"💡 Usage Examples:\")\n", "print(example_usage)\n", "\n", "print(\"\\n🚀 Available Functions:\")\n", "print(\"- generate_ttl_aware_sparql_query() - Main query generation\")\n", "print(\"- demo_ttl_sparql_system() - Run demonstration scenarios\")\n", "print(\"- test_with_custom_ttl() - Test with your TTL files\") \n", "print(\"- validate_system_components() - Check system health\")\n", "\n", "print(\"\\n⭐ Features:\")\n", "print(\"✅ Automatic TTL analysis and context awareness\")\n", "print(\"✅ Multi-agent architecture with specialized roles\")\n", "print(\"✅ SPARQL syntax validation and error handling\")\n", "print(\"✅ Retry mechanism for improved reliability\")\n", "print(\"✅ Support for SELECT, CONSTRUCT, ASK, DESCRIBE queries\")\n", "print(\"✅ Generic - works with any RDF vocabulary\")\n", "\n", "print(\"\\n🎮 Try running:\")\n", "print(\"await demo_ttl_sparql_system()  # For demo scenarios\")\n", "print(\"await validate_system_components()  # For system check\")\n", "\n", "print(\"\\nSystem initialized and ready! 🎉\")"]}, {"cell_type": "markdown", "id": "229c945a", "metadata": {}, "source": ["# TTL-Aware SPARQL Query Agent System\n", "\n", "A clean, multi-agent system that automatically analyzes TTL files and generates contextually appropriate SPARQL queries.\n", "\n", "## Architecture\n", "- **Parent Agent**: Analyzes TTL files and coordinates query generation\n", "- **SPARQL Tool Agent**: Generates validated SPARQL queries based on TTL context\n", "- **Validation System**: Ensures all generated queries are syntactically correct\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}