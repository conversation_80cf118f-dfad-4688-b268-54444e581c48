"""
TTL File Auto-Discovery and Analysis Module

This module provides a generic TTL (Turtle) RDF file analyzer that works with ANY
RDF vocabulary or ontology. It generates comprehensive markdown documentation 
about the structure and content of TTL files without making any assumptions 
about specific vocabularies, prefixes, or data models.

The analyzer is completely vocabulary-agnostic and works equally well with:
- FOA<PERSON> (Friend of a Friend) personal profiles
- Dublin Core metadata
- Schema.org structured data
- Custom domain-specific vocabularies
- Building/property management systems
- Scientific datasets
- Government open data
- Any valid RDF/TTL content

Author: <PERSON><PERSON><PERSON> Labidi
Date: August 2025
License: MIT
"""

# Standard library imports for basic Python functionality
import sys                          # System-specific parameters and functions (for exit codes)
import argparse                     # Command-line argument parsing
from pathlib import Path            # Object-oriented filesystem paths (cross-platform)
from typing import Dict, Any, List  # Type hints for better code documentation and IDE support

# Third-party RDF library imports
from rdflib import Graph           # Core RDF graph for parsing and storing TTL data
from rdflib.exceptions import ParserError  # Specific exception for TTL parsing errors


class TTLFileNotFoundError(Exception):
    """
    Exception raised when the specified TTL file cannot be found.
    
    This is a specific exception for file system issues, providing
    clear context about missing files.
    """
    def __init__(self, file_path: str, attempted_path: str = None):
        self.file_path = file_path
        self.attempted_path = attempted_path
        
        message = f"TTL file not found: '{file_path}'"
        if attempted_path and attempted_path != file_path:
            message += f" (resolved to: '{attempted_path}')"
        message += ". Please verify the file exists and the path is correct."
        
        super().__init__(message)


class TTLParsingError(Exception):
    """
    Exception raised when the TTL file cannot be parsed due to syntax errors.
    
    This exception provides detailed information about TTL parsing failures,
    including line numbers and specific error descriptions when available.
    """
    def __init__(self, file_path: str, original_error: Exception, file_size: int = None):
        self.file_path = file_path
        self.original_error = original_error
        self.file_size = file_size
        
        message = f"Failed to parse TTL file '{file_path}': {str(original_error)}"
        
        if file_size is not None:
            message += f" (File size: {file_size:,} bytes)"
        
        # Add helpful suggestions for common TTL syntax issues
        error_str = str(original_error).lower()
        if any(keyword in error_str for keyword in ['syntax', 'unexpected', 'invalid']):
            message += ". This appears to be a TTL syntax error. Please check the file format and syntax."
        elif 'encoding' in error_str:
            message += ". This might be an encoding issue. Ensure the file is saved in UTF-8 format."
        
        super().__init__(message)


class TTLAnalysisError(Exception):
    """
    Exception raised when SPARQL queries fail during TTL analysis.
    
    This exception handles errors that occur during the analysis phase,
    such as malformed SPARQL queries or graph traversal issues.
    """
    def __init__(self, operation: str, original_error: Exception, ttl_file: str = None):
        self.operation = operation
        self.original_error = original_error
        self.ttl_file = ttl_file
        
        message = f"Analysis error during {operation}: {str(original_error)}"
        
        if ttl_file:
            message += f" (File: '{ttl_file}')"
        
        message += ". This indicates an issue with the SPARQL analysis queries or RDF graph structure."
        
        super().__init__(message)


class FileSystemError(Exception):
    """
    Exception raised for various file system related errors.
    
    This covers permission issues, directory problems, and other
    file system related failures.
    """
    def __init__(self, operation: str, file_path: str, original_error: Exception):
        self.operation = operation
        self.file_path = file_path
        self.original_error = original_error
        
        message = f"File system error during {operation} for '{file_path}': {str(original_error)}"
        
        # Provide specific guidance based on error type
        if 'permission' in str(original_error).lower():
            message += ". Check file permissions and ensure you have read access to the file."
        elif 'directory' in str(original_error).lower():
            message += ". Verify the directory path exists and is accessible."
        
        super().__init__(message)


def analyze_ttl_file(ttl_file_path: str) -> str:
    """
    Analyze a TTL (Turtle) RDF file and generate comprehensive markdown documentation.
    
    This function is completely generic and works with ANY TTL file structure,
    vocabulary, or ontology. It makes no assumptions about specific prefixes,
    classes, or properties. Instead, it dynamically discovers the structure
    and provides comprehensive context for understanding what can be queried.
    
    The function performs the following analysis:
    1. File statistics and metadata extraction
    2. Namespace prefix discovery and cataloging
    3. RDF class identification with instance counts
    4. Property relationship mapping and usage statistics
    5. Data type analysis for literal values
    6. Relationship pattern discovery between classes
    7. Generic SPARQL query pattern generation
    
    Args:
        ttl_file_path (str): Absolute or relative path to the TTL file to analyze.
                           The path will be resolved and validated before processing.
    
    Returns:
        str: Comprehensive markdown documentation string containing:
             - File overview and statistics
             - Namespace declarations and usage
             - Complete class and property inventories
             - Relationship patterns and data type analysis
             - Generic SPARQL query templates with placeholders
             - Structural insights and querying recommendations
    
    Raises:
        TTLFileNotFoundError: If the specified file cannot be found or accessed
        TTLParsingError: If the file cannot be parsed as valid TTL/RDF
        TTLAnalysisError: If SPARQL analysis queries fail during processing
        FileSystemError: If file system operations fail (permissions, I/O errors)
        
    Example:
        >>> analysis = analyze_ttl_file("data/example.ttl")
        >>> print(analysis)
        # TTL File Analysis: `example.ttl`
        ...
        
    Note:
        This function is completely vocabulary-agnostic. It will work equally
        well with FOAF, Dublin Core, Schema.org, custom vocabularies, or any
        valid RDF content without requiring modifications.
    """
    
    def execute_sparql_query(graph: Graph, query: str) -> List[Dict[str, Any]]:
        """
        Execute a SPARQL query against the RDF graph and return structured results.
        
        This internal helper function handles SPARQL query execution with proper
        error handling and result formatting. It converts RDFLib query results
        into standard Python dictionaries for easier processing.
        
        Args:
            graph (Graph): The RDFLib Graph object containing the TTL data
            query (str): Well-formed SPARQL query string to execute
        
        Returns:
            List[Dict[str, Any]]: List of dictionaries, one per result row,
                                 with variable names as keys and values as strings
        
        Raises:
            TTLAnalysisError: If the SPARQL query fails to execute
        """
        try:
            # Execute the SPARQL query against the RDF graph
            results = graph.query(query)
            result_list = []
            
            # Convert each result row to a dictionary
            for row in results:
                row_dict = {}
                # Iterate through each variable in the SELECT clause
                for var in results.vars:
                    value = row[var]  # Get the value for this variable in this row
                    if value:
                        # Convert RDFLib terms (URIRef, Literal, etc.) to strings
                        row_dict[str(var)] = str(value)
                    else:
                        # Handle cases where a variable has no value (None/NULL)
                        row_dict[str(var)] = None
                result_list.append(row_dict)
            
            return result_list
            
        except Exception as e:
            # Wrap any SPARQL execution errors in our custom exception
            raise TTLAnalysisError(
                operation="SPARQL query execution",
                original_error=e
            )
    
    def extract_namespace_info(uri: str) -> Dict[str, str]:
        """
        Extract namespace and local name components from a URI.
        
        This function handles different URI patterns commonly found in RDF:
        - Fragment URIs using # (e.g., http://example.org/vocab#term)
        - Path URIs using / (e.g., http://example.org/vocab/term)
        - Simple URIs without clear namespace separation
        
        Args:
            uri (str): The URI to analyze and decompose
        
        Returns:
            Dict[str, str]: Dictionary with 'namespace' and 'local_name' keys
                           containing the separated components
        """
        if '#' in uri:
            # Handle fragment-based URIs (most common in RDF vocabularies)
            namespace, local_name = uri.rsplit('#', 1)  # Split from the right, only once
            return {'namespace': namespace + '#', 'local_name': local_name}
        elif '/' in uri:
            # Handle path-based URIs
            namespace, local_name = uri.rsplit('/', 1)  # Split from the right, only once
            return {'namespace': namespace + '/', 'local_name': local_name}
        else:
            # Handle URIs without clear namespace separation
            return {'namespace': '', 'local_name': uri}
    
    def analyze_uri_patterns(uris: List[str]) -> Dict[str, Any]:
        """
        Analyze patterns in a collection of URIs to understand namespace usage.
        
        This function examines a list of URIs to identify:
        - How many different namespaces are used
        - Which namespaces are most common
        - Overall namespace distribution patterns
        
        Args:
            uris (List[str]): List of URI strings to analyze
        
        Returns:
            Dict[str, Any]: Analysis results containing:
                           - total_uris: Total number of URIs analyzed
                           - unique_namespaces: Number of distinct namespaces found
                           - namespace_distribution: List of (namespace, count) tuples
                                                   sorted by frequency
        """
        namespace_counts = {}
        
        # Count occurrences of each namespace
        for uri in uris:
            info = extract_namespace_info(uri)
            ns = info['namespace']
            if ns:  # Only count non-empty namespaces
                namespace_counts[ns] = namespace_counts.get(ns, 0) + 1
        
        return {
            'total_uris': len(uris),
            'unique_namespaces': len(namespace_counts),
            'namespace_distribution': sorted(
                namespace_counts.items(), 
                key=lambda x: x[1], 
                reverse=True  # Sort by count, descending
            )
        }
    
    # === MAIN FUNCTION LOGIC STARTS HERE ===
    
    # Step 1: Validate and load the TTL file
    try:
        # Convert string path to pathlib.Path for robust path handling
        file_path = Path(ttl_file_path)
        resolved_path = file_path.resolve()  # Get absolute path, resolving any symlinks
        
        # Check if the file exists before attempting to parse it
        if not resolved_path.exists():
            raise TTLFileNotFoundError(ttl_file_path, str(resolved_path))
        
        # Check if the path points to a file (not a directory)
        if not resolved_path.is_file():
            raise FileSystemError(
                operation="file validation",
                file_path=str(resolved_path),
                original_error=Exception(f"Path exists but is not a file: {resolved_path}")
            )
        
        # Get file size for metadata and error context
        try:
            file_size = resolved_path.stat().st_size
        except OSError as e:
            raise FileSystemError(
                operation="file size check",
                file_path=str(resolved_path),
                original_error=e
            )
        
        # Initialize RDF graph and parse the TTL file
        graph = Graph()
        
        try:
            # Parse the TTL file - this is where syntax errors would be caught
            graph.parse(resolved_path, format="turtle")
        except ParserError as e:
            # Handle TTL-specific parsing errors with detailed context
            raise TTLParsingError(str(resolved_path), e, file_size)
        except Exception as e:
            # Handle other parsing-related errors
            raise TTLParsingError(str(resolved_path), e, file_size)
        
    except (TTLFileNotFoundError, TTLParsingError, FileSystemError):
        # Re-raise our custom exceptions as-is
        raise
    except Exception as e:
        # Catch any unexpected errors during file loading
        raise FileSystemError(
            operation="file loading",
            file_path=ttl_file_path,
            original_error=e
        )
    
    # Step 2: Extract declared namespaces from the TTL file
    try:
        # Get all namespace prefixes declared in the TTL file
        # These are the @prefix declarations that can be used in SPARQL queries
        declared_namespaces = dict(graph.namespaces())
    except Exception as e:
        raise TTLAnalysisError(
            operation="namespace extraction",
            original_error=e,
            ttl_file=str(resolved_path)
        )
    
    # Step 3: Build the comprehensive markdown documentation
    md = []  # List to store markdown lines
    
    # Document header with file identification
    md.append(f"# TTL File Analysis: `{file_path.name}`")
    md.append("")
    md.append("*This analysis provides comprehensive context for understanding what can be queried from this TTL file, without making assumptions about specific vocabularies or data structures.*")
    md.append("")
    
    # File statistics section
    md.append("## File Statistics")
    md.append(f"- **File Size**: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    md.append(f"- **Total RDF Triples**: {len(graph):,}")
    md.append(f"- **Declared Namespaces**: {len(declared_namespaces)}")
    md.append("")
    
    # Namespace documentation section
    md.append("## Declared Namespace Prefixes")
    md.append("*These prefixes are declared in the TTL file and can be used in SPARQL queries:*")
    md.append("")
    
    if declared_namespaces:
        md.append("| Prefix | Namespace URI |")
        md.append("|--------|---------------|")
        # Sort namespaces alphabetically for consistent output
        for prefix, uri in sorted(declared_namespaces.items()):
            md.append(f"| `{prefix}` | `{uri}` |")
    else:
        md.append("*No namespace prefixes declared in this file.*")
    md.append("")
    
    # Step 4: Analyze RDF classes (types) in the dataset
    md.append("## RDF Classes (Types)")
    md.append("*Classes represent the types of entities in this dataset. Each class can have multiple instances.*")
    md.append("")
    
    # SPARQL query to find all classes and count their instances
    class_query = """
    SELECT ?class (COUNT(?instance) AS ?instance_count) WHERE {
        ?instance a ?class .
    }
    GROUP BY ?class
    ORDER BY DESC(?instance_count)
    """
    
    try:
        class_results = execute_sparql_query(graph, class_query)
    except TTLAnalysisError as e:
        # Re-raise with more specific context
        e.operation = "class analysis"
        e.ttl_file = str(resolved_path)
        raise e
    
    if class_results:
        md.append("| Class URI | Instance Count |")
        md.append("|-----------|----------------|")
        
        # Analyze URI patterns in the discovered classes
        class_uris = [result.get('class', '') for result in class_results]
        class_patterns = analyze_uri_patterns(class_uris)
        
        # Document each class and its instance count
        for result in class_results:
            class_uri = result.get('class', 'Unknown')
            count = result.get('instance_count', '0')
            md.append(f"| `{class_uri}` | {count} |")
        
        md.append("")
        # Provide analytical insights about class URI patterns
        md.append("### Class URI Pattern Analysis")
        md.append(f"- **Total distinct classes**: {len(class_results)}")
        md.append(f"- **Namespace diversity**: {class_patterns['unique_namespaces']} different namespaces")
        
        # Show the most common class namespaces (top 3)
        if class_patterns['namespace_distribution']:
            md.append("- **Most common class namespaces**:")
            for ns, count in class_patterns['namespace_distribution'][:3]:
                md.append(f"  - `{ns}` ({count} classes)")
    else:
        md.append("*No explicit class declarations found (no `rdf:type` statements).*")
    md.append("")
    
    # Step 5: Analyze RDF properties (relationships) in the dataset
    md.append("## RDF Properties (Relationships)")
    md.append("*Properties define relationships between resources. Each property connects subjects to objects.*")
    md.append("")
    
    # SPARQL query to find all properties with usage statistics
    property_query = """
    SELECT ?property 
           (COUNT(*) AS ?usage_count) 
           (COUNT(DISTINCT ?subject) AS ?distinct_subjects)
           (COUNT(DISTINCT ?object) AS ?distinct_objects) WHERE {
        ?subject ?property ?object .
    }
    GROUP BY ?property
    ORDER BY DESC(?usage_count)
    """
    
    try:
        property_results = execute_sparql_query(graph, property_query)
    except TTLAnalysisError as e:
        e.operation = "property analysis"
        e.ttl_file = str(resolved_path)
        raise e
    
    if property_results:
        md.append("| Property URI | Usage Count | Distinct Subjects | Distinct Objects |")
        md.append("|--------------|-------------|-------------------|------------------|")
        
        # Analyze URI patterns in the discovered properties
        property_uris = [result.get('property', '') for result in property_results]
        property_patterns = analyze_uri_patterns(property_uris)
        
        # Document each property with its usage statistics
        for result in property_results:
            prop_uri = result.get('property', 'Unknown')
            usage = result.get('usage_count', '0')
            subjects = result.get('distinct_subjects', '0')
            objects = result.get('distinct_objects', '0')
            md.append(f"| `{prop_uri}` | {usage} | {subjects} | {objects} |")
        
        md.append("")
        # Provide analytical insights about property URI patterns
        md.append("### Property URI Pattern Analysis")
        md.append(f"- **Total distinct properties**: {len(property_results)}")
        md.append(f"- **Namespace diversity**: {property_patterns['unique_namespaces']} different namespaces")
        
        # Show the most common property namespaces (top 3)
        if property_patterns['namespace_distribution']:
            md.append("- **Most common property namespaces**:")
            for ns, count in property_patterns['namespace_distribution'][:3]:
                md.append(f"  - `{ns}` ({count} properties)")
    else:
        md.append("*No properties found.*")
    md.append("")
    
    # Step 6: Analyze data value types (what kinds of objects appear in triples)
    md.append("## Data Value Types")
    md.append("*Analysis of what types of values appear as objects in triples.*")
    md.append("")
    
    # SPARQL query to categorize object types (URI, Literal, Blank Node)
    value_type_query = """
    SELECT 
        (IF(ISLITERAL(?object), "Literal", 
            IF(ISURI(?object), "URI", "Blank Node")) AS ?value_type)
        (COUNT(*) AS ?count) WHERE {
        ?subject ?property ?object .
    }
    GROUP BY ?value_type
    ORDER BY DESC(?count)
    """
    
    try:
        value_type_results = execute_sparql_query(graph, value_type_query)
    except TTLAnalysisError as e:
        e.operation = "value type analysis"
        e.ttl_file = str(resolved_path)
        raise e
    
    if value_type_results:
        md.append("| Value Type | Count | Percentage |")
        md.append("|------------|-------|------------|")
        
        # Calculate percentages for better understanding
        total_values = sum(int(result.get('count', '0')) for result in value_type_results)
        for result in value_type_results:
            value_type = result.get('value_type', 'Unknown')
            count = int(result.get('count', '0'))
            percentage = (count / total_values * 100) if total_values > 0 else 0
            md.append(f"| {value_type} | {count:,} | {percentage:.1f}% |")
    md.append("")
    
    # Step 7: Analyze literal data types (if literals exist in the data)
    literal_datatype_query = """
    SELECT ?datatype (COUNT(*) AS ?count) WHERE {
        ?subject ?property ?object .
        FILTER(ISLITERAL(?object))
        BIND(COALESCE(DATATYPE(?object), <http://www.w3.org/2001/XMLSchema#string>) AS ?datatype)
    }
    GROUP BY ?datatype
    ORDER BY DESC(?count)
    LIMIT 10
    """
    
    try:
        datatype_results = execute_sparql_query(graph, literal_datatype_query)
    except TTLAnalysisError as e:
        e.operation = "literal datatype analysis"
        e.ttl_file = str(resolved_path)
        raise e
    
    if datatype_results:
        md.append("### Literal Data Types")
        md.append("*Specific data types found in literal values:*")
        md.append("")
        md.append("| Data Type | Count |")
        md.append("|-----------|-------|")
        for result in datatype_results:
            datatype = result.get('datatype', 'Unknown')
            count = result.get('count', '0')
            md.append(f"| `{datatype}` | {count} |")
        md.append("")
    
    # Step 8: Analyze relationship patterns between classes
    md.append("## Relationship Patterns")
    md.append("*How different classes relate to each other through properties.*")
    md.append("")
    
    # SPARQL query to find class-to-class relationships
    relationship_query = """
    SELECT ?subject_class ?property ?object_class (COUNT(*) AS ?pattern_count) WHERE {
        ?subject a ?subject_class .
        ?subject ?property ?object .
        ?object a ?object_class .
    }
    GROUP BY ?subject_class ?property ?object_class
    ORDER BY DESC(?pattern_count)
    LIMIT 20
    """
    
    try:
        relationship_results = execute_sparql_query(graph, relationship_query)
    except TTLAnalysisError as e:
        e.operation = "relationship pattern analysis"
        e.ttl_file = str(resolved_path)
        raise e
    
    if relationship_results:
        md.append("| Subject Class | Property | Object Class | Occurrences |")
        md.append("|---------------|----------|--------------|-------------|")
        for result in relationship_results:
            subj_class = result.get('subject_class', 'Unknown')
            prop = result.get('property', 'Unknown')
            obj_class = result.get('object_class', 'Unknown')
            count = result.get('pattern_count', '0')
            md.append(f"| `{subj_class}` | `{prop}` | `{obj_class}` | {count} |")
    else:
        md.append("*No class-to-class relationship patterns found.*")
    md.append("")
    
    # Step 9: Provide generic SPARQL query patterns and guidance
    md.append("## Query Possibilities")
    md.append("*Based on this analysis, here are the types of queries that are possible with this dataset:*")
    md.append("")
    
    md.append("### Generic SPARQL Query Patterns")
    md.append("")
    
    # Generic pattern 1: Find all classes
    md.append("#### 1. Find All Classes")
    md.append("```sparql")
    md.append("SELECT DISTINCT ?class WHERE {")
    md.append("    ?instance a ?class .")
    md.append("}")
    md.append("```")
    md.append("")
    
    # Generic pattern 2: Find all properties
    md.append("#### 2. Find All Properties")
    md.append("```sparql")
    md.append("SELECT DISTINCT ?property WHERE {")
    md.append("    ?subject ?property ?object .")
    md.append("}")
    md.append("```")
    md.append("")
    
    # Generic pattern 3: Explore instances of any class
    md.append("#### 3. Explore Instances of Any Class")
    md.append("```sparql")
    md.append("SELECT ?instance WHERE {")
    md.append("    ?instance a <REPLACE_WITH_CLASS_URI> .")
    md.append("}")
    md.append("LIMIT 10")
    md.append("```")
    md.append("")
    
    # Generic pattern 4: Find properties of specific instances
    md.append("#### 4. Find Properties of Specific Instances")
    md.append("```sparql")
    md.append("SELECT ?property ?value WHERE {")
    md.append("    <REPLACE_WITH_INSTANCE_URI> ?property ?value .")
    md.append("}")
    md.append("```")
    md.append("")
    
    # Generic pattern 5: Explore relationships between classes
    md.append("#### 5. Explore Relationships Between Classes")
    md.append("```sparql")
    md.append("SELECT ?subject ?property ?object WHERE {")
    md.append("    ?subject a <REPLACE_WITH_SUBJECT_CLASS> .")
    md.append("    ?subject ?property ?object .")
    md.append("    ?object a <REPLACE_WITH_OBJECT_CLASS> .")
    md.append("}")
    md.append("```")
    md.append("")
    
    # Step 10: Generate summary insights and recommendations
    md.append("## Summary Insights")
    
    # Calculate summary statistics
    total_classes = len(class_results) if class_results else 0
    total_properties = len(property_results) if property_results else 0
    total_relationships = len(relationship_results) if relationship_results else 0
    
    # Determine data complexity level based on triple count
    complexity = "High" if len(graph) > 1000 else "Medium" if len(graph) > 100 else "Low"
    
    # Calculate total unique namespaces across classes and properties
    total_unique_namespaces = 0
    if class_results:
        total_unique_namespaces += class_patterns.get('unique_namespaces', 0)
    if property_results:
        total_unique_namespaces += property_patterns.get('unique_namespaces', 0)
    
    md.append(f"- **Data Complexity**: {complexity} ({len(graph):,} triples)")
    md.append(f"- **Schema Richness**: {total_classes} classes, {total_properties} properties")
    md.append(f"- **Relationship Patterns**: {total_relationships} distinct class-to-class relationships")
    md.append(f"- **Namespace Usage**: {len(declared_namespaces)} declared, with usage across {total_unique_namespaces} different namespaces")
    
    # Provide query strategy recommendations based on the data characteristics
    md.append("")
    md.append("### Recommended Query Strategy")
    
    if total_classes > 0:
        md.append("- **Start with class exploration**: Use the class list to understand entity types")
    if total_properties > 5:
        md.append("- **Property-based queries**: Rich property set allows for detailed filtering")
    if total_relationships > 0:
        md.append("- **Relationship traversal**: Follow connections between different entity types")
    if len(declared_namespaces) > 0:
        md.append("- **Use declared prefixes**: Leverage namespace prefixes for cleaner queries")
    
    md.append("")
    md.append("---")
    md.append("*This analysis provides a complete structural understanding of the TTL file without making assumptions about specific vocabularies or data models. Use the URIs and patterns identified above to construct queries appropriate for your specific use case.*")
    
    # Return the complete markdown documentation as a single string
    return "\n".join(md)


def main():
    """
    Command-line interface for the TTL file analyzer.
    
    This function provides a command-line interface that accepts a TTL file path
    as an argument and outputs the analysis to stdout or a file.
    """
    # Set up command-line argument parsing
    parser = argparse.ArgumentParser(
        description="Analyze TTL (Turtle) RDF files and generate comprehensive markdown documentation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python ttl_sparql_auto_discovery.py data.ttl
  python ttl_sparql_auto_discovery.py assets/example.ttl > analysis.md
  python ttl_sparql_auto_discovery.py /path/to/file.ttl --output report.md

This tool works with any RDF vocabulary or ontology and makes no assumptions
about specific data structures or prefixes.
        """
    )
    
    # Required positional argument for the TTL file path
    parser.add_argument(
        "ttl_file",
        help="Path to the TTL file to analyze"
    )
    
    # Optional argument for output file
    parser.add_argument(
        "-o", "--output",
        help="Output file path (if not specified, prints to stdout)"
    )
    
    # Parse command-line arguments
    args = parser.parse_args()
    
    try:
        # Perform the TTL file analysis
        analysis_result = analyze_ttl_file(args.ttl_file)
        
        # Output the results
        if args.output:
            # Write to specified output file
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(analysis_result)
                print(f"Analysis saved to: {args.output}", file=sys.stderr)
            except Exception as e:
                print(f"Error writing to output file '{args.output}': {e}", file=sys.stderr)
                sys.exit(1)
        else:
            # Print to stdout with proper encoding handling
            try:
                print(analysis_result, flush=True)
            except UnicodeEncodeError:
                # Fallback for Windows console encoding issues
                print(analysis_result.encode('utf-8', errors='replace').decode('utf-8', errors='replace'), flush=True)
    
    except (TTLFileNotFoundError, TTLParsingError, TTLAnalysisError, FileSystemError) as e:
        # Handle our custom exceptions with clear error messages
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
    
    except KeyboardInterrupt:
        # Handle Ctrl+C gracefully
        print("\nOperation cancelled by user.", file=sys.stderr)
        sys.exit(1)
    
    except Exception as e:
        # Handle any unexpected errors
        print(f"Unexpected error: {e}", file=sys.stderr)
        sys.exit(1)


# Entry point when script is run directly
if __name__ == "__main__":
    main()
