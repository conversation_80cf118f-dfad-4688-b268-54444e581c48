{"cells": [{"cell_type": "markdown", "id": "b5173064", "metadata": {}, "source": ["# TTL Data Question-Answering System\n", "\n", "A comprehensive system that processes natural language questions about TTL data, generates SPARQL queries, executes them, and provides meaningful human-readable responses.\n", "\n", "## Features\n", "- Natural language question processing\n", "- Automatic SPARQL query generation\n", "- Query execution against TTL data\n", "- Intelligent response generation\n", "- Complete question-to-answer pipeline"]}, {"cell_type": "markdown", "id": "5b9d798b", "metadata": {}, "source": ["## 1. Environment Setup and Dependencies"]}, {"cell_type": "code", "execution_count": 14, "id": "258dc114", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Environment setup complete\n", "📊 RDFLib version: 7.1.4\n", "🤖 Model initialized: GPT-4.1 Mini via OpenRouter\n"]}], "source": ["# Environment Setup and Dependencies\n", "import os\n", "import sys\n", "import dotenv\n", "import json\n", "from typing import Union, Dict, Any, List, Optional\n", "from pathlib import Path\n", "\n", "# Pydantic AI imports\n", "from pydantic import BaseModel, Field\n", "from pydantic_ai import Agent, RunContext, ModelRetry\n", "from pydantic_ai.models.openai import OpenAIModel\n", "from pydantic_ai.providers.openrouter import OpenRouterProvider\n", "\n", "# RDF and SPARQL handling\n", "import rdflib\n", "from rdflib import Graph, Namespace\n", "from rdflib.plugins.sparql import prepareQuery\n", "from rdflib.exceptions import ParserError\n", "\n", "# Load environment\n", "dotenv.load_dotenv()\n", "OR_API_KEY = os.getenv(\"OR_API_KEY\")\n", "\n", "if not OR_API_KEY:\n", "    raise ValueError(\"OR_API_KEY environment variable is required\")\n", "\n", "# Initialize model\n", "model = OpenAIModel('openai/gpt-4.1-mini', provider=OpenRouterProvider(api_key=OR_API_KEY))\n", "\n", "print(\"✅ Environment setup complete\")\n", "print(f\"📊 RDFLib version: {rdflib.__version__}\")\n", "print(f\"🤖 Model initialized: GPT-4.1 Mini via OpenRouter\")"]}, {"cell_type": "markdown", "id": "d3fcf58a", "metadata": {}, "source": ["## 2. Data Models for Question-Answering Pipeline"]}, {"cell_type": "code", "execution_count": 15, "id": "50740129", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Data models defined\n", "📋 Models: UserQuestion, SPARQLQuery, QueryResult, NaturalLanguageResponse, QAError\n"]}], "source": ["# Data Models for Question-Answering Pipeline\n", "\n", "class UserQuestion(BaseModel):\n", "    \"\"\"Represents a user's natural language question\"\"\"\n", "    question: str = Field(description=\"The user's natural language question\")\n", "    intent: Optional[str] = Field(description=\"Detected intent of the question\", default=None)\n", "\n", "class SPARQLQuery(BaseModel):\n", "    \"\"\"Represents a generated SPARQL query\"\"\"\n", "    query: str = Field(description=\"The SPARQL query string\")\n", "    query_type: str = Field(description=\"Type of SPARQL query (SELECT, ASK, etc.)\")\n", "    description: str = Field(description=\"What the query does\")\n", "    confidence: float = Field(description=\"Confidence in the query generation\", default=0.8)\n", "\n", "class QueryResult(BaseModel):\n", "    \"\"\"Represents the result of executing a SPARQL query\"\"\"\n", "    success: bool = Field(description=\"Whether the query executed successfully\")\n", "    data: List[Dict[str, Any]] = Field(description=\"Query result data\", default=[])\n", "    row_count: int = Field(description=\"Number of rows returned\", default=0)\n", "    columns: List[str] = Field(description=\"Column names in the result\", default=[])\n", "    execution_time: float = Field(description=\"Query execution time in seconds\", default=0.0)\n", "    error_message: Optional[str] = Field(description=\"Error message if query failed\", default=None)\n", "\n", "class NaturalLanguageResponse(BaseModel):\n", "    \"\"\"Represents a natural language response to the user\"\"\"\n", "    answer: str = Field(description=\"The natural language answer to the user's question\")\n", "    confidence: float = Field(description=\"Confidence in the response\", default=0.8)\n", "    data_summary: str = Field(description=\"Summary of the data found\")\n", "    additional_insights: Optional[str] = Field(description=\"Additional insights from the data\", default=None)\n", "\n", "class QAError(BaseModel):\n", "    \"\"\"Represents an error in the QA pipeline\"\"\"\n", "    error_message: str = Field(description=\"Error description\")\n", "    suggestions: str = Field(description=\"How to fix or rephrase the question\")\n", "    stage: str = Field(description=\"Which stage of the pipeline failed\")\n", "\n", "# Union types for different outputs\n", "SPARQLOutput = Union[SPARQLQuery, QAError]\n", "ResponseOutput = Union[NaturalLanguageResponse, QAError]\n", "\n", "def validate_sparql_query(query: str) -> str:\n", "    \"\"\"Validate SPARQL syntax and return query type.\"\"\"\n", "    if not query or not isinstance(query, str):\n", "        raise ValueError(\"Query must be a non-empty string\")\n", "    \n", "    try:\n", "        prepareQuery(query)\n", "        # Simple query type detection\n", "        query_upper = query.upper().strip()\n", "        if 'SELECT' in query_upper:\n", "            return 'SELECT'\n", "        elif 'ASK' in query_upper:\n", "            return 'ASK'\n", "        elif 'CONSTRUCT' in query_upper:\n", "            return 'CONSTRUCT'\n", "        elif 'DESCRIBE' in query_upper:\n", "            return 'DESCRIBE'\n", "        else:\n", "            return 'UNKNOWN'\n", "    except Exception as e:\n", "        raise ValueError(f\"Invalid SPARQL syntax: {e}\")\n", "\n", "print(\"✅ Data models defined\")\n", "print(\"📋 Models: UserQuestion, SPARQLQuery, QueryResult, NaturalLanguageResponse, QAError\")"]}, {"cell_type": "markdown", "id": "e0213632", "metadata": {}, "source": ["## 3. TTL File Analysis and SPARQL Query Generation"]}, {"cell_type": "code", "execution_count": 16, "id": "962433d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ TTL analyzer imported successfully\n", "✅ SPARQL generation agent ready\n"]}], "source": ["# TTL Analysis Import\n", "sys.path.append('.')\n", "\n", "try:\n", "    from ttl_sparql_auto_discovery import analyze_ttl_file\n", "    print(\"✅ TTL analyzer imported successfully\")\n", "except ImportError:\n", "    print(\"⚠️ TTL analyzer not found, creating fallback analyzer...\")\n", "    \n", "    def analyze_ttl_file(ttl_file_path: str) -> str:\n", "        \"\"\"Fallback TTL analyzer that reads the file and provides basic analysis\"\"\"\n", "        try:\n", "            g = Graph()\n", "            g.parse(ttl_file_path, format='turtle')\n", "            \n", "            # Get basic statistics\n", "            total_triples = len(g)\n", "            \n", "            # Get unique classes\n", "            classes_query = \"\"\"\n", "            SELECT DISTINCT ?class WHERE {\n", "                ?s a ?class .\n", "            }\n", "            \"\"\"\n", "            classes = [str(row[0]) for row in g.query(classes_query)]\n", "            \n", "            # Get unique properties\n", "            props_query = \"\"\"\n", "            SELECT DISTINCT ?prop WHERE {\n", "                ?s ?prop ?o .\n", "                FILTER(?prop != <http://www.w3.org/1999/02/22-rdf-syntax-ns#type>)\n", "            }\n", "            \"\"\"\n", "            properties = [str(row[0]) for row in g.query(props_query)]\n", "            \n", "            # Get prefixes\n", "            prefixes = dict(g.namespaces())\n", "            \n", "            analysis = f\"\"\"\n", "TTL File Analysis:\n", "- Total triples: {total_triples}\n", "- Classes found: {len(classes)}\n", "- Properties found: {len(properties)}\n", "- Prefixes: {len(prefixes)}\n", "\n", "Classes:\n", "{chr(10).join([f\"  - {cls}\" for cls in classes[:10]])}\n", "\n", "Properties:\n", "{chr(10).join([f\"  - {prop}\" for prop in properties[:15]])}\n", "\n", "Prefixes:\n", "{chr(10).join([f\"  {prefix}: {namespace}\" for prefix, namespace in list(prefixes.items())[:10]])}\n", "\"\"\"\n", "            return analysis\n", "        except Exception as e:\n", "            return f\"Error analyzing TTL file: {e}\"\n", "    \n", "    print(\"✅ Fallback TTL analyzer created\")\n", "\n", "# SPARQL Query Generation Agent\n", "sparql_agent = Agent[str, SPARQLOutput](\n", "    model,\n", "    deps_type=str,\n", "    output_type=SPARQLOutput,\n", "    system_prompt=\"\"\"You are an expert SPARQL query generator. \n", "    \n", "    Your task is to:\n", "    1. Analyze the TTL file structure provided\n", "    2. Understand the user's natural language question\n", "    3. Generate a syntactically correct SPARQL query that answers the question\n", "    4. Use actual prefixes, classes, and properties from the TTL analysis\n", "    \n", "    Guidelines:\n", "    - Always use proper prefixes from the TTL file\n", "    - Make queries as specific as possible to the user's question\n", "    - For counting questions, use COUNT(*) or COUNT(?variable)\n", "    - For listing questions, use SELECT with appropriate variables\n", "    - For existence questions, consider using ASK queries\n", "    - Include LIMIT clauses for potentially large result sets\n", "    - Return a QAError if the question cannot be answered with the available data\n", "    \"\"\"\n", ")\n", "\n", "@sparql_agent.output_validator\n", "async def validate_sparql_output(ctx: RunContext[str], output: SPARQLOutput) -> SPARQLOutput:\n", "    if isinstance(output, QAError):\n", "        return output\n", "    \n", "    try:\n", "        query_type = validate_sparql_query(output.query)\n", "        output.query_type = query_type\n", "        return output\n", "    except ValueError as e:\n", "        raise ModelRetry(f'SPARQL validation failed: {e}')\n", "\n", "print(\"✅ SPARQL generation agent ready\")"]}, {"cell_type": "markdown", "id": "945dff0d", "metadata": {}, "source": ["## 4. SPARQL Query Execution Engine"]}, {"cell_type": "code", "execution_count": 17, "id": "b57ed5ac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ SPARQL execution engine ready\n", "🚀 Features: Graph caching, multiple query types, error handling\n"]}], "source": ["# SPARQL Query Execution Engine\n", "import time\n", "\n", "class SPARQLExecutor:\n", "    \"\"\"Handles execution of SPARQL queries against TTL files\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.graphs = {}  # Cache for loaded graphs\n", "    \n", "    def load_ttl_file(self, ttl_file_path: str) -> Graph:\n", "        \"\"\"Load TTL file into RDF graph with caching\"\"\"\n", "        if ttl_file_path not in self.graphs:\n", "            try:\n", "                g = Graph()\n", "                g.parse(ttl_file_path, format='turtle')\n", "                self.graphs[ttl_file_path] = g\n", "                print(f\"📊 Loaded TTL file: {len(g)} triples\")\n", "            except Exception as e:\n", "                raise ValueError(f\"Failed to load TTL file: {e}\")\n", "        \n", "        return self.graphs[ttl_file_path]\n", "    \n", "    def execute_query(self, ttl_file_path: str, sparql_query: str) -> QueryResult:\n", "        \"\"\"Execute SPARQL query against TTL file\"\"\"\n", "        start_time = time.time()\n", "        \n", "        try:\n", "            # Load graph\n", "            g = self.load_ttl_file(ttl_file_path)\n", "            \n", "            # Execute query\n", "            results = g.query(sparql_query)\n", "            \n", "            # Process results based on query type\n", "            if hasattr(results, 'vars') and results.vars:\n", "                # SELECT query\n", "                columns = [str(var) for var in results.vars]\n", "                data = []\n", "                \n", "                for row in results:\n", "                    row_dict = {}\n", "                    for i, var in enumerate(columns):\n", "                        value = row[i]\n", "                        # Convert RDF terms to strings\n", "                        if value is not None:\n", "                            row_dict[var] = str(value)\n", "                        else:\n", "                            row_dict[var] = None\n", "                    data.append(row_dict)\n", "                \n", "                execution_time = time.time() - start_time\n", "                \n", "                return QueryResult(\n", "                    success=True,\n", "                    data=data,\n", "                    row_count=len(data),\n", "                    columns=columns,\n", "                    execution_time=execution_time\n", "                )\n", "            \n", "            <PERSON><PERSON>(results, 'askAnswer'):\n", "                # ASK query\n", "                execution_time = time.time() - start_time\n", "                \n", "                return QueryResult(\n", "                    success=True,\n", "                    data=[{\"result\": results.askAnswer}],\n", "                    row_count=1,\n", "                    columns=[\"result\"],\n", "                    execution_time=execution_time\n", "                )\n", "            \n", "            else:\n", "                # Other query types (CONSTRUCT, DESCRIBE)\n", "                execution_time = time.time() - start_time\n", "                \n", "                return QueryResult(\n", "                    success=True,\n", "                    data=[{\"result\": str(results)}],\n", "                    row_count=1,\n", "                    columns=[\"result\"],\n", "                    execution_time=execution_time\n", "                )\n", "        \n", "        except Exception as e:\n", "            execution_time = time.time() - start_time\n", "            return QueryResult(\n", "                success=False,\n", "                execution_time=execution_time,\n", "                error_message=str(e)\n", "            )\n", "    \n", "    def clear_cache(self):\n", "        \"\"\"Clear the graph cache\"\"\"\n", "        self.graphs.clear()\n", "        print(\"🧹 Graph cache cleared\")\n", "\n", "# Initialize executor\n", "executor = SPARQLExecutor()\n", "\n", "print(\"✅ SPARQL execution engine ready\")\n", "print(\"🚀 Features: Graph caching, multiple query types, error handling\")"]}, {"cell_type": "markdown", "id": "49f0d01d", "metadata": {}, "source": ["## 5. Natural Language Response Generation Agent"]}, {"cell_type": "code", "execution_count": 18, "id": "46021b28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Natural language response agent ready\n", "💬 Capabilities: Data interpretation, insight generation, conversational responses\n"]}], "source": ["# Natural Language Response Generation Agent\n", "\n", "response_agent = Agent[Dict[str, Any], ResponseOutput](\n", "    model,\n", "    deps_type=Dict[str, Any],\n", "    output_type=ResponseOutput,\n", "    system_prompt=\"\"\"You are an expert data analyst and communicator.\n", "    \n", "    Your task is to:\n", "    1. Analyze the query results from a SPARQL query\n", "    2. Understand the original user question\n", "    3. Generate a clear, natural language response that directly answers the question\n", "    4. Provide insights and context about the data\n", "    \n", "    Guidelines:\n", "    - Always directly answer the user's original question\n", "    - Use specific numbers and facts from the data\n", "    - Make the response conversational and easy to understand\n", "    - Highlight interesting patterns or insights in the data\n", "    - If no data is found, explain this clearly\n", "    - For count queries, provide the exact number and context\n", "    - For list queries, summarize the findings and mention key examples\n", "    - Use bullet points or structured format when appropriate\n", "    \n", "    Context will include:\n", "    - original_question: The user's original question\n", "    - query_result: The SPARQL query results\n", "    - sparql_query: The SPARQL query that was executed\n", "    \"\"\"\n", ")\n", "\n", "async def generate_natural_response(\n", "    original_question: str, \n", "    query_result: <PERSON>ry<PERSON><PERSON><PERSON>, \n", "    sparql_query: str\n", ") -> ResponseOutput:\n", "    \"\"\"Generate a natural language response from query results\"\"\"\n", "    \n", "    try:\n", "        # Prepare context for the response agent\n", "        context = {\n", "            \"original_question\": original_question,\n", "            \"query_result\": {\n", "                \"success\": query_result.success,\n", "                \"data\": query_result.data,\n", "                \"row_count\": query_result.row_count,\n", "                \"columns\": query_result.columns,\n", "                \"execution_time\": query_result.execution_time,\n", "                \"error_message\": query_result.error_message\n", "            },\n", "            \"sparql_query\": sparql_query\n", "        }\n", "        \n", "        # Create prompt for response generation\n", "        prompt = f\"\"\"Please analyze this query result and generate a natural language response.\n", "        \n", "Original Question: {original_question}\n", "\n", "Query Results:\n", "- Success: {query_result.success}\n", "- Rows returned: {query_result.row_count}\n", "- Columns: {query_result.columns}\n", "- Execution time: {query_result.execution_time:.3f}s\n", "\n", "Data (first 10 rows):\n", "{json.dumps(query_result.data[:10], indent=2) if query_result.data else 'No data'}\n", "\n", "SPARQL Query:\n", "{sparql_query}\n", "\"\"\"\n", "        \n", "        # Generate response\n", "        result = await response_agent.run(prompt, deps=context)\n", "        return result.output\n", "        \n", "    except Exception as e:\n", "        return QAError(\n", "            error_message=f\"Failed to generate response: {e}\",\n", "            suggestions=\"Try rephrasing your question or check if the data contains relevant information.\",\n", "            stage=\"response_generation\"\n", "        )\n", "\n", "print(\"✅ Natural language response agent ready\")\n", "print(\"💬 Capabilities: Data interpretation, insight generation, conversational responses\")"]}, {"cell_type": "markdown", "id": "fa4050a1", "metadata": {}, "source": ["## 6. Main Question-Answering Pipeline"]}, {"cell_type": "code", "execution_count": 19, "id": "1efb4827", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Question-Answering Pipeline Ready!\n", "🎯 Features:\n", "  - Complete question-to-answer pipeline\n", "  - Intelligent query generation and execution\n", "  - Natural language response generation\n", "  - Caching for improved performance\n", "  - Detailed error handling and suggestions\n"]}], "source": ["# Main Question-Answering Pipeline\n", "\n", "class TTLQuestionAnsweringSystem:\n", "    \"\"\"Complete question-answering system for TTL data\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.executor = SPARQLExecutor()\n", "        self.query_cache = {}  # Cache for generated queries\n", "    \n", "    async def answer_question(\n", "        self, \n", "        ttl_file_path: str, \n", "        question: str, \n", "        use_cache: bool = True\n", "    ) -> Dict[str, Any]:\n", "        \"\"\"Complete pipeline: question -> SPARQL -> execution -> natural response\"\"\"\n", "        \n", "        pipeline_start = time.time()\n", "        \n", "        try:\n", "            print(f\"❓ Question: {question}\")\n", "            print(f\"📁 TTL File: {Path(ttl_file_path).name}\")\n", "            print(\"🔄 Starting QA pipeline...\\n\")\n", "            \n", "            # Step 1: Analyze TTL file\n", "            print(\"📊 Step 1: Analyzing TTL file structure...\")\n", "            ttl_analysis = analyze_ttl_file(ttl_file_path)\n", "            print(\"✅ TTL analysis complete\\n\")\n", "            \n", "            # Step 2: Generate SPARQL query\n", "            print(\"🔍 Step 2: Generating SPARQL query...\")\n", "            cache_key = f\"{ttl_file_path}:{question}\"\n", "            \n", "            if use_cache and cache_key in self.query_cache:\n", "                sparql_result = self.query_cache[cache_key]\n", "                print(\"📋 Using cached query\")\n", "            else:\n", "                prompt = f\"\"\"\n", "TTL Analysis:\n", "{ttl_analysis}\n", "\n", "User Question: {question}\n", "\n", "Generate a SPARQL query that answers this question using the TTL data structure above.\n", "\"\"\"\n", "                \n", "                result = await sparql_agent.run(prompt, deps=ttl_analysis)\n", "                sparql_result = result.output\n", "                \n", "                if use_cache and isinstance(sparql_result, SPARQLQuery):\n", "                    self.query_cache[cache_key] = sparql_result\n", "            \n", "            if isinstance(sparql_result, QAError):\n", "                return {\n", "                    \"success\": <PERSON><PERSON><PERSON>,\n", "                    \"error\": sparql_result.error_message,\n", "                    \"suggestions\": sparql_result.suggestions,\n", "                    \"stage\": \"query_generation\"\n", "                }\n", "            \n", "            print(f\"✅ SPARQL query generated: {sparql_result.query_type}\")\n", "            print(f\"📝 Query: {sparql_result.query[:100]}...\"\n", "                  f\"{'[truncated]' if len(sparql_result.query) > 100 else ''}\\n\")\n", "            \n", "            # Step 3: Execute SPARQL query\n", "            print(\"⚡ Step 3: Executing SPARQL query...\")\n", "            query_result = self.executor.execute_query(ttl_file_path, sparql_result.query)\n", "            \n", "            if not query_result.success:\n", "                return {\n", "                    \"success\": <PERSON><PERSON><PERSON>,\n", "                    \"error\": query_result.error_message,\n", "                    \"suggestions\": \"The generated query could not be executed. Try rephrasing your question.\",\n", "                    \"stage\": \"query_execution\",\n", "                    \"sparql_query\": sparql_result.query\n", "                }\n", "            \n", "            print(f\"✅ Query executed successfully\")\n", "            print(f\"📊 Results: {query_result.row_count} rows in {query_result.execution_time:.3f}s\\n\")\n", "            \n", "            # Step 4: Generate natural language response\n", "            print(\"💬 Step 4: Generating natural language response...\")\n", "            nl_response = await generate_natural_response(\n", "                question, query_result, sparql_result.query\n", "            )\n", "            \n", "            if isinstance(nl_response, QAError):\n", "                return {\n", "                    \"success\": <PERSON><PERSON><PERSON>,\n", "                    \"error\": nl_response.error_message,\n", "                    \"suggestions\": nl_response.suggestions,\n", "                    \"stage\": \"response_generation\",\n", "                    \"query_result\": query_result.data\n", "                }\n", "            \n", "            total_time = time.time() - pipeline_start\n", "            print(f\"✅ Response generated successfully\\n\")\n", "            print(f\"🏁 Pipeline completed in {total_time:.3f}s\")\n", "            \n", "            # Return complete result\n", "            return {\n", "                \"success\": True,\n", "                \"answer\": nl_response.answer,\n", "                \"confidence\": nl_response.confidence,\n", "                \"data_summary\": nl_response.data_summary,\n", "                \"additional_insights\": nl_response.additional_insights,\n", "                \"sparql_query\": sparql_result.query,\n", "                \"query_type\": sparql_result.query_type,\n", "                \"execution_time\": query_result.execution_time,\n", "                \"total_pipeline_time\": total_time,\n", "                \"data_rows\": query_result.row_count,\n", "                \"raw_data\": query_result.data\n", "            }\n", "            \n", "        except Exception as e:\n", "            total_time = time.time() - pipeline_start\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": str(e),\n", "                \"suggestions\": \"An unexpected error occurred. Please try again or rephrase your question.\",\n", "                \"stage\": \"pipeline_error\",\n", "                \"total_pipeline_time\": total_time\n", "            }\n", "    \n", "    def clear_caches(self):\n", "        \"\"\"Clear all caches\"\"\"\n", "        self.executor.clear_cache()\n", "        self.query_cache.clear()\n", "        print(\"🧹 All caches cleared\")\n", "    \n", "    def get_cache_stats(self):\n", "        \"\"\"Get cache statistics\"\"\"\n", "        return {\n", "            \"query_cache_size\": len(self.query_cache),\n", "            \"graph_cache_size\": len(self.executor.graphs)\n", "        }\n", "\n", "# Initialize the QA system\n", "qa_system = TTLQuestionAnsweringSystem()\n", "\n", "print(\"✅ Question-Answering Pipeline Ready!\")\n", "print(\"🎯 Features:\")\n", "print(\"  - Complete question-to-answer pipeline\")\n", "print(\"  - Intelligent query generation and execution\")\n", "print(\"  - Natural language response generation\")\n", "print(\"  - Caching for improved performance\")\n", "print(\"  - Detailed error handling and suggestions\")"]}, {"cell_type": "markdown", "id": "9bb06009", "metadata": {}, "source": ["## 7. Interactive Testing and Examples\n", "\n", "Now let's test our complete question-answering system with various types of questions!"]}, {"cell_type": "code", "execution_count": 20, "id": "892df27e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧪 Testing TTL Question-Answering System\n", "📁 Using TTL file: example.ttl\n", "================================================================================\n"]}], "source": ["# Test Setup\n", "ttl_file = r\"C:\\Users\\<USER>\\Documents\\BA\\.phases\\phase4_ttl_sparql_ai\\assets\\example.ttl\"\n", "\n", "print(\"🧪 Testing TTL Question-Answering System\")\n", "print(f\"📁 Using TTL file: {Path(ttl_file).name}\")\n", "print(\"=\" * 80)"]}, {"cell_type": "code", "execution_count": 21, "id": "d93cf24b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔢 Test 1: Basic Counting Question\n", "========================================\n", "❓ Question: How many addresses are there in total?\n", "📁 TTL File: example.ttl\n", "🔄 Starting QA pipeline...\n", "\n", "📊 Step 1: Analyzing TTL file structure...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/class/>\n", "SELECT (COUNT(?address) AS ?totalAddresses) WHERE {...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "📊 Loaded TTL file: 2172 triples\n", "✅ Query executed successfully\n", "📊 Results: 1 rows in 0.087s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/class/>\n", "SELECT (COUNT(?address) AS ?totalAddresses) WHERE {...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "📊 Loaded TTL file: 2172 triples\n", "✅ Query executed successfully\n", "📊 Results: 1 rows in 0.087s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 5.713s\n", "\n", "📋 Question: How many addresses are there in total?\n", "\n", "💬 Answer: There are a total of 137 addresses in the dataset based on the query results.\n", "\n", "📊 Data Summary: The SPARQL query counted all entries classified as addresses and found exactly 137 such entries.\n", "\n", "💡 Additional Insights: This count provides a good overview of the total number of addresses available in the dataset, which can be useful for understanding the scope of location data covered.\n", "\n", "⏱️ Execution Time: 0.087s\n", "📈 Total Pipeline Time: 5.713s\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 5.713s\n", "\n", "📋 Question: How many addresses are there in total?\n", "\n", "💬 Answer: There are a total of 137 addresses in the dataset based on the query results.\n", "\n", "📊 Data Summary: The SPARQL query counted all entries classified as addresses and found exactly 137 such entries.\n", "\n", "💡 Additional Insights: This count provides a good overview of the total number of addresses available in the dataset, which can be useful for understanding the scope of location data covered.\n", "\n", "⏱️ Execution Time: 0.087s\n", "📈 Total Pipeline Time: 5.713s\n"]}], "source": ["# Test 1: Basic Counting Question\n", "print(\"\\n🔢 Test 1: Basic Counting Question\")\n", "print(\"=\" * 40)\n", "\n", "question1 = \"How many addresses are there in total?\"\n", "result1 = await qa_system.answer_question(ttl_file, question1)\n", "\n", "if result1[\"success\"]:\n", "    print(f\"\\n📋 Question: {question1}\")\n", "    print(f\"\\n💬 Answer: {result1['answer']}\")\n", "    print(f\"\\n📊 Data Summary: {result1['data_summary']}\")\n", "    if result1.get('additional_insights'):\n", "        print(f\"\\n💡 Additional Insights: {result1['additional_insights']}\")\n", "    print(f\"\\n⏱️ Execution Time: {result1['execution_time']:.3f}s\")\n", "    print(f\"📈 Total Pipeline Time: {result1['total_pipeline_time']:.3f}s\")\n", "else:\n", "    print(f\"❌ Error: {result1['error']}\")\n", "    print(f\"💡 Suggestions: {result1['suggestions']}\")"]}, {"cell_type": "code", "execution_count": 22, "id": "85a63fe4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🌍 Test 2: Geographic Question\n", "========================================\n", "❓ Question: What cities are represented in the data and how many addresses are in each city?\n", "📁 TTL File: example.ttl\n", "🔄 Starting QA pipeline...\n", "\n", "📊 Step 1: Analyzing TTL file structure...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 65 rows in 0.018s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 65 rows in 0.018s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 9.029s\n", "\n", "📋 Question: What cities are represented in the data and how many addresses are in each city?\n", "\n", "💬 Answer: The data represents addresses in 65 different cities. Here are the top 10 cities by number of addresses:\n", "\n", "- Frankfurt am Main: 11 addresses\n", "- München: 10 addresses\n", "- Paris: 10 addresses\n", "- London: 9 addresses\n", "- Berlin: 8 addresses\n", "- Wien (Vienna): 6 addresses\n", "- Hamburg: 5 addresses\n", "- Milano MI (Milan): 4 addresses\n", "- Raunheim: 4 addresses\n", "- Barcelona: 4 addresses\n", "\n", "This shows a strong representation from several major European cities, with Frankfurt am Main having the highest count of addresses at 11. The cities included span Germany, France, the UK, Austria, Italy, and Spain, indicating a broad geographic spread within Europe.\n", "\n", "📊 Data Summary: 65 cities are represented with varying address counts. Top 10 cities have between 4 and 11 addresses each.\n", "\n", "💡 Additional Insights: The data highlights a concentration of addresses in major European cities, with Frankfurt am Main leading. This could indicate focused data collection or relevance to these urban centers.\n", "\n", "⏱️ Execution Time: 0.018s\n", "📈 Total Pipeline Time: 9.029s\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 9.029s\n", "\n", "📋 Question: What cities are represented in the data and how many addresses are in each city?\n", "\n", "💬 Answer: The data represents addresses in 65 different cities. Here are the top 10 cities by number of addresses:\n", "\n", "- Frankfurt am Main: 11 addresses\n", "- München: 10 addresses\n", "- Paris: 10 addresses\n", "- London: 9 addresses\n", "- Berlin: 8 addresses\n", "- Wien (Vienna): 6 addresses\n", "- Hamburg: 5 addresses\n", "- Milano MI (Milan): 4 addresses\n", "- Raunheim: 4 addresses\n", "- Barcelona: 4 addresses\n", "\n", "This shows a strong representation from several major European cities, with Frankfurt am Main having the highest count of addresses at 11. The cities included span Germany, France, the UK, Austria, Italy, and Spain, indicating a broad geographic spread within Europe.\n", "\n", "📊 Data Summary: 65 cities are represented with varying address counts. Top 10 cities have between 4 and 11 addresses each.\n", "\n", "💡 Additional Insights: The data highlights a concentration of addresses in major European cities, with Frankfurt am Main leading. This could indicate focused data collection or relevance to these urban centers.\n", "\n", "⏱️ Execution Time: 0.018s\n", "📈 Total Pipeline Time: 9.029s\n"]}], "source": ["# Test 2: Geographic Question\n", "print(\"\\n🌍 Test 2: Geographic Question\")\n", "print(\"=\" * 40)\n", "\n", "question2 = \"What cities are represented in the data and how many addresses are in each city?\"\n", "result2 = await qa_system.answer_question(ttl_file, question2)\n", "\n", "if result2[\"success\"]:\n", "    print(f\"\\n📋 Question: {question2}\")\n", "    print(f\"\\n💬 Answer: {result2['answer']}\")\n", "    print(f\"\\n📊 Data Summary: {result2['data_summary']}\")\n", "    if result2.get('additional_insights'):\n", "        print(f\"\\n💡 Additional Insights: {result2['additional_insights']}\")\n", "    print(f\"\\n⏱️ Execution Time: {result2['execution_time']:.3f}s\")\n", "    print(f\"📈 Total Pipeline Time: {result2['total_pipeline_time']:.3f}s\")\n", "else:\n", "    print(f\"❌ Error: {result2['error']}\")\n", "    print(f\"💡 Suggestions: {result2['suggestions']}\")"]}, {"cell_type": "code", "execution_count": 23, "id": "69cb80d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🏢 Test 3: Building Information Question\n", "========================================\n", "❓ Question: What types of buildings are there and can you show me some examples with their names?\n", "📁 TTL File: example.ttl\n", "🔄 Starting QA pipeline...\n", "\n", "📊 Step 1: Analyzing TTL file structure...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 20 rows in 0.000s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 20 rows in 0.000s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 7.791s\n", "\n", "📋 Question: What types of buildings are there and can you show me some examples with their names?\n", "\n", "💬 Answer: There are predominantly \"Office\" type buildings in the query results. All 20 buildings listed share the same building type, \"Office.\" Here are some examples of these office buildings by name: PlusZwei, Poseidon, Magnusstraße 11, Forum Gliwice, Général Foy, Atrinova, Spectrum, New Deal, Große Elbstraße 14, and Alpha-Haus.\n", "\n", "📊 Data Summary: The query returned 20 buildings, all classified as \"Office\" type buildings, with their respective names listed.\n", "\n", "💡 Additional Insights: This suggests that the data set or location queried focuses heavily on office buildings, without other building types appearing in this sample of 20. It may reflect the nature of the area or database content targeted by the query.\n", "\n", "⏱️ Execution Time: 0.000s\n", "📈 Total Pipeline Time: 7.791s\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 7.791s\n", "\n", "📋 Question: What types of buildings are there and can you show me some examples with their names?\n", "\n", "💬 Answer: There are predominantly \"Office\" type buildings in the query results. All 20 buildings listed share the same building type, \"Office.\" Here are some examples of these office buildings by name: PlusZwei, Poseidon, Magnusstraße 11, Forum Gliwice, Général Foy, Atrinova, Spectrum, New Deal, Große Elbstraße 14, and Alpha-Haus.\n", "\n", "📊 Data Summary: The query returned 20 buildings, all classified as \"Office\" type buildings, with their respective names listed.\n", "\n", "💡 Additional Insights: This suggests that the data set or location queried focuses heavily on office buildings, without other building types appearing in this sample of 20. It may reflect the nature of the area or database content targeted by the query.\n", "\n", "⏱️ Execution Time: 0.000s\n", "📈 Total Pipeline Time: 7.791s\n"]}], "source": ["# Test 3: Building Information Question\n", "print(\"\\n🏢 Test 3: Building Information Question\")\n", "print(\"=\" * 40)\n", "\n", "question3 = \"What types of buildings are there and can you show me some examples with their names?\"\n", "result3 = await qa_system.answer_question(ttl_file, question3)\n", "\n", "if result3[\"success\"]:\n", "    print(f\"\\n📋 Question: {question3}\")\n", "    print(f\"\\n💬 Answer: {result3['answer']}\")\n", "    print(f\"\\n📊 Data Summary: {result3['data_summary']}\")\n", "    if result3.get('additional_insights'):\n", "        print(f\"\\n💡 Additional Insights: {result3['additional_insights']}\")\n", "    print(f\"\\n⏱️ Execution Time: {result3['execution_time']:.3f}s\")\n", "    print(f\"📈 Total Pipeline Time: {result3['total_pipeline_time']:.3f}s\")\n", "else:\n", "    print(f\"❌ Error: {result3['error']}\")\n", "    print(f\"💡 Suggestions: {result3['suggestions']}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "f257443b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔗 Test 4: Relationship Question\n", "========================================\n", "❓ Question: Show me some addresses in Germany with their corresponding building information\n", "📁 TTL File: example.ttl\n", "🔄 Starting QA pipeline...\n", "\n", "📊 Step 1: Analyzing TTL file structure...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 10 rows in 0.018s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 10 rows in 0.018s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 12.819s\n", "\n", "📋 Question: Show me some addresses in Germany with their corresponding building information\n", "\n", "💬 Answer: Here are some addresses in Germany along with their corresponding building information based on the query results:\n", "\n", "- The first address is on Mainzer Landstraße 172-190 in Frankfurt am Main, postal code 60327. The building there has the code 1000000005 and is classified as an Office.\n", "- In Hamburg, at Stadthausbrücke 1-3 (postal code 20355), there is an Office building with code 1000000012.\n", "- Munich has a building located at Rosenheimer Straße 141) with code 1000000047, also an Office.\n", "- Neuss features an Industrial and Distribution Warehouse building at Am Blankenwasser 22, postal 41468, code 1000000030.\n", "- Another Office building in Frankfurt am Main is at Ulmenstraße 30, postal 60325, code 1000000022.\n", "- Hamburg has another Office located at Hobe Bleichen 11, postal 20354, with building code 1000000036.\n", "- Berlin includes a Retail building at Dorotheenstraße 54, postal 10117, building code 1000000041.\n", "- Stuttgart has a Retail building on Thouretstraße 2, postal 70173, code 1000000007.\n", "- Berlin also has an Office building at Friedrichstraße 50-55, postal 10117, code 1000000002.\n", "- Finally, Frankfurt am Main includes an Office at Theodor-Heuss-Allee 2, postal 60486, building code 1000000021.\n", "\n", "Overall, the addresses come from diverse major cities in Germany, and most buildings listed are Offices, with some Retail and an Industrial warehouse. This gives a good snapshot of commercial and industrial building locations in Germany.\n", "\n", "📊 Data Summary: The dataset includes 10 address entries across various German cities, mainly Frankfurt am Main, Hamburg, Berlin, Munich, Neuss, and Stuttgart. Building types are primarily Offices, with a couple of Retail locations and one Industrial warehouse. Each entry includes detailed address components and unique building codes.\n", "\n", "💡 Additional Insights: It's notable that Frankfurt am Main appears multiple times with office buildings, indicating it may have a high concentration of commercial properties in the sample set. The diversity in building types and cities suggests a mixture of urban commercial usage.\n", "\n", "⏱️ Execution Time: 0.018s\n", "📈 Total Pipeline Time: 12.819s\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 12.819s\n", "\n", "📋 Question: Show me some addresses in Germany with their corresponding building information\n", "\n", "💬 Answer: Here are some addresses in Germany along with their corresponding building information based on the query results:\n", "\n", "- The first address is on Mainzer Landstraße 172-190 in Frankfurt am Main, postal code 60327. The building there has the code 1000000005 and is classified as an Office.\n", "- In Hamburg, at Stadthausbrücke 1-3 (postal code 20355), there is an Office building with code 1000000012.\n", "- Munich has a building located at Rosenheimer Straße 141) with code 1000000047, also an Office.\n", "- Neuss features an Industrial and Distribution Warehouse building at Am Blankenwasser 22, postal 41468, code 1000000030.\n", "- Another Office building in Frankfurt am Main is at Ulmenstraße 30, postal 60325, code 1000000022.\n", "- Hamburg has another Office located at Hobe Bleichen 11, postal 20354, with building code 1000000036.\n", "- Berlin includes a Retail building at Dorotheenstraße 54, postal 10117, building code 1000000041.\n", "- Stuttgart has a Retail building on Thouretstraße 2, postal 70173, code 1000000007.\n", "- Berlin also has an Office building at Friedrichstraße 50-55, postal 10117, code 1000000002.\n", "- Finally, Frankfurt am Main includes an Office at Theodor-Heuss-Allee 2, postal 60486, building code 1000000021.\n", "\n", "Overall, the addresses come from diverse major cities in Germany, and most buildings listed are Offices, with some Retail and an Industrial warehouse. This gives a good snapshot of commercial and industrial building locations in Germany.\n", "\n", "📊 Data Summary: The dataset includes 10 address entries across various German cities, mainly Frankfurt am Main, Hamburg, Berlin, Munich, Neuss, and Stuttgart. Building types are primarily Offices, with a couple of Retail locations and one Industrial warehouse. Each entry includes detailed address components and unique building codes.\n", "\n", "💡 Additional Insights: It's notable that Frankfurt am Main appears multiple times with office buildings, indicating it may have a high concentration of commercial properties in the sample set. The diversity in building types and cities suggests a mixture of urban commercial usage.\n", "\n", "⏱️ Execution Time: 0.018s\n", "📈 Total Pipeline Time: 12.819s\n"]}], "source": ["# Test 4: Relationship Question\n", "print(\"\\n🔗 Test 4: Relationship Question\")\n", "print(\"=\" * 40)\n", "\n", "question4 = \"Show me some addresses in Germany with their corresponding building information\"\n", "result4 = await qa_system.answer_question(ttl_file, question4)\n", "\n", "if result4[\"success\"]:\n", "    print(f\"\\n📋 Question: {question4}\")\n", "    print(f\"\\n💬 Answer: {result4['answer']}\")\n", "    print(f\"\\n📊 Data Summary: {result4['data_summary']}\")\n", "    if result4.get('additional_insights'):\n", "        print(f\"\\n💡 Additional Insights: {result4['additional_insights']}\")\n", "    print(f\"\\n⏱️ Execution Time: {result4['execution_time']:.3f}s\")\n", "    print(f\"📈 Total Pipeline Time: {result4['total_pipeline_time']:.3f}s\")\n", "else:\n", "    print(f\"❌ Error: {result4['error']}\")\n", "    print(f\"💡 Suggestions: {result4['suggestions']}\")"]}, {"cell_type": "code", "execution_count": 25, "id": "a7898438", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📈 Test 5: Complex Analysis Question\n", "========================================\n", "❓ Question: What's the distribution of buildings by construction year? Are there any patterns?\n", "📁 TTL File: example.ttl\n", "🔄 Starting QA pipeline...\n", "\n", "📊 Step 1: Analyzing TTL file structure...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ TTL analysis complete\n", "\n", "🔍 Step 2: Generating SPARQL query...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 52 rows in 0.013s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ SPARQL query generated: SELECT\n", "📝 Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX class: <https://ibpdi.datacat.org/class/>...[truncated]\n", "\n", "⚡ Step 3: Executing SPARQL query...\n", "✅ Query executed successfully\n", "📊 Results: 52 rows in 0.013s\n", "\n", "💬 Step 4: Generating natural language response...\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 10.712s\n", "\n", "📋 Question: What's the distribution of buildings by construction year? Are there any patterns?\n", "\n", "💬 Answer: The distribution of buildings by construction year, based on the data, shows a wide range of years from 1800 through the early 20th century and beyond. There are 52 distinct years recorded, each with varying counts of buildings constructed in those years. Most years have just one building recorded, but some years, like 1900, have a higher count—in this case, 3 buildings. This suggests a generally sparse distribution with occasional years of slightly more construction activity.\n", "\n", "In terms of patterns, the data indicates that many buildings date back to the 19th century, with the earliest recorded being from 1800. There is a visible increase in the number of buildings recorded as construction years approach the 20th century, with some variability. However, no very large spikes are evident in the sample up to 1922, suggesting a steady but modest pace of building construction over these years.\n", "\n", "Overall, the trend seems to show gradual growth in the number of buildings constructed as time progresses, with some years standing out with slightly higher numbers, indicating periods of increased building activity.\n", "\n", "📊 Data Summary: There are 52 unique construction years in the data, with most years having 1 building each. The earliest year recorded is 1800, and years such as 1900 have slightly higher construction counts (3 buildings). The data primarily covers years from the 19th century and early 20th century.\n", "\n", "💡 Additional Insights: This data suggests a historical spread of building construction with steady development over time, without sharp peaks in any particular year. It might be interesting to see data for more recent decades to identify if construction activity has accelerated.\n", "\n", "⏱️ Execution Time: 0.013s\n", "📈 Total Pipeline Time: 10.712s\n", "✅ Response generated successfully\n", "\n", "🏁 Pipeline completed in 10.712s\n", "\n", "📋 Question: What's the distribution of buildings by construction year? Are there any patterns?\n", "\n", "💬 Answer: The distribution of buildings by construction year, based on the data, shows a wide range of years from 1800 through the early 20th century and beyond. There are 52 distinct years recorded, each with varying counts of buildings constructed in those years. Most years have just one building recorded, but some years, like 1900, have a higher count—in this case, 3 buildings. This suggests a generally sparse distribution with occasional years of slightly more construction activity.\n", "\n", "In terms of patterns, the data indicates that many buildings date back to the 19th century, with the earliest recorded being from 1800. There is a visible increase in the number of buildings recorded as construction years approach the 20th century, with some variability. However, no very large spikes are evident in the sample up to 1922, suggesting a steady but modest pace of building construction over these years.\n", "\n", "Overall, the trend seems to show gradual growth in the number of buildings constructed as time progresses, with some years standing out with slightly higher numbers, indicating periods of increased building activity.\n", "\n", "📊 Data Summary: There are 52 unique construction years in the data, with most years having 1 building each. The earliest year recorded is 1800, and years such as 1900 have slightly higher construction counts (3 buildings). The data primarily covers years from the 19th century and early 20th century.\n", "\n", "💡 Additional Insights: This data suggests a historical spread of building construction with steady development over time, without sharp peaks in any particular year. It might be interesting to see data for more recent decades to identify if construction activity has accelerated.\n", "\n", "⏱️ Execution Time: 0.013s\n", "📈 Total Pipeline Time: 10.712s\n"]}], "source": ["# Test 5: Complex Analysis Question\n", "print(\"\\n📈 Test 5: Complex Analysis Question\")\n", "print(\"=\" * 40)\n", "\n", "question5 = \"What's the distribution of buildings by construction year? Are there any patterns?\"\n", "result5 = await qa_system.answer_question(ttl_file, question5)\n", "\n", "if result5[\"success\"]:\n", "    print(f\"\\n📋 Question: {question5}\")\n", "    print(f\"\\n💬 Answer: {result5['answer']}\")\n", "    print(f\"\\n📊 Data Summary: {result5['data_summary']}\")\n", "    if result5.get('additional_insights'):\n", "        print(f\"\\n💡 Additional Insights: {result5['additional_insights']}\")\n", "    print(f\"\\n⏱️ Execution Time: {result5['execution_time']:.3f}s\")\n", "    print(f\"📈 Total Pipeline Time: {result5['total_pipeline_time']:.3f}s\")\n", "else:\n", "    print(f\"❌ Error: {result5['error']}\")\n", "    print(f\"💡 Suggestions: {result5['suggestions']}\")"]}, {"cell_type": "code", "execution_count": 26, "id": "e92f10c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "🎯 TTL Question-Answering System Ready!\n", "\n", "📊 System Status:\n", "  - Query cache: 5 entries\n", "  - Graph cache: 1 files loaded\n", "\n", "💡 Usage:\n", "  result = await ask_question('Your question here')\n", "\n", "🚀 Try asking questions like:\n", "  - 'How many buildings have parking spaces?'\n", "  - 'What countries are represented in the data?'\n", "  - 'Show me buildings with energy efficiency class A'\n", "  - 'What's the average construction year of office buildings?'\n"]}], "source": ["# Interactive Question Function\n", "async def ask_question(question: str):\n", "    \"\"\"Convenient function to ask a question and get a formatted response\"\"\"\n", "    print(f\"\\n❓ Your Question: {question}\")\n", "    print(\"=\" * 60)\n", "    \n", "    result = await qa_system.answer_question(ttl_file, question)\n", "    \n", "    if result[\"success\"]:\n", "        print(f\"\\n💬 Answer: {result['answer']}\")\n", "        print(f\"\\n📊 Data Summary: {result['data_summary']}\")\n", "        \n", "        if result.get('additional_insights'):\n", "            print(f\"\\n💡 Insights: {result['additional_insights']}\")\n", "        \n", "        print(f\"\\n📈 Performance:\")\n", "        print(f\"  - Query execution: {result['execution_time']:.3f}s\")\n", "        print(f\"  - Total pipeline: {result['total_pipeline_time']:.3f}s\")\n", "        print(f\"  - Data rows: {result['data_rows']}\")\n", "        print(f\"  - Confidence: {result['confidence']:.1%}\")\n", "        \n", "    else:\n", "        print(f\"\\n❌ Error ({result.get('stage', 'unknown')}): {result['error']}\")\n", "        print(f\"\\n💡 Suggestions: {result['suggestions']}\")\n", "    \n", "    return result\n", "\n", "# System Status\n", "cache_stats = qa_system.get_cache_stats()\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"🎯 TTL Question-Answering System Ready!\")\n", "print(\"\\n📊 System Status:\")\n", "print(f\"  - Query cache: {cache_stats['query_cache_size']} entries\")\n", "print(f\"  - Graph cache: {cache_stats['graph_cache_size']} files loaded\")\n", "print(\"\\n💡 Usage:\")\n", "print(\"  result = await ask_question('Your question here')\")\n", "print(\"\\n🚀 Try asking questions like:\")\n", "print(\"  - 'How many buildings have parking spaces?'\")\n", "print(\"  - 'What countries are represented in the data?'\")\n", "print(\"  - 'Show me buildings with energy efficiency class A'\")\n", "print(\"  - 'What's the average construction year of office buildings?'\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}