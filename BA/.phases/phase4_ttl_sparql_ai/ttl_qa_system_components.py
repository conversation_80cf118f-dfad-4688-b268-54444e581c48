"""
TTL Question-Answering System Components
Extracted from the <PERSON><PERSON><PERSON> notebook for use in the CLI tool
"""

import os
import sys
import time
import json
from typing import Union, Dict, Any, List, Optional
from pathlib import Path

# Pydantic AI imports
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel

# RDF and SPARQL handling
import rdflib
from rdflib import Graph, Namespace
from rdflib.plugins.sparql import prepareQuery
from rdflib.exceptions import ParserError

# Load environment
import dotenv
dotenv.load_dotenv()
OR_API_KEY = os.getenv("OR_API_KEY")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", OR_API_KEY)

if not OPENROUTER_API_KEY:
    raise ValueError("OPENROUTER_API_KEY or OR_API_KEY environment variable is required")

# Set the environment variable for pydantic-ai
os.environ["OPENROUTER_API_KEY"] = OPENROUTER_API_KEY

# Initialize model - Using OpenRouter provider with o4-mini for function calling
model = OpenAIModel(
    'openai/o4-mini',
    provider='openrouter',
)

# Data Models
class UserQuestion(BaseModel):
    """Represents a user's natural language question"""
    question: str = Field(description="The user's natural language question")
    intent: Optional[str] = Field(description="Detected intent of the question", default=None)

class SPARQLQuery(BaseModel):
    """Represents a generated SPARQL query"""
    query: str = Field(description="The SPARQL query string")
    query_type: str = Field(description="Type of SPARQL query (SELECT, ASK, etc.)")
    description: str = Field(description="What the query does")
    confidence: float = Field(description="Confidence in the query generation", default=0.8)

class QueryResult(BaseModel):
    """Represents the result of executing a SPARQL query"""
    success: bool = Field(description="Whether the query executed successfully")
    data: List[Dict[str, Any]] = Field(description="Query result data", default=[])
    row_count: int = Field(description="Number of rows returned", default=0)
    columns: List[str] = Field(description="Column names in the result", default=[])
    execution_time: float = Field(description="Query execution time in seconds", default=0.0)
    error_message: Optional[str] = Field(description="Error message if query failed", default=None)

class NaturalLanguageResponse(BaseModel):
    """Represents a natural language response to the user"""
    answer: str = Field(description="The natural language answer to the user's question")
    confidence: float = Field(description="Confidence in the response", default=0.8)
    data_summary: str = Field(description="Summary of the data found")
    additional_insights: Optional[str] = Field(description="Additional insights from the data", default=None)

class QAError(BaseModel):
    """Represents an error in the QA pipeline"""
    error_message: str = Field(description="Error description")
    suggestions: str = Field(description="How to fix or rephrase the question")
    stage: str = Field(description="Which stage of the pipeline failed")

# Union types
SPARQLOutput = Union[SPARQLQuery, QAError]
ResponseOutput = Union[NaturalLanguageResponse, QAError]

def validate_sparql_query(query: str) -> str:
    """Validate SPARQL syntax and return query type."""
    if not query or not isinstance(query, str):
        raise ValueError("Query must be a non-empty string")
    
    try:
        prepareQuery(query)
        # Simple query type detection
        query_upper = query.upper().strip()
        if 'SELECT' in query_upper:
            return 'SELECT'
        elif 'ASK' in query_upper:
            return 'ASK'
        elif 'CONSTRUCT' in query_upper:
            return 'CONSTRUCT'
        elif 'DESCRIBE' in query_upper:
            return 'DESCRIBE'
        else:
            return 'UNKNOWN'
    except Exception as e:
        raise ValueError(f"Invalid SPARQL syntax: {e}")

def analyze_ttl_file(ttl_file_path: str) -> str:
    """Comprehensively analyze TTL file and provide detailed structure information"""
    try:
        g = Graph()
        g.parse(ttl_file_path, format='turtle')
        
        # Get basic statistics
        total_triples = len(g)
        
        # Get unique classes
        classes_query = """
        SELECT DISTINCT ?class WHERE {
            ?s a ?class .
        }
        """
        classes = [str(row[0]) for row in g.query(classes_query)]
        
        # Get unique properties
        props_query = """
        SELECT DISTINCT ?prop WHERE {
            ?s ?prop ?o .
            FILTER(?prop != <http://www.w3.org/1999/02/22-rdf-syntax-ns#type>)
        }
        """
        properties = [str(row[0]) for row in g.query(props_query)]
        
        # Get prefixes and their namespaces
        prefixes = dict(g.namespaces())
        
        # Organize properties by namespace for better understanding
        property_by_namespace = {}
        class_by_namespace = {}
        
        for prop in properties:
            for prefix, namespace in prefixes.items():
                if prop.startswith(str(namespace)):
                    if prefix not in property_by_namespace:
                        property_by_namespace[prefix] = []
                    # Get the local name (after the namespace)
                    local_name = prop.replace(str(namespace), '')
                    property_by_namespace[prefix].append(f"{prefix}:{local_name}")
                    break
        
        for cls in classes:
            for prefix, namespace in prefixes.items():
                if cls.startswith(str(namespace)):
                    if prefix not in class_by_namespace:
                        class_by_namespace[prefix] = []
                    # Get the local name (after the namespace)
                    local_name = cls.replace(str(namespace), '')
                    class_by_namespace[prefix].append(f"{prefix}:{local_name}")
                    break
        
        # Get sample data for key properties (especially important for understanding data patterns)
        sample_data = {}
        for prop in properties[:10]:  # Sample first 10 properties
            sample_query = f"""
            SELECT DISTINCT ?value WHERE {{
                ?s <{prop}> ?value .
            }} LIMIT 5
            """
            try:
                values = [str(row[0]) for row in g.query(sample_query)]
                if values:
                    # Get local name for better readability
                    prop_local = prop
                    for prefix, namespace in prefixes.items():
                        if prop.startswith(str(namespace)):
                            prop_local = f"{prefix}:{prop.replace(str(namespace), '')}"
                            break
                    sample_data[prop_local] = values[:3]  # Keep only first 3 examples
            except:
                pass
        
        # Get instance count per class
        class_counts = {}
        for cls in classes:
            count_query = f"""
            SELECT (COUNT(?s) as ?count) WHERE {{
                ?s a <{cls}> .
            }}
            """
            try:
                result = list(g.query(count_query))
                if result:
                    cls_local = cls
                    for prefix, namespace in prefixes.items():
                        if cls.startswith(str(namespace)):
                            cls_local = f"{prefix}:{cls.replace(str(namespace), '')}"
                            break
                    class_counts[cls_local] = int(result[0][0])
            except:
                pass

        analysis = f"""COMPREHENSIVE TTL FILE ANALYSIS:

=== BASIC STATISTICS ===
- Total triples: {total_triples}
- Classes found: {len(classes)}
- Properties found: {len(properties)}
- Prefixes defined: {len(prefixes)}

=== PREFIX DEFINITIONS ===
{chr(10).join([f"@prefix {prefix}: <{namespace}> ." for prefix, namespace in list(prefixes.items())])}

=== CLASSES BY NAMESPACE ===
{chr(10).join([f"{prefix} namespace classes: {', '.join(classes)}" for prefix, classes in class_by_namespace.items()])}

=== PROPERTIES BY NAMESPACE ===
{chr(10).join([f"{prefix} namespace properties: {', '.join(props[:10])}" + ("..." if len(props) > 10 else "") for prefix, props in property_by_namespace.items()])}

=== CLASS INSTANCE COUNTS ===
{chr(10).join([f"- {cls}: {count} instances" for cls, count in class_counts.items()])}

=== SAMPLE DATA VALUES ===
{chr(10).join([f"- {prop}: {', '.join(values)}" for prop, values in sample_data.items()])}

=== CRITICAL NAMESPACE MAPPING RULES ===
- For SPARQL queries, use the EXACT prefix definitions shown above
- Properties are typically in the "prop" namespace: prop:property-name
- Classes are typically in the "ibpdi" namespace: ibpdi:ClassName
- Always check which namespace a property belongs to before writing queries

=== QUERY CONSTRUCTION GUIDANCE ===
- Use PREFIX declarations exactly as shown in the prefix definitions
- Match property and class names with their correct namespaces
- Pay attention to the sample data values for exact string matching
- Remember that string values are case-sensitive
"""
        return analysis
    except Exception as e:
        return f"Error analyzing TTL file: {e}"

# SPARQL Query Generation Agent
sparql_agent = Agent[str, SPARQLOutput](
    model,
    deps_type=str,
    output_type=SPARQLOutput,
    system_prompt="""You are an expert SPARQL query generator. 
    
    Your task is to:
    1. CAREFULLY analyze the comprehensive TTL file structure provided
    2. Understand the user's natural language question
    3. Generate a syntactically correct SPARQL query that answers the question
    4. Use the EXACT prefixes, classes, and properties from the TTL analysis
    
    CRITICAL Namespace Usage Rules:
    - ALWAYS use the exact PREFIX definitions provided in the TTL analysis
    - Properties are usually in a specific property namespace (e.g., prop:property-name)
    - Classes are usually in a specific class namespace (e.g., ibpdi:ClassName)
    - Study the "PROPERTIES BY NAMESPACE" section carefully to use correct prefixes
    - Study the "CLASSES BY NAMESPACE" section to identify the right class prefixes
    - Use the "SAMPLE DATA VALUES" to understand exact string formats and casing
    
    Query Construction Guidelines:
    - Always use proper PREFIX declarations from the TTL analysis
    - Make queries as specific as possible to the user's question
    - For counting questions, use COUNT(*) or COUNT(?variable)
    - For listing questions, use SELECT with appropriate variables
    - For existence questions, consider using ASK queries
    - NEVER add LIMIT clauses unless explicitly requested by the user
    - Always return complete results without artificial limitations
    - When filtering by string values, use EXACT case matching from sample data
    - If string matching fails, suggest case-insensitive approaches using FILTER with LCASE() or regex
    - Return a QAError if the question cannot be answered with the available data
    
    IMPORTANT: Pay special attention to which namespace each property belongs to!
    Don't assume - check the TTL analysis carefully.
    """
)

@sparql_agent.output_validator
async def validate_sparql_output(ctx: RunContext[str], output: SPARQLOutput) -> SPARQLOutput:
    if isinstance(output, QAError):
        return output
    
    try:
        query_type = validate_sparql_query(output.query)
        output.query_type = query_type
        return output
    except ValueError as e:
        raise ModelRetry(f'SPARQL validation failed: {e}')

# SPARQL Query Execution Engine
class SPARQLExecutor:
    """Handles execution of SPARQL queries against TTL files"""
    
    def __init__(self):
        self.graphs = {}  # Cache for loaded graphs
    
    def load_ttl_file(self, ttl_file_path: str) -> Graph:
        """Load TTL file into RDF graph with caching"""
        if ttl_file_path not in self.graphs:
            try:
                g = Graph()
                g.parse(ttl_file_path, format='turtle')
                self.graphs[ttl_file_path] = g
            except Exception as e:
                raise ValueError(f"Failed to load TTL file: {e}")
        
        return self.graphs[ttl_file_path]
    
    def execute_query(self, ttl_file_path: str, sparql_query: str) -> QueryResult:
        """Execute SPARQL query against TTL file"""
        start_time = time.time()
        
        try:
            # Load graph
            g = self.load_ttl_file(ttl_file_path)
            
            # Execute query
            results = g.query(sparql_query)
            
            # Process results based on query type
            if hasattr(results, 'vars') and results.vars:
                # SELECT query
                columns = [str(var) for var in results.vars]
                data = []
                
                for row in results:
                    row_dict = {}
                    for i, var in enumerate(columns):
                        value = row[i]
                        # Convert RDF terms to strings
                        if value is not None:
                            row_dict[var] = str(value)
                        else:
                            row_dict[var] = None
                    data.append(row_dict)
                
                execution_time = time.time() - start_time
                
                return QueryResult(
                    success=True,
                    data=data,
                    row_count=len(data),
                    columns=columns,
                    execution_time=execution_time
                )
            
            elif hasattr(results, 'askAnswer'):
                # ASK query
                execution_time = time.time() - start_time
                
                return QueryResult(
                    success=True,
                    data=[{"result": results.askAnswer}],
                    row_count=1,
                    columns=["result"],
                    execution_time=execution_time
                )
            
            else:
                # Other query types (CONSTRUCT, DESCRIBE)
                execution_time = time.time() - start_time
                
                return QueryResult(
                    success=True,
                    data=[{"result": str(results)}],
                    row_count=1,
                    columns=["result"],
                    execution_time=execution_time
                )
        
        except Exception as e:
            execution_time = time.time() - start_time
            return QueryResult(
                success=False,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def clear_cache(self):
        """Clear the graph cache"""
        self.graphs.clear()

# Natural Language Response Generation Agent
response_agent = Agent[Dict[str, Any], ResponseOutput](
    model,
    deps_type=Dict[str, Any],
    output_type=ResponseOutput,
    system_prompt="""You are an expert data analyst and communicator.
    
    Your task is to:
    1. Analyze the query results from a SPARQL query
    2. Understand the original user question
    3. Generate a clear, natural language response that directly answers the question
    4. Provide insights and context about the data
    
    Guidelines:
    - Always directly answer the user's original question
    - Use specific numbers and facts from the data
    - Make the response conversational and easy to understand
    - Highlight interesting patterns or insights in the data
    - If no data is found, explain this clearly
    - For count queries, provide the exact number and context
    - For list queries, summarize the findings and mention key examples
    - Use bullet points or structured format when appropriate
    """
)

async def generate_natural_response(
    original_question: str, 
    query_result: QueryResult, 
    sparql_query: str
) -> ResponseOutput:
    """Generate a natural language response from query results"""
    
    try:
        # Prepare context for the response agent
        context = {
            "original_question": original_question,
            "query_result": {
                "success": query_result.success,
                "data": query_result.data,
                "row_count": query_result.row_count,
                "columns": query_result.columns,
                "execution_time": query_result.execution_time,
                "error_message": query_result.error_message
            },
            "sparql_query": sparql_query
        }
        
        # Create prompt for response generation
        prompt = f"""Please analyze this query result and generate a natural language response.
        
Original Question: {original_question}

Query Results:
- Success: {query_result.success}
- Rows returned: {query_result.row_count}
- Columns: {query_result.columns}
- Execution time: {query_result.execution_time:.3f}s

Data (first 10 rows):
{json.dumps(query_result.data[:10], indent=2) if query_result.data else 'No data'}

SPARQL Query:
{sparql_query}
"""
        
        # Generate response
        result = await response_agent.run(prompt, deps=context)
        return result.output
        
    except Exception as e:
        return QAError(
            error_message=f"Failed to generate response: {e}",
            suggestions="Try rephrasing your question or check if the data contains relevant information.",
            stage="response_generation"
        )

# Main QA System
class TTLQuestionAnsweringSystem:
    """Complete question-answering system for TTL data"""
    
    def __init__(self):
        self.executor = SPARQLExecutor()
        self.query_cache = {}  # Cache for generated queries
    
    async def answer_question(
        self, 
        ttl_file_path: str, 
        question: str, 
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """Complete pipeline: question -> SPARQL -> execution -> natural response"""
        
        pipeline_start = time.time()
        
        try:
            # Step 1: Analyze TTL file
            ttl_analysis = analyze_ttl_file(ttl_file_path)
            
            # Step 2: Generate SPARQL query
            cache_key = f"{ttl_file_path}:{question}"
            
            if use_cache and cache_key in self.query_cache:
                sparql_result = self.query_cache[cache_key]
            else:
                prompt = f"""
TTL Analysis:
{ttl_analysis}

User Question: {question}

Generate a SPARQL query that answers this question using the TTL data structure above.
"""
                
                result = await sparql_agent.run(prompt, deps=ttl_analysis)
                sparql_result = result.output
                
                if use_cache and isinstance(sparql_result, SPARQLQuery):
                    self.query_cache[cache_key] = sparql_result
            
            if isinstance(sparql_result, QAError):
                return {
                    "success": False,
                    "error": sparql_result.error_message,
                    "suggestions": sparql_result.suggestions,
                    "stage": "query_generation"
                }
            
            # Step 3: Execute SPARQL query
            query_result = self.executor.execute_query(ttl_file_path, sparql_result.query)
            
            if not query_result.success:
                return {
                    "success": False,
                    "error": query_result.error_message,
                    "suggestions": "The generated query could not be executed. Try rephrasing your question.",
                    "stage": "query_execution",
                    "sparql_query": sparql_result.query
                }
            
            # Step 4: Generate natural language response
            nl_response = await generate_natural_response(
                question, query_result, sparql_result.query
            )
            
            if isinstance(nl_response, QAError):
                return {
                    "success": False,
                    "error": nl_response.error_message,
                    "suggestions": nl_response.suggestions,
                    "stage": "response_generation",
                    "query_result": query_result.data
                }
            
            total_time = time.time() - pipeline_start
            
            # Return complete result
            return {
                "success": True,
                "answer": nl_response.answer,
                "confidence": nl_response.confidence,
                "data_summary": nl_response.data_summary,
                "additional_insights": nl_response.additional_insights,
                "sparql_query": sparql_result.query,
                "query_type": sparql_result.query_type,
                "execution_time": query_result.execution_time,
                "total_pipeline_time": total_time,
                "data_rows": query_result.row_count,
                "raw_data": query_result.data
            }
            
        except Exception as e:
            total_time = time.time() - pipeline_start
            return {
                "success": False,
                "error": str(e),
                "suggestions": "An unexpected error occurred. Please try again or rephrase your question.",
                "stage": "pipeline_error",
                "total_pipeline_time": total_time
            }
    
    def clear_caches(self):
        """Clear all caches"""
        self.executor.clear_cache()
        self.query_cache.clear()
    
    def get_cache_stats(self):
        """Get cache statistics"""
        return {
            "query_cache_size": len(self.query_cache),
            "graph_cache_size": len(self.executor.graphs)
        }
