{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7caac14c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Setup complete\n"]}], "source": ["# Environment Setup\n", "import os\n", "import sys\n", "import dotenv\n", "from typing import Union, Dict, Any\n", "from pathlib import Path\n", "\n", "# Pydantic AI imports\n", "from pydantic import BaseModel, Field\n", "from pydantic_ai import Agent, RunContext, ModelRetry\n", "from pydantic_ai.models.openai import OpenAIModel\n", "from pydantic_ai.providers.openrouter import OpenRouterProvider\n", "\n", "# RDF validation\n", "from rdflib.plugins.sparql import prepareQuery\n", "from rdflib.exceptions import ParserError\n", "\n", "# Load environment\n", "dotenv.load_dotenv()\n", "OR_API_KEY = os.getenv(\"OR_API_KEY\")\n", "\n", "# Initialize model\n", "model = OpenAIModel('openai/gpt-4o-mini', provider=OpenRouterProvider(api_key=OR_API_KEY))\n", "\n", "print(\"✅ Setup complete\")"]}, {"cell_type": "code", "execution_count": 2, "id": "4dd4687c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Models ready\n"]}], "source": ["# Data Models and Validation\n", "\n", "class SPARQLQuery(BaseModel):\n", "    query: str = Field(description=\"The SPARQL query string\")\n", "    query_type: str = Field(description=\"Type of SPARQL query\")\n", "    description: str = Field(description=\"What the query does\")\n", "\n", "class SPARQLError(BaseModel):\n", "    error_message: str = Field(description=\"Error description\")\n", "    suggestions: str = Field(description=\"How to fix it\")\n", "\n", "SPARQLOutput = Union[SPARQLQuery, SPARQLError]\n", "\n", "def validate_sparql_query(query: str) -> str:\n", "    \"\"\"Validate SPARQL syntax and return query type.\"\"\"\n", "    if not query or not isinstance(query, str):\n", "        raise ValueError(\"Query must be a non-empty string\")\n", "    \n", "    try:\n", "        prepareQuery(query)\n", "        # Simple query type detection\n", "        query_upper = query.upper().strip()\n", "        if 'SELECT' in query_upper:\n", "            return 'SELECT'\n", "        elif 'ASK' in query_upper:\n", "            return 'ASK'\n", "        elif 'CONSTRUCT' in query_upper:\n", "            return 'CONSTRUCT'\n", "        elif 'DESCRIBE' in query_upper:\n", "            return 'DESCRIBE'\n", "        else:\n", "            return 'UNKNOWN'\n", "    except Exception as e:\n", "        raise ValueError(f\"Invalid SPARQL syntax: {e}\")\n", "\n", "print(\"✅ Models ready\")"]}, {"cell_type": "code", "execution_count": 3, "id": "8b41145f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ TTL analyzer imported\n"]}], "source": ["# TTL Analysis Import\n", "sys.path.append('.')\n", "\n", "try:\n", "    from ttl_sparql_auto_discovery import analyze_ttl_file\n", "    print(\"✅ TTL analyzer imported\")\n", "except ImportError:\n", "    def analyze_ttl_file(ttl_file_path: str) -> str:\n", "        return f\"Mock analysis for {ttl_file_path} - analyzer not found\"\n", "    print(\"⚠️ Using mock TTL analyzer\")"]}, {"cell_type": "code", "execution_count": 4, "id": "a2d16a55", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Agent ready\n"]}], "source": ["# SPARQL Agent\n", "sparql_agent = Agent[str, SPARQLOutput](\n", "    model,\n", "    deps_type=str,\n", "    output_type=SPARQLOutput,\n", "    system_prompt=\"\"\"You are a SPARQL query generator. \n", "    Create syntactically correct SPARQL queries based on the TTL analysis and user request.\n", "    Use actual prefixes and properties from the analysis.\n", "    If you can't create a query, return a SPARQLError.\"\"\"\n", ")\n", "\n", "@sparql_agent.output_validator\n", "async def validate_output(ctx: RunContext[str], output: SPARQLOutput) -> SPARQLOutput:\n", "    if isinstance(output, SPARQLError):\n", "        return output\n", "    \n", "    try:\n", "        query_type = validate_sparql_query(output.query)\n", "        output.query_type = query_type\n", "        return output\n", "    except ValueError as e:\n", "        raise ModelRetry(f'SPARQL validation failed: {e}')\n", "\n", "print(\"✅ Agent ready\")"]}, {"cell_type": "code", "execution_count": 5, "id": "f2935604", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Main function ready\n"]}], "source": ["# Main Function\n", "async def generate_sparql_query(ttl_file_path: str, user_query: str) -> Dict[str, Any]:\n", "    \"\"\"Generate SPARQL query for TTL file.\"\"\"\n", "    try:\n", "        # Analyze TTL file\n", "        ttl_analysis = analyze_ttl_file(ttl_file_path)\n", "        \n", "        # Create prompt\n", "        prompt = f\"\"\"\n", "TTL Analysis:\n", "{ttl_analysis}\n", "\n", "User Query: {user_query}\n", "\"\"\"\n", "        \n", "        # Get result\n", "        result = await sparql_agent.run(prompt, deps=ttl_analysis)\n", "        \n", "        # Return result\n", "        if isinstance(result.output, SPARQLQuery):\n", "            return {\n", "                \"success\": True,\n", "                \"query\": result.output.query,\n", "                \"query_type\": result.output.query_type,\n", "                \"description\": result.output.description\n", "            }\n", "        else:\n", "            return {\n", "                \"success\": <PERSON><PERSON><PERSON>,\n", "                \"error\": result.output.error_message,\n", "                \"suggestions\": result.output.suggestions\n", "            }\n", "            \n", "    except Exception as e:\n", "        return {\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": str(e),\n", "            \"suggestions\": \"Check TTL file path and try again\"\n", "        }\n", "\n", "print(\"✅ Main function ready\")"]}, {"cell_type": "code", "execution_count": 6, "id": "81981ea9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test 1: Find all addresses\n", "Success: True\n", "Query Type: SELECT\n", "Query: PREFIX ibpdi: <https://ibpdi.datacat.org/property/>\n", "PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>\n", "PREFIX inst: <https://example.com/>\n", "\n", "SELECT ?address ?city ?country WHERE {\n", "    ?address a <https://ibpdi.datacat.org/class/Address> .\n", "    ?address ibpdi:city ?city .\n", "    ?address ibpdi:country ?country .\n", "}\n", "\n", "==================================================\n", "\n", "Test 2: Find buildings\n", "Success: True\n", "Query Type: SELECT\n", "Query: SELECT ?building ?address WHERE { ?building a <https://ibpdi.datacat.org/class/Building> . ?address a <https://ibpdi.datacat.org/class/Address> . ?address <https://ibpdi.datacat.org/class/hasBuilding> ?building . }\n", "\n", "==================================================\n", "\n", "Test 3: Count entities\n", "Success: True\n", "Query Type: SELECT\n", "Query: SELECT (COUNT(DISTINCT ?address) AS ?addressCount) (COUNT(DISTINCT ?building) AS ?buildingCount) WHERE { \n", "    ?address a <https://ibpdi.datacat.org/class/Address> .\n", "    ?building a <https://ibpdi.datacat.org/class/Building> .\n", "}\n", "\n", "✅ All tests completed\n"]}], "source": ["# Test with example.ttl\n", "ttl_file = r\"C:\\Users\\<USER>\\Documents\\BA\\.phases\\phase4_ttl_sparql_ai\\assets\\example.ttl\"\n", "\n", "# Test 1: Basic query\n", "print(\"Test 1: Find all addresses\")\n", "result1 = await generate_sparql_query(ttl_file, \"Find all addresses with their cities and countries\")\n", "print(f\"Success: {result1['success']}\")\n", "if result1['success']:\n", "    print(f\"Query Type: {result1['query_type']}\")\n", "    print(f\"Query: {result1['query']}\")\n", "else:\n", "    print(f\"Error: {result1['error']}\")\n", "\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "# Test 2: Building query\n", "print(\"Test 2: Find buildings\")\n", "result2 = await generate_sparql_query(ttl_file, \"Get all buildings and their addresses\")\n", "print(f\"Success: {result2['success']}\")\n", "if result2['success']:\n", "    print(f\"Query Type: {result2['query_type']}\")\n", "    print(f\"Query: {result2['query']}\")\n", "else:\n", "    print(f\"Error: {result2['error']}\")\n", "\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "# Test 3: Count query\n", "print(\"Test 3: Count entities\")\n", "result3 = await generate_sparql_query(ttl_file, \"Count how many addresses and buildings exist\")\n", "print(f\"Success: {result3['success']}\")\n", "if result3['success']:\n", "    print(f\"Query Type: {result3['query_type']}\")\n", "    print(f\"Query: {result3['query']}\")\n", "else:\n", "    print(f\"Error: {result3['error']}\")\n", "\n", "print(\"\\n✅ All tests completed\")"]}, {"cell_type": "code", "execution_count": 7, "id": "51f71371", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎯 TTL-Aware SPARQL Generator Ready!\n", "\n", "📝 Usage:\n", "result = await generate_sparql_query('path/to/file.ttl', 'your query')\n", "\n", "✅ Features:\n", "- Analyzes TTL file structure automatically\n", "- Generates contextually appropriate SPARQL queries\n", "- Validates syntax before returning\n", "- Clean error handling\n", "\n", "🚀 Ready to use!\n"]}], "source": ["# Simple Usage Example\n", "print(\"🎯 TTL-Aware SPARQL Generator Ready!\")\n", "print(\"\\n📝 Usage:\")\n", "print(\"result = await generate_sparql_query('path/to/file.ttl', 'your query')\")\n", "print(\"\\n✅ Features:\")\n", "print(\"- Analyzes TTL file structure automatically\")\n", "print(\"- Generates contextually appropriate SPARQL queries\") \n", "print(\"- Validates syntax before returning\")\n", "print(\"- Clean error handling\")\n", "print(\"\\n🚀 Ready to use!\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}