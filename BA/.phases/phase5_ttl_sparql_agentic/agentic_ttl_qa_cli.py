#!/usr/bin/env python3
"""
Agentic TTL Question-Answering CLI Tool
An intelligent agentic system for querying TTL data with natural language
The agent can generate, validate, and iteratively improve SPARQL queries based on results
"""

import os
import sys
import asyncio
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

# Rich imports for beautiful CLI
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich.table import Table
from rich.markdown import Markdown
from rich import box
from rich.align import Align
from rich.syntax import Syntax
from rich.live import Live

# Load environment
import dotenv
dotenv.load_dotenv()

# Pydantic AI imports
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

# RDF and SPARQL handling
import rdflib
from rdflib import Graph
from rdflib.plugins.sparql import prepareQuery
from rdflib.exceptions import ParserError

# Set up API key
OR_API_KEY = os.getenv("OR_API_KEY")


model = OpenAIModel(
    'openai/gpt-4.1-mini',
    provider=OpenRouterProvider(api_key=OR_API_KEY)
)

# Data Models
@dataclass
class TTLContext:
    """Context containing TTL graph and analysis"""
    graph: Graph
    file_path: str
    analysis: str
    
class UserQuery(BaseModel):
    """Represents a user's natural language question"""
    question: str = Field(description="The user's natural language question")
    intent: Optional[str] = Field(description="Detected intent of the question", default=None)

class SPARQLQuery(BaseModel):
    """Represents a generated and validated SPARQL query"""
    query: str = Field(description="The SPARQL query string")
    query_type: str = Field(description="Type of SPARQL query (SELECT, ASK, etc.)")
    description: str = Field(description="What the query does")
    confidence: float = Field(description="Confidence in the query generation", default=0.8)
    validation_passed: bool = Field(description="Whether the query passed syntax validation", default=False)

class QAError(BaseModel):
    """Represents an error in the QA pipeline"""
    error_message: str = Field(description="Error description")
    suggestions: str = Field(description="How to fix or rephrase the question")
    stage: str = Field(description="Which stage of the pipeline failed")

# Union types for SPARQL agent output
SPARQLOutput = Union[SPARQLQuery, QAError]

class QueryResult(BaseModel):
    """Represents the result of executing a SPARQL query"""
    success: bool = Field(description="Whether the query executed successfully")
    data: List[Dict[str, Any]] = Field(description="Query result data", default=[])
    row_count: int = Field(description="Number of rows returned", default=0)
    columns: List[str] = Field(description="Column names in the result", default=[])
    execution_time: float = Field(description="Query execution time in seconds", default=0.0)
    error_message: Optional[str] = Field(description="Error message if query failed", default=None)

class AnalysisInsight(BaseModel):
    """Represents an analysis insight from query results"""
    insight: str = Field(description="The insight discovered from the data")
    confidence: float = Field(description="Confidence in the insight", default=0.8)
    supporting_data: str = Field(description="Summary of data supporting this insight")

class FinalAnswer(BaseModel):
    """Final answer to the user's question"""
    answer: str = Field(description="Natural language answer to the user's question")
    confidence: float = Field(description="Overall confidence in the response", default=0.8)
    queries_used: List[str] = Field(description="List of SPARQL queries used", default=[])
    insights: List[str] = Field(description="Key insights discovered", default=[])
    methodology: str = Field(description="Explanation of the approach taken")

class AgentStrategy(BaseModel):
    """Strategy for approaching the user's question"""
    approach: str = Field(description="High-level approach to take")
    steps: List[str] = Field(description="Specific steps to execute")
    expected_queries: int = Field(description="Expected number of queries needed")
    reasoning: str = Field(description="Why this strategy was chosen")

def analyze_ttl_file(ttl_file_path: str) -> str:
    """Comprehensively analyze TTL file and provide detailed structure information"""
    try:
        g = Graph()
        g.parse(ttl_file_path, format='turtle')
        
        # Get basic statistics
        total_triples = len(g)
        
        # Get unique classes
        classes_query = """
        SELECT DISTINCT ?class WHERE {
            ?s a ?class .
        }
        """
        classes = [str(row[0]) for row in g.query(classes_query)]
        
        # Get unique properties
        props_query = """
        SELECT DISTINCT ?prop WHERE {
            ?s ?prop ?o .
            FILTER(?prop != <http://www.w3.org/1999/02/22-rdf-syntax-ns#type>)
        }
        """
        properties = [str(row[0]) for row in g.query(props_query)]
        
        # Get prefixes and their namespaces
        prefixes = dict(g.namespaces())
        
        # Organize properties by namespace for better understanding
        property_by_namespace = {}
        class_by_namespace = {}
        
        for prop in properties:
            for prefix, namespace in prefixes.items():
                if prop.startswith(str(namespace)):
                    if prefix not in property_by_namespace:
                        property_by_namespace[prefix] = []
                    local_name = prop.replace(str(namespace), '')
                    property_by_namespace[prefix].append(f"{prefix}:{local_name}")
                    break
        
        for cls in classes:
            for prefix, namespace in prefixes.items():
                if cls.startswith(str(namespace)):
                    if prefix not in class_by_namespace:
                        class_by_namespace[prefix] = []
                    local_name = cls.replace(str(namespace), '')
                    class_by_namespace[prefix].append(f"{prefix}:{local_name}")
                    break
        
        # Get sample data for key properties
        sample_data = {}
        for prop in properties[:10]:  # Sample first 10 properties
            sample_query = f"""
            SELECT DISTINCT ?value WHERE {{
                ?s <{prop}> ?value .
            }} LIMIT 5
            """
            try:
                values = [str(row[0]) for row in g.query(sample_query)]
                if values:
                    prop_local = prop
                    for prefix, namespace in prefixes.items():
                        if prop.startswith(str(namespace)):
                            prop_local = f"{prefix}:{prop.replace(str(namespace), '')}"
                            break
                    sample_data[prop_local] = values[:3]
            except:
                pass
        
        # Get instance count per class
        class_counts = {}
        for cls in classes:
            count_query = f"""
            SELECT (COUNT(?s) as ?count) WHERE {{
                ?s a <{cls}> .
            }}
            """
            try:
                result = list(g.query(count_query))
                if result:
                    cls_local = cls
                    for prefix, namespace in prefixes.items():
                        if cls.startswith(str(namespace)):
                            cls_local = f"{prefix}:{cls.replace(str(namespace), '')}"
                            break
                    class_counts[cls_local] = int(result[0][0])
            except:
                pass

        analysis = f"""COMPREHENSIVE TTL FILE ANALYSIS:

=== BASIC STATISTICS ===
- Total triples: {total_triples}
- Classes found: {len(classes)}
- Properties found: {len(properties)}
- Prefixes defined: {len(prefixes)}

=== PREFIX DEFINITIONS ===
{chr(10).join([f"@prefix {prefix}: <{namespace}> ." for prefix, namespace in list(prefixes.items())])}

=== CLASSES BY NAMESPACE ===
{chr(10).join([f"{prefix} namespace classes: {', '.join(classes)}" for prefix, classes in class_by_namespace.items()])}

=== PROPERTIES BY NAMESPACE ===
{chr(10).join([f"{prefix} namespace properties: {', '.join(props[:10])}" + ("..." if len(props) > 10 else "") for prefix, props in property_by_namespace.items()])}

=== CLASS INSTANCE COUNTS ===
{chr(10).join([f"- {cls}: {count} instances" for cls, count in class_counts.items()])}

=== SAMPLE DATA VALUES ===
{chr(10).join([f"- {prop}: {', '.join(values)}" for prop, values in sample_data.items()])}

=== CRITICAL NAMESPACE MAPPING RULES ===
- For SPARQL queries, use the EXACT prefix definitions shown above
- Properties are typically in the "prop" namespace: prop:property-name  
- Classes are typically in the "ibpdi" namespace: ibpdi:ClassName
- Always check which namespace a property belongs to before writing queries

=== QUERY CONSTRUCTION GUIDANCE ===
- Use PREFIX declarations exactly as shown in the prefix definitions
- Match property and class names with their correct namespaces
- Pay attention to the sample data values for exact string matching
- Remember that string values are case-sensitive
"""
        return analysis
    except Exception as e:
        return f"Error analyzing TTL file: {e}"

def validate_sparql_query(query: str) -> str:
    """Validate SPARQL syntax and return query type."""
    if not query or not isinstance(query, str):
        raise ValueError("Query must be a non-empty string")
    
    try:
        prepareQuery(query)
        query_upper = query.upper().strip()
        if 'SELECT' in query_upper:
            return 'SELECT'
        elif 'ASK' in query_upper:
            return 'ASK'
        elif 'CONSTRUCT' in query_upper:
            return 'CONSTRUCT'
        elif 'DESCRIBE' in query_upper:
            return 'DESCRIBE'
        else:
            return 'UNKNOWN'
    except Exception as e:
        raise ValueError(f"Invalid SPARQL syntax: {e}")

# SPARQL Query Generation Agent
sparql_agent = Agent[str, SPARQLOutput](
    model,
    deps_type=str,
    output_type=SPARQLOutput,
    system_prompt="""You are an expert SPARQL query generator. 
    
    Your task is to:
    1. CAREFULLY analyze the comprehensive TTL file structure provided
    2. Understand the user's natural language question
    3. Generate a syntactically correct SPARQL query that answers the question
    4. Use the EXACT prefixes, classes, and properties from the TTL analysis
    
    CRITICAL Namespace Usage Rules:
    - ALWAYS use the exact PREFIX definitions provided in the TTL analysis
    - Properties are usually in a specific property namespace (e.g., prop:property-name)
    - Classes are usually in a specific class namespace (e.g., ibpdi:ClassName)
    - Study the "PROPERTIES BY NAMESPACE" section carefully to use correct prefixes
    - Study the "CLASSES BY NAMESPACE" section to identify the right class prefixes
    - Use the "SAMPLE DATA VALUES" to understand exact string formats and casing
    
    Query Construction Guidelines:
    - Always use proper PREFIX declarations from the TTL analysis
    - Make queries as specific as possible to the user's question
    - For counting questions, use COUNT(*) or COUNT(?variable)
    - For listing questions, use SELECT with appropriate variables
    - For existence questions, consider using ASK queries
    - NEVER add LIMIT clauses unless explicitly requested by the user
    - Always return complete results without artificial limitations
    - When filtering by string values, use EXACT case matching from sample data
    - If string matching fails, suggest case-insensitive approaches using FILTER with LCASE() or regex
    - Return a QAError if the question cannot be answered with the available data
    
    IMPORTANT: Pay special attention to which namespace each property belongs to!
    Don't assume - check the TTL analysis carefully.
    """
)

@sparql_agent.output_validator
async def validate_sparql_output(ctx: RunContext[str], output: SPARQLOutput) -> SPARQLOutput:
    if isinstance(output, QAError):
        return output
    
    try:
        query_type = validate_sparql_query(output.query)
        output.query_type = query_type
        output.validation_passed = True
        return output
    except ValueError as e:
        raise ModelRetry(f'SPARQL validation failed: {e}')

# Create the intelligent TTL QA Agent
ttl_qa_agent = Agent[TTLContext, FinalAnswer](
    model,
    deps_type=TTLContext,
    output_type=FinalAnswer,
    system_prompt="""You are an intelligent TTL (Turtle RDF) question-answering agent with advanced reasoning capabilities.

Your role is to help users understand and query TTL/RDF data using natural language questions. You have access to several tools:

1. develop_strategy: Plan your approach for complex questions
2. generate_sparql_query: Create SPARQL queries to extract data
3. execute_sparql_query: Run queries against the TTL data
4. analyze_query_results: Extract insights from query results

CRITICAL: The TTL file structure and analysis is provided in your context dependencies. ALWAYS refer to the complete analysis to understand:
- Available classes and their counts
- Available properties and their usage
- Sample data values for accurate filtering
- Exact prefix definitions for namespace usage
- Relationships between entities

METHODOLOGY:
For each user question, you should:
1. First examine the TTL analysis to understand what data is available
2. Develop a strategy using develop_strategy tool based on available data
3. Generate appropriate SPARQL queries using generate_sparql_query with full context
4. Execute queries using execute_sparql_query
5. Analyze results using analyze_query_results
6. If needed, generate follow-up queries based on initial results and available data
7. Finally, synthesize all findings into a comprehensive answer

IMPORTANT RULES:
- ALWAYS consult the TTL analysis before generating queries
- Use exact prefix definitions and property names from the analysis
- Refer to sample data values for accurate string matching
- Consider class instance counts to provide context
- If a question asks about data that doesn't exist, explain what IS available
- Be thorough in your analysis and provide clear explanations
- Show your reasoning process throughout

You must return a FinalAnswer with your complete response."""
)

@ttl_qa_agent.tool
async def get_ttl_analysis(
    ctx: RunContext[TTLContext]
) -> str:
    """Get the complete TTL file analysis to understand available data structure, classes, properties, and sample values.
    
    Returns:
        Complete analysis of the TTL file including prefixes, classes, properties, counts, and sample data
    """
    return ctx.deps.analysis

@ttl_qa_agent.tool
async def generate_sparql_query(
    ctx: RunContext[TTLContext], 
    question: str, 
    strategy_context: Optional[str] = None
) -> SPARQLQuery:
    """Generate a SPARQL query to answer a specific question about the TTL data.
    
    Uses an AI-powered SPARQL agent to generate intelligent, context-aware queries.
    
    Args:
        question: The specific question to answer
        strategy_context: Additional context from the overall strategy
    """
    try:
        # Get the complete TTL analysis to provide to the SPARQL agent
        analysis = ctx.deps.analysis
        
        # Prepare the prompt for the SPARQL agent
        context_prompt = f"""Generate a SPARQL query to answer this question: "{question}"

Context from strategy: {strategy_context if strategy_context else "No additional strategy context"}

TTL File Analysis:
{analysis}

Please generate a SPARQL query that:
1. Uses the exact prefixes and namespaces from the analysis
2. Targets the appropriate classes and properties for this question
3. Returns the most relevant data to answer the question
4. Is syntactically correct and executable

Focus on the question's intent and use the available data structure intelligently."""

        # Use the SPARQL agent to generate the query
        result = await sparql_agent.run(context_prompt, deps=analysis)
        
        if isinstance(result.output, QAError):
            # Convert QAError to SPARQLQuery with error information
            return SPARQLQuery(
                query=f"# Error generating query: {result.output.error_message}",
                query_type="ERROR",
                description=f"Failed to generate query: {result.output.error_message}. {result.output.suggestions}",
                confidence=0.1,
                validation_passed=False
            )
        
        # Return the successfully generated SPARQL query
        return result.output
        
    except Exception as e:
        raise ModelRetry(f"Failed to generate SPARQL query using AI agent: {e}")

@ttl_qa_agent.tool
async def execute_sparql_query(
    ctx: RunContext[TTLContext], 
    sparql_query: str
) -> QueryResult:
    """Execute a SPARQL query against the loaded TTL graph.
    
    Args:
        sparql_query: The SPARQL query to execute
    """
    
    try:
        start_time = time.time()
        
        # Execute the query
        results = list(ctx.deps.graph.query(sparql_query))
        
        execution_time = time.time() - start_time
        
        # Convert results to dictionaries
        data = []
        columns = []
        
        if results:
            # Get column names from the first result
            first_result = results[0]
            if hasattr(first_result, '_fields'):
                columns = list(first_result._fields)
            else:
                columns = [f"col_{i}" for i in range(len(first_result))]
            
            # Convert all results to dictionaries
            for result in results:
                if hasattr(result, '_asdict'):
                    data.append(result._asdict())
                else:
                    row_dict = {}
                    for i, value in enumerate(result):
                        row_dict[columns[i]] = str(value) if value else None
                    data.append(row_dict)
        
        return QueryResult(
            success=True,
            data=data,
            row_count=len(results),
            columns=columns,
            execution_time=execution_time
        )
        
    except Exception as e:
        return QueryResult(
            success=False,
            error_message=str(e),
            execution_time=0.0
        )

@ttl_qa_agent.tool
async def analyze_query_results(
    ctx: RunContext[TTLContext], 
    query_result: QueryResult,
    original_question: str
) -> AnalysisInsight:
    """Analyze query results to extract insights relevant to the original question.
    
    Args:
        query_result: The result from executing a SPARQL query
        original_question: The original user question for context
    """
    if not query_result.success:
        return AnalysisInsight(
            insight=f"Query failed: {query_result.error_message}",
            confidence=0.9,
            supporting_data="Query execution error - this may indicate the data structure is different than expected"
        )
    
    if query_result.row_count == 0:
        # Get analysis to provide context about what data IS available
        analysis = ctx.deps.analysis
        available_entities = []
        lines = analysis.split('\n')
        
        for line in lines:
            if line.strip().startswith('-') and 'instances' in line and ':' in line:
                class_name = line.split(':')[0].replace('-', '').strip()
                available_entities.append(class_name)
        
        context = f"Available entity types: {', '.join(available_entities[:5])}" if available_entities else "No entity information available"
        
        return AnalysisInsight(
            insight="No data found matching the query criteria",
            confidence=0.8,
            supporting_data=f"Empty result set. {context}"
        )
    
    # Get analysis context for better insight
    analysis = ctx.deps.analysis
    total_entities = 0
    available_classes = []
    
    lines = analysis.split('\n')
    for line in lines:
        if line.strip().startswith('-') and 'instances' in line and ':' in line:
            parts = line.split(':')
            class_name = parts[0].replace('-', '').strip()
            available_classes.append(class_name)
            try:
                count = int(parts[1].split()[0].strip())
                total_entities += count
            except:
                pass
    
    # Enhanced analysis based on result structure and context
    insight = f"Found {query_result.row_count} results"
    if query_result.columns:
        insight += f" with columns: {', '.join(query_result.columns)}"
    
    # Add context about dataset coverage if it's a count query
    question_lower = original_question.lower()
    if any(word in question_lower for word in ["how many", "count", "number"]) and query_result.row_count == 1:
        # This is likely a count query result
        if query_result.data and query_result.data[0]:
            count_value = list(query_result.data[0].values())[0]
            if str(count_value).isdigit() and total_entities > 0:
                percentage = (int(count_value) / total_entities) * 100
                insight += f" (represents {percentage:.1f}% of total {total_entities} entities in dataset)"
    
    # Add coverage context for non-count queries
    elif total_entities > 0:
        coverage = (query_result.row_count / total_entities) * 100
        insight += f" (covering {coverage:.1f}% of dataset with {total_entities} total entities)"
    
    # Enhanced sample data with context
    sample_data = f"Sample results from {len(available_classes)} entity types available: "
    for i, row in enumerate(query_result.data[:3]):  # Show first 3 rows
        sample_data += f"Row {i+1}: {row}; "
    
    # Determine confidence based on result quality and context
    confidence = 0.7  # Default
    if query_result.row_count > 0:
        confidence = 0.9  # Good results
        if "count" in question_lower and query_result.row_count == 1:
            confidence = 0.95  # Count queries are typically very accurate
        elif query_result.row_count < 5 and "list" in question_lower:
            confidence = 0.75  # Might need more exploration for list queries
    
    return AnalysisInsight(
        insight=insight,
        confidence=confidence,
        supporting_data=sample_data.strip()
    )

@ttl_qa_agent.tool
async def develop_strategy(
    ctx: RunContext[TTLContext], 
    user_question: str
) -> AgentStrategy:
    """Develop a strategic approach for answering a complex question.
    
    Args:
        user_question: The user's natural language question
    """
    # Get analysis to understand available data
    analysis = ctx.deps.analysis
    
    # Parse available classes and properties
    available_classes = []
    available_properties = []
    class_counts = {}
    
    lines = analysis.split('\n')
    current_section = None
    
    for line in lines:
        line = line.strip()
        if '=== CLASSES BY NAMESPACE ===' in line:
            current_section = 'classes'
        elif '=== PROPERTIES BY NAMESPACE ===' in line:
            current_section = 'properties'
        elif '=== CLASS INSTANCE COUNTS ===' in line:
            current_section = 'counts'
        elif current_section == 'counts' and line.startswith('-'):
            if ':' in line and 'instances' in line:
                parts = line.split(':')
                if len(parts) >= 2:
                    class_name = parts[0].replace('-', '').strip()
                    count_part = parts[1].split()[0].strip()
                    try:
                        class_counts[class_name] = int(count_part)
                        available_classes.append(class_name)
                    except:
                        pass
        elif current_section == 'classes' and 'classes:' in line:
            # Extract class names
            classes_part = line.split('classes:')[1]
            for cls in classes_part.split(','):
                cls = cls.strip()
                if cls and cls not in available_classes:
                    available_classes.append(cls)
        elif current_section == 'properties' and 'properties:' in line:
            # Extract property names  
            props_part = line.split('properties:')[1]
            for prop in props_part.split(','):
                prop = prop.strip()
                if prop and prop not in available_properties:
                    available_properties.append(prop)
    
    question_lower = user_question.lower()
    
    # Enhanced strategy based on available data
    if any(word in question_lower for word in ["how many", "count", "number"]):
        # Check what's being counted exists
        target_entity = None
        expected_count = "unknown"
        
        if "building" in question_lower:
            building_classes = [cls for cls in available_classes if 'building' in cls.lower()]
            if building_classes:
                target_entity = building_classes[0]
                expected_count = class_counts.get(target_entity, "unknown")
            
        elif "address" in question_lower:
            address_classes = [cls for cls in available_classes if 'address' in cls.lower()]
            if address_classes:
                target_entity = address_classes[0]
                expected_count = class_counts.get(target_entity, "unknown")
        
        return AgentStrategy(
            approach="Count-based analysis",
            steps=[
                f"Check if target entity exists in data (found: {target_entity})" if target_entity else "Explore available entity types",
                f"Generate COUNT query for {target_entity}" if target_entity else "Generate general count query",
                f"Execute query (expected result: ~{expected_count})" if expected_count != "unknown" else "Execute query and analyze results",
                "Provide interpretation of count with context from available data"
            ],
            expected_queries=1,
            reasoning=f"Question asks for quantitative info. Available data shows: {len(available_classes)} entity types with {sum(class_counts.values()) if class_counts else '?'} total instances"
        )
    
    elif any(word in question_lower for word in ["what", "which", "list", "show"]):
        relevant_properties = []
        relevant_classes = []
        
        # Find relevant entities based on question
        for word in ["building", "address", "city", "type", "parking"]:
            if word in question_lower:
                # Find related classes
                related_classes = [cls for cls in available_classes if word in cls.lower()]
                relevant_classes.extend(related_classes)
                
                # Find related properties
                related_props = [prop for prop in available_properties if word in prop.lower()]
                relevant_properties.extend(related_props)
        
        return AgentStrategy(
            approach="Exploratory data retrieval",
            steps=[
                f"Identify relevant entities (found: {', '.join(relevant_classes[:3])})" if relevant_classes else "Explore available entity types",
                f"Check for relevant properties (found: {', '.join(relevant_properties[:3])})" if relevant_properties else "Discover available properties",
                "Generate initial exploratory query based on available data",
                "Execute and analyze results",
                "Generate follow-up queries if needed for complete information",
                "Synthesize findings with context about data availability"
            ],
            expected_queries=2,
            reasoning=f"Descriptive question. Found {len(relevant_classes)} relevant entity types and {len(relevant_properties)} relevant properties in the data"
        )
    
    elif any(word in question_lower for word in ["compare", "difference", "versus"]):
        # Identify what can be compared
        comparable_entities = []
        comparison_properties = []
        
        for cls in available_classes:
            if any(word in question_lower for word in cls.lower().split(':')):
                comparable_entities.append(cls)
        
        # Look for properties that enable comparison
        for prop in available_properties:
            if any(word in prop.lower() for word in ["type", "category", "class", "kind"]):
                comparison_properties.append(prop)
        
        return AgentStrategy(
            approach="Comparative analysis", 
            steps=[
                f"Identify entities to compare (available: {', '.join(comparable_entities[:3])})" if comparable_entities else "Explore what entities exist for comparison",
                f"Find comparison criteria (properties: {', '.join(comparison_properties[:3])})" if comparison_properties else "Identify properties that enable comparison",
                "Generate queries for each comparison target",
                "Execute comparative queries",
                "Analyze differences, similarities, and distributions",
                "Provide comparative insights with data context"
            ],
            expected_queries=3,
            reasoning=f"Comparative question. Available for comparison: {len(comparable_entities)} entity types, {len(comparison_properties)} comparison properties"
        )
    
    else:
        return AgentStrategy(
            approach="General inquiry with data-aware exploration",
            steps=[
                f"Analyze available data structure ({len(available_classes)} entity types, {len(available_properties)} properties)",
                "Generate broad exploratory query based on question context",
                "Analyze initial results to identify relevant data patterns",
                "Generate targeted follow-up queries for specific aspects",
                "Synthesize comprehensive answer with full context"
            ],
            expected_queries=3,
            reasoning=f"General question requiring flexible approach. Dataset contains: {', '.join(available_classes[:3])} with {sum(class_counts.values()) if class_counts else '?'} total entities"
        )

class AgenticTTLQACLI:
    """Agentic TTL Question-Answering CLI Application"""
    
    def __init__(self):
        self.console = Console()
        self.current_context: Optional[TTLContext] = None
        
    def print_header(self):
        """Print beautiful app header"""
        header_text = """
🤖 Agentic TTL Question-Answering System
Intelligent Multi-Agent Approach for RDF Data Analysis
        """
        
        header_panel = Panel(
            Align.center(header_text),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def print_status(self):
        """Print current system status"""
        if self.current_context:
            status_table = Table(show_header=False, box=box.MINIMAL)
            status_table.add_column("Item", style="cyan")
            status_table.add_column("Value", style="green")
            
            status_table.add_row("📁 Current File", Path(self.current_context.file_path).name)
            status_table.add_row("🔢 Total Triples", str(len(self.current_context.graph)))
            
            status_panel = Panel(
                status_table,
                title="📊 System Status",
                border_style="green",
                padding=(0, 1)
            )
            
            self.console.print(status_panel)
        else:
            self.console.print(Panel(
                "[yellow]No TTL file loaded. Use 'load' command to load a file.[/yellow]",
                title="⚠️ Status",
                border_style="yellow"
            ))
    
    def load_ttl_file(self) -> bool:
        """Load a TTL file and create context"""
        self.console.print("\n[bold cyan]Loading TTL File[/bold cyan]")
        
        default_path = r"C:\Users\<USER>\Documents\BA\.phases\phase5_ttl_sparql_agentic\assets\example.ttl"
        
        file_path = Prompt.ask(
            "[cyan]Enter TTL file path[/cyan]",
            default=default_path,
            console=self.console
        )
        
        if not Path(file_path).exists():
            self.console.print(f"[red]❌ File not found: {file_path}[/red]")
            return False
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Loading TTL file...", total=None)
                
                # Load and analyze the file
                graph = Graph()
                graph.parse(file_path, format='turtle')
                
                progress.update(task, description="Analyzing TTL structure...")
                analysis = analyze_ttl_file(file_path)
                
                self.current_context = TTLContext(
                    graph=graph,
                    file_path=file_path,
                    analysis=analysis
                )
                
                progress.update(task, description="✅ TTL file loaded successfully!")
            
            self.console.print(f"[green]✅ Successfully loaded TTL file with {len(graph)} triples[/green]")
            return True
            
        except Exception as e:
            self.console.print(f"[red]❌ Error loading TTL file: {e}[/red]")
            return False
    
    def display_tool_call(self, tool_name: str, args: Dict[str, Any], result: Any):
        """Display tool call information"""
        # Create tool call panel
        call_info = f"🔧 **Tool:** `{tool_name}`\n"
        if args:
            call_info += f"**Arguments:** {json.dumps(args, indent=2)}\n"
        
        tool_panel = Panel(
            Markdown(call_info),
            title="🛠️ Tool Call",
            border_style="blue",
            expand=False
        )
        self.console.print(tool_panel)
        
        # Display result based on type
        if isinstance(result, SPARQLQuery):
            self.display_sparql_query(result)
        elif isinstance(result, QueryResult):
            self.display_query_result(result)
        elif isinstance(result, AgentStrategy):
            self.display_strategy(result)
        elif isinstance(result, AnalysisInsight):
            self.display_insight(result)
        else:
            result_panel = Panel(
                str(result),
                title="🔄 Tool Result", 
                border_style="green"
            )
            self.console.print(result_panel)
    
    def display_sparql_query(self, query: SPARQLQuery):
        """Display a SPARQL query with syntax highlighting"""
        query_syntax = Syntax(
            query.query,
            "sparql",
            theme="monokai",
            line_numbers=True,
            word_wrap=True
        )
        
        query_info = f"""**Type:** {query.query_type}
**Description:** {query.description}
**Confidence:** {query.confidence:.1%}
**Validation:** {'✅ Passed' if query.validation_passed else '❌ Failed'}"""
        
        query_panel = Panel(
            Markdown(query_info) + "\n" + query_syntax,
            title="🔍 Generated SPARQL Query",
            border_style="cyan"
        )
        self.console.print(query_panel)
    
    def display_query_result(self, result: QueryResult):
        """Display query execution results"""
        if result.success:
            result_info = f"""**Status:** ✅ Success
**Rows:** {result.row_count}
**Execution Time:** {result.execution_time:.3f}s
**Columns:** {', '.join(result.columns) if result.columns else 'None'}"""
            
            content = Markdown(result_info)
            
            # Show sample data if available
            if result.data and len(result.data) > 0:
                table = Table(show_header=True, header_style="bold magenta")
                
                # Add columns
                if result.columns:
                    for col in result.columns:
                        table.add_column(col)
                else:
                    for i in range(len(result.data[0])):
                        table.add_column(f"col_{i}")
                
                # Add rows (limit to first 5)
                for row in result.data[:5]:
                    if isinstance(row, dict):
                        table.add_row(*[str(v) if v else "" for v in row.values()])
                    else:
                        table.add_row(*[str(v) if v else "" for v in row])
                
                if len(result.data) > 5:
                    table.add_row(*["..." for _ in range(len(result.columns or result.data[0]))])
                
                content = content + "\n" + table
        else:
            result_info = f"""**Status:** ❌ Failed
**Error:** {result.error_message}"""
            content = Markdown(result_info)
        
        result_panel = Panel(
            content,
            title="📊 Query Results",
            border_style="green" if result.success else "red"
        )
        self.console.print(result_panel)
    
    def display_strategy(self, strategy: AgentStrategy):
        """Display agent strategy"""
        strategy_info = f"""**Approach:** {strategy.approach}
**Expected Queries:** {strategy.expected_queries}
**Reasoning:** {strategy.reasoning}

**Planned Steps:**"""
        
        for i, step in enumerate(strategy.steps, 1):
            strategy_info += f"\n{i}. {step}"
        
        strategy_panel = Panel(
            Markdown(strategy_info),
            title="🎯 Agent Strategy",
            border_style="magenta"
        )
        self.console.print(strategy_panel)
    
    def display_insight(self, insight: AnalysisInsight):
        """Display analysis insight"""
        insight_info = f"""**Insight:** {insight.insight}
**Confidence:** {insight.confidence:.1%}
**Supporting Data:** {insight.supporting_data}"""
        
        insight_panel = Panel(
            Markdown(insight_info),
            title="💡 Analysis Insight",
            border_style="yellow"
        )
        self.console.print(insight_panel)
    
    def format_tool_args(self, tool_name: str, args) -> str:
        """Format tool arguments nicely for display"""
        try:
            if hasattr(args, 'items'):
                args_dict = dict(args)
            else:
                args_dict = args
                
            if tool_name == "execute_sparql_query" and 'sparql_query' in args_dict:
                # Format SPARQL query nicely
                query = args_dict['sparql_query']
                return f"**SPARQL Query:**\n```sparql\n{query}\n```"
            else:
                # Format other arguments as JSON
                return f"**Args:**\n```json\n{json.dumps(args_dict, indent=2)}\n```"
        except:
            return f"**Args:** {str(args)}"
    
    def format_tool_response(self, response) -> str:
        """Format tool response nicely for display"""
        try:
            # Handle QueryResult objects directly if they're passed as objects
            if hasattr(response, 'success') and hasattr(response, 'data'):
                return self.format_query_result_response(response)
            
            # Handle QueryResult objects as strings
            if isinstance(response, str):
                try:
                    # Try to parse as JSON if it looks like a QueryResult
                    import re
                    if 'QueryResult' in response or ('success' in response and 'data' in response):
                        return self.parse_query_result_string(response)
                except:
                    pass
            
            # For other responses, format nicely
            response_str = str(response)
            if len(response_str) > 1000:
                return f"```\n{response_str[:1000]}...\n```"
            else:
                return f"```\n{response_str}\n```"
                
        except Exception as e:
            return f"**Response:** {str(response)}"
    
    def format_query_result_response(self, result) -> str:
        """Format a QueryResult object for display"""
        if result.success:
            response_text = f"**Status:** ✅ Success\n**Rows:** {result.row_count}\n**Time:** {result.execution_time:.3f}s"
            
            if result.data and len(result.data) > 0:
                response_text += "\n\n**Results:**\n"
                
                # Create a table for the results
                table = Table(box=box.MINIMAL_DOUBLE_HEAD, show_header=True, header_style="bold cyan")
                
                # Add columns
                if result.columns:
                    for col in result.columns:
                        table.add_column(col, overflow="fold", max_width=30)
                elif result.data and len(result.data) > 0:
                    for i in range(len(result.data[0])):
                        table.add_column(f"col_{i}", overflow="fold", max_width=30)
                
                # Add rows (limit to first 10 for display)
                display_count = min(10, len(result.data))
                for i, row in enumerate(result.data[:display_count]):
                    if isinstance(row, dict):
                        table.add_row(*[str(v)[:100] if v else "" for v in row.values()])
                    else:
                        table.add_row(*[str(v)[:100] if v else "" for v in row])
                
                if len(result.data) > display_count:
                    cols_count = len(result.columns) if result.columns else len(result.data[0])
                    table.add_row(*[f"... (+{len(result.data) - display_count} more rows)" if i == 0 else "" for i in range(cols_count)])
                
                # Convert table to string (this is a bit tricky with Rich)
                from io import StringIO
                import contextlib
                
                temp_console = Console(file=StringIO(), width=120)
                temp_console.print(table)
                table_str = temp_console.file.getvalue()
                
                return response_text + "\n" + table_str
            else:
                return response_text + "\n\n*No results returned*"
        else:
            return f"**Status:** ❌ Failed\n**Error:** {result.error_message}"
    
    def parse_query_result_string(self, response: str) -> str:
        """Parse a QueryResult string representation"""
        import re
        if 'success=True' in response:
            row_count_match = re.search(r'row_count=(\d+)', response)
            execution_time_match = re.search(r'execution_time=([\d.]+)', response)
            
            result_text = "**Status:** ✅ Success\n"
            
            if row_count_match:
                result_text += f"**Rows:** {row_count_match.group(1)}\n"
            
            if execution_time_match:
                exec_time = float(execution_time_match.group(1))
                result_text += f"**Time:** {exec_time:.3f}s\n"
            
            # Try to extract and format data
            data_match = re.search(r'data=\[(.*?)\]', response, re.DOTALL)
            if data_match and row_count_match and int(row_count_match.group(1)) > 0:
                result_text += "\n**Sample Results:**\n```\n"
                data_str = data_match.group(1)
                if data_str:
                    # Clean up the data representation
                    formatted_data = data_str.replace("{'", "\n  {'")[:1000]
                    result_text += formatted_data
                    if len(data_str) > 1000:
                        result_text += "\n  ..."
                result_text += "\n```"
            
            return result_text
        else:
            error_match = re.search(r"error_message='([^']*)'", response)
            if error_match:
                return f"**Status:** ❌ Failed\n**Error:** {error_match.group(1)}"
        
        return f"```\n{response}\n```"
    
    def format_tool_response_by_name(self, tool_name: str, response) -> str:
        """Format tool response based on the tool name"""
        if tool_name == "execute_sparql_query":
            # Handle QueryResult objects specifically
            if hasattr(response, 'success') and hasattr(response, 'data'):
                return self.format_query_result_response(response)
            else:
                return self.format_tool_response(response)
        else:
            return self.format_tool_response(response)
    
    async def ask_question(self, question: str):
        """Process a user question using the agentic approach"""
        if not self.current_context:
            self.console.print("[red]❌ No TTL file loaded. Please load a file first.[/red]")
            return
        
        self.console.print(Panel(
            f"[bold cyan]Question:[/bold cyan] {question}",
            title="❓ User Query",
            border_style="blue"
        ))
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console,
                transient=False
            ) as progress:
                task = progress.add_task("🤖 Agent processing...", total=None)
                
                # Run the agent - it will use tools as needed
                result = await ttl_qa_agent.run(
                    f"Please answer this question about the TTL data: {question}",
                    deps=self.current_context
                )
                
                progress.update(task, description="✅ Agent completed!", completed=True)
            
            # Show the conversation messages to see tool calls
            messages = result.all_messages()
            
            # Display tool calls and their responses
            tool_call_count = 0
            current_tool_name = None
            
            for msg in messages:
                if hasattr(msg, 'parts'):
                    for part in msg.parts:
                        # Handle tool calls
                        if hasattr(part, 'tool_name') and hasattr(part, 'args'):
                            tool_call_count += 1
                            current_tool_name = part.tool_name
                            
                            # Format tool arguments nicely
                            args_formatted = self.format_tool_args(part.tool_name, part.args)
                            
                            self.console.print(Panel(
                                args_formatted,
                                title=f"🛠️ Tool Call #{tool_call_count}: {part.tool_name}",
                                border_style="blue",
                                padding=(0, 1)
                            ))
                        
                        # Handle tool returns (results)
                        elif hasattr(part, 'tool_return') and current_tool_name:
                            response_formatted = self.format_tool_response_by_name(current_tool_name, part.tool_return)
                            
                            self.console.print(Panel(
                                response_formatted,
                                title=f"📊 Tool Response #{tool_call_count}: {current_tool_name}",
                                border_style="green",
                                padding=(0, 1)
                            ))
                            current_tool_name = None
                        
                        # Fallback for content-based tool responses
                        elif hasattr(part, 'content') and tool_call_count > 0 and current_tool_name:
                            response_formatted = self.format_tool_response_by_name(current_tool_name, part.content)
                            
                            self.console.print(Panel(
                                response_formatted,
                                title=f"📊 Tool Response #{tool_call_count}: {current_tool_name}",
                                border_style="green",
                                padding=(0, 1)
                            ))
                            current_tool_name = None
            
            # Display final result
            if isinstance(result.output, FinalAnswer):
                self.display_final_answer(result.output)
            else:
                # Display whatever the agent returned
                self.console.print(Panel(
                    str(result.output),
                    title="🤖 Agent Response",
                    border_style="green"
                ))
            
            # Show usage information
            usage = result.usage()
            if usage:
                usage_info = f"**Requests:** {usage.requests} | **Tokens:** {usage.total_tokens}"
                self.console.print(Panel(
                    Markdown(usage_info),
                    title="📈 Usage Stats",
                    border_style="dim"
                ))
                
        except Exception as e:
            self.console.print(f"[red]❌ Error processing question: {e}[/red]")
    
    def display_final_answer(self, answer: FinalAnswer):
        """Display the final answer from the agent"""
        answer_info = f"""**Answer:** {answer.answer}

**Confidence:** {answer.confidence:.1%}

**Methodology:** {answer.methodology}"""
        
        if answer.queries_used:
            answer_info += f"\n\n**Queries Used:**"
            for i, query in enumerate(answer.queries_used, 1):
                answer_info += f"\n{i}. `{query[:100]}...`" if len(query) > 100 else f"\n{i}. `{query}`"
        
        if answer.insights:
            answer_info += f"\n\n**Key Insights:**"
            for insight in answer.insights:
                answer_info += f"\n• {insight}"
        
        final_panel = Panel(
            Markdown(answer_info),
            title="🎯 Final Answer",
            border_style="bright_green",
            padding=(1, 2)
        )
        self.console.print(final_panel)
    
    async def run(self):
        """Main application loop"""
        self.print_header()
        
        # Load TTL file first
        if not self.load_ttl_file():
            return
        
        self.print_status()
        
        self.console.print("\n[bold green]🤖 Agentic TTL Q&A System Ready![/bold green]")
        self.console.print("Enter your questions in natural language. Type 'quit' or 'exit' to stop.\n")
        
        while True:
            try:
                question = Prompt.ask("\n[bold cyan]Your question[/bold cyan]")
                
                if question.lower() in ['quit', 'exit', 'q']:
                    self.console.print("[yellow]Goodbye! 👋[/yellow]")
                    break
                
                if question.lower() in ['load', 'reload']:
                    self.load_ttl_file()
                    continue
                
                if question.lower() in ['status', 'info']:
                    self.print_status()
                    continue
                
                if question.lower() in ['help', '?']:
                    help_text = """**Available Commands:**
• `load` or `reload` - Load a new TTL file
• `status` or `info` - Show system status  
• `help` or `?` - Show this help
• `quit`, `exit`, or `q` - Exit the application

**Example Questions:**
• "How many buildings are there?"
• "What types of addresses exist?"
• "Show me buildings with parking spaces"
• "Compare residential and commercial buildings"
• "What cities are represented in the data?"
"""
                    self.console.print(Panel(
                        Markdown(help_text),
                        title="❓ Help",
                        border_style="blue"
                    ))
                    continue
                
                # Process the question with the agent
                await self.ask_question(question)
                
            except KeyboardInterrupt:
                self.console.print("\n[yellow]Goodbye! 👋[/yellow]")
                break
            except Exception as e:
                self.console.print(f"[red]❌ Unexpected error: {e}[/red]")

async def main():
    """Main entry point"""
    app = AgenticTTLQACLI()
    await app.run()

if __name__ == "__main__":
    asyncio.run(main())
