#!/usr/bin/env python3
"""
Agentic TTL Q&A Demo Script
Demonstrates the agentic capabilities programmatically
"""

import os
import asyncio
from pathlib import Path

# Set up environment (you would set this in your actual environment)
os.environ["OPENROUTER_API_KEY"] = "your_api_key_here"  # Replace with actual key

from agentic_ttl_qa_cli import TTLContext, ttl_qa_agent, analyze_ttl_file
from rdflib import Graph

async def demo_agentic_qa():
    """Demonstrate the agentic Q&A capabilities"""
    
    print("🤖 Agentic TTL Q&A Demo")
    print("=" * 50)
    
    # Load the TTL file
    ttl_file = Path("assets/example.ttl")
    if not ttl_file.exists():
        print("❌ TTL file not found. Please ensure assets/example.ttl exists.")
        return
    
    # Create TTL context
    graph = Graph()
    graph.parse(str(ttl_file), format='turtle')
    analysis = analyze_ttl_file(str(ttl_file))
    
    context = TTLContext(
        graph=graph,
        file_path=str(ttl_file),
        analysis=analysis
    )
    
    print(f"✅ Loaded TTL file with {len(graph)} triples")
    print()
    
    # Test questions that demonstrate different agentic approaches
    test_questions = [
        {
            "question": "How many buildings are there?",
            "expected_strategy": "Count-based analysis"
        },
        {
            "question": "What types of buildings exist?", 
            "expected_strategy": "Exploratory data retrieval"
        },
        {
            "question": "Compare residential and commercial buildings",
            "expected_strategy": "Comparative analysis"
        }
    ]
    
    for i, test in enumerate(test_questions, 1):
        print(f"📋 Test {i}: {test['question']}")
        print(f"Expected Strategy: {test['expected_strategy']}")
        print("-" * 30)
        
        try:
            # Run the agent
            result = await ttl_qa_agent.run(
                f"Please answer this question about the TTL data: {test['question']}",
                deps=context
            )
            
            # Display results
            if hasattr(result.output, 'answer'):
                print(f"💬 Answer: {result.output.answer}")
                print(f"🔍 Methodology: {result.output.methodology}")
                print(f"📊 Confidence: {result.output.confidence:.1%}")
                
                if result.output.queries_used:
                    print(f"🔧 Queries Used: {len(result.output.queries_used)}")
                    for j, query in enumerate(result.output.queries_used, 1):
                        print(f"  {j}. {query[:100]}...")
                        
                if result.output.insights:
                    print(f"💡 Key Insights:")
                    for insight in result.output.insights:
                        print(f"  • {insight}")
            else:
                print(f"🤖 Response: {result.output}")
            
            # Show usage
            usage = result.usage()
            if usage:
                print(f"📈 Usage: {usage.requests} requests, {usage.total_tokens} tokens")
            
            print()
            
        except Exception as e:
            print(f"❌ Error: {e}")
            print()
    
    print("🎯 Demo completed! The agent demonstrated:")
    print("  • Strategic planning based on question types")
    print("  • Dynamic tool usage")  
    print("  • Multi-query capabilities")
    print("  • Iterative reasoning")
    print("  • Transparent methodology")

if __name__ == "__main__":
    asyncio.run(demo_agentic_qa())
