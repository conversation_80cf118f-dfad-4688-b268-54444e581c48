# Comparison: Pipeline vs Agentic Approach

This document compares the previous rigid pipeline approach with the new agentic approach for TTL question-answering.

## Architecture Comparison

| Aspect              | Pipeline Approach               | Agentic Approach               |
| ------------------- | ------------------------------- | ------------------------------ |
| **Decision Making** | Fixed sequence                  | Dynamic reasoning              |
| **Query Strategy**  | One query per question          | Variable queries based on need |
| **Error Handling**  | Pipeline breaks on error        | Agent can retry and adapt      |
| **Transparency**    | Hidden internal steps           | All tool calls visible         |
| **Flexibility**     | Same approach for all questions | Tailored strategy per question |
| **Learning**        | Static behavior                 | Can improve through iteration  |

## Workflow Comparison

### Pipeline Approach

```
User Question
    ↓
Parse Intent
    ↓
Generate SPARQL Query
    ↓
Execute Query
    ↓
Format Response
    ↓
Final Answer
```

**Limitations:**

- ❌ Cannot handle complex multi-step questions
- ❌ No ability to refine queries based on results
- ❌ One-size-fits-all approach
- ❌ Limited error recovery
- ❌ No visibility into reasoning process

### Agentic Approach

```
User Question
    ↓
Agent Analyzes Question
    ↓
Agent Develops Strategy ← Tool Call #1
    ↓
Agent Generates Query ← Tool Call #2
    ↓
Agent Executes Query ← Tool Call #3
    ↓
Agent Analyzes Results ← Tool Call #4
    ↓
[Agent Decides: Need More Data?]
    ↓ (Yes)
Agent Generates Follow-up Query ← Tool Call #5
    ↓
Agent Executes Follow-up ← Tool Call #6
    ↓
Agent Synthesizes Final Answer
    ↓
Comprehensive Response
```

**Benefits:**

- ✅ Handles complex multi-step analysis
- ✅ Iterative refinement based on intermediate results
- ✅ Strategy tailored to question type
- ✅ Robust error handling and recovery
- ✅ Complete transparency of reasoning process
- ✅ Can learn and improve approaches

## Example Question: "Compare residential and commercial buildings"

### Pipeline Approach Response

```
Generated Query:
SELECT ?building ?type WHERE {
    ?building a ibpdi:Building ;
              prop:building-type ?type .
}

Result: Basic list of buildings and types
Answer: "Here are the buildings and their types: ..."
```

**Limitations:** Single query provides raw data but no actual comparison or analysis.

### Agentic Approach Response

```
Strategy: Comparative Analysis
Expected Queries: 3

Tool Call #1: develop_strategy
- Approach: Multi-faceted comparison
- Steps: Get residential data → Get commercial data → Compare characteristics

Tool Call #2: generate_sparql_query (Residential)
SELECT ?building ?floors ?parking ?energy WHERE {
    ?building a ibpdi:Building ;
              prop:building-type "Residential" ;
              prop:floor-count ?floors ;
              prop:has-parking ?parking ;
              prop:energy-rating ?energy .
}

Tool Call #3: execute_sparql_query
Results: 2 residential buildings, avg 4 floors, 50% have parking, ratings A-C

Tool Call #4: analyze_query_results
Insight: Residential buildings tend to be mid-height with mixed parking availability

Tool Call #5: generate_sparql_query (Commercial)
SELECT ?building ?floors ?parking ?energy WHERE {
    ?building a ibpdi:Building ;
              prop:building-type "Commercial" ;
              prop:floor-count ?floors ;
              prop:has-parking ?parking ;
              prop:energy-rating ?energy .
}

Tool Call #6: execute_sparql_query
Results: 1 commercial building, 10 floors, has parking, rating B

Tool Call #7: analyze_query_results
Insight: Commercial buildings are taller and more likely to have parking

Final Answer: "Based on the analysis of 2 residential and 1 commercial building:

Key Differences:
• Height: Commercial buildings average 10 floors vs 4 for residential
• Parking: 100% of commercial vs 50% of residential have parking
• Energy Efficiency: Similar distribution across both types

The commercial building in the dataset represents a typical high-rise office
building with comprehensive amenities, while residential buildings show more
variation in size and amenities."

Methodology: Comparative analysis using 2 targeted queries
Confidence: 85%
Queries Used: 2 comparative SPARQL queries
```

## Technical Implementation Differences

### Pipeline Implementation

```python
def answer_question(question: str) -> str:
    sparql_query = generate_query(question)  # Single query
    result = execute_query(sparql_query)     # Execute once
    return format_response(result)           # Format and return
```

### Agentic Implementation

```python
# Agent decides which tools to use and when
@ttl_qa_agent.tool
async def develop_strategy(ctx, question) -> AgentStrategy: ...

@ttl_qa_agent.tool
async def generate_sparql_query(ctx, question, context) -> SPARQLQuery: ...

@ttl_qa_agent.tool
async def execute_sparql_query(ctx, query) -> QueryResult: ...

@ttl_qa_agent.tool
async def analyze_query_results(ctx, results, question) -> AnalysisInsight: ...

# Agent orchestrates tool usage dynamically
result = await ttl_qa_agent.run(question, deps=context)
```

## User Experience Comparison

### Pipeline UX

```
User: "Compare residential and commercial buildings"
System: [Processing...]
System: "Here are buildings by type: Building_001: Residential, Building_002: Commercial"
```

### Agentic UX

```
User: "Compare residential and commercial buildings"
System: 🎯 Strategy: Comparative Analysis (3 expected queries)
System: 🔍 Generated query for residential buildings
System: 📊 Found 2 residential buildings, analyzing...
System: 💡 Insight: Residential avg 4 floors, mixed parking
System: 🔍 Generated query for commercial buildings
System: 📊 Found 1 commercial building, analyzing...
System: 💡 Insight: Commercial 10 floors, has parking
System: 🎯 Final Answer: [Comprehensive comparison with methodology]
```

## Performance Comparison

| Metric                | Pipeline         | Agentic                    |
| --------------------- | ---------------- | -------------------------- |
| **Simple Questions**  | Fast (1 query)   | Similar (1-2 queries)      |
| **Complex Questions** | Limited quality  | High quality (2-5 queries) |
| **Error Recovery**    | Fails completely | Graceful retry/adaptation  |
| **User Insight**      | Minimal          | Complete transparency      |
| **Flexibility**       | None             | High adaptability          |

## Conclusion

The agentic approach transforms the TTL Q&A system from a basic query generator into an intelligent analytical assistant that can:

1. **Reason strategically** about how to approach different question types
2. **Execute multi-step analysis** with iterative refinement
3. **Provide transparency** into its decision-making process
4. **Adapt and recover** from errors or incomplete information
5. **Scale complexity** to match the sophistication of user questions

While the pipeline approach works for simple, single-query questions, the agentic approach excels at complex analytical tasks that require reasoning, comparison, and synthesis - exactly what users need for meaningful data exploration and insight generation.
