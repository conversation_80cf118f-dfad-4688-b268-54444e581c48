"""
Core Logic Module for TTL Question-Answering System
Contains all AI logic, data models, agents, and core functionality
"""

import os
import asyncio
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Set, Tuple, Callable
from dataclasses import dataclass, field
from collections import defaultdict
from functools import wraps
from datetime import datetime

# Load environment
import dotenv
dotenv.load_dotenv()

# Pydantic AI imports
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext, ModelRetry
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

# RDF and SPARQL handling
import rdflib
from rdflib import Graph, Namespace, URIRef, Literal
from rdflib.plugins.sparql import prepareQuery
from rdflib.exceptions import ParserError

# Set up API key
OR_API_KEY = os.getenv("OR_API_KEY")

model = OpenAIModel(
    'openai/gpt-4.1-mini',
    provider=OpenRouterProvider(api_key=OR_API_KEY)
)

# ===== TIMING TRACKING SYSTEM =====

@dataclass
class TimingEntry:
    """Individual timing entry for a step or operation"""
    name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    step_type: str = "operation"  # operation, tool_call, analysis, query
    details: Optional[str] = None
    sub_entries: List['TimingEntry'] = field(default_factory=list)

    def finish(self, details: Optional[str] = None):
        """Mark this timing entry as finished"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        if details:
            self.details = details

    def add_sub_entry(self, entry: 'TimingEntry'):
        """Add a sub-timing entry"""
        self.sub_entries.append(entry)

@dataclass
class TimingTracker:
    """Comprehensive timing tracker for all operations"""
    session_start: float = field(default_factory=time.time)
    entries: List[TimingEntry] = field(default_factory=list)
    current_stack: List[TimingEntry] = field(default_factory=list)

    def start_operation(self, name: str, step_type: str = "operation", details: Optional[str] = None) -> TimingEntry:
        """Start timing a new operation"""
        entry = TimingEntry(
            name=name,
            start_time=time.time(),
            step_type=step_type,
            details=details
        )

        # Add to current parent or root level
        if self.current_stack:
            self.current_stack[-1].add_sub_entry(entry)
        else:
            self.entries.append(entry)

        self.current_stack.append(entry)
        return entry

    def finish_operation(self, details: Optional[str] = None) -> Optional[TimingEntry]:
        """Finish the current operation"""
        if not self.current_stack:
            return None

        entry = self.current_stack.pop()
        entry.finish(details)
        return entry

    def get_total_duration(self) -> float:
        """Get total session duration"""
        return time.time() - self.session_start

    def get_tracked_operations_total(self) -> float:
        """Calculate total time of all tracked operations"""
        total = 0.0
        for entry in self.entries:
            if entry.duration is not None:
                total += entry.duration
        return total

    def display_timing_report(self, console):
        """Display a beautiful timing report using Rich"""
        # Import Rich components here to avoid circular imports
        from rich.panel import Panel
        from rich.tree import Tree
        from rich.table import Table

        total_session_duration = self.get_total_duration()
        tracked_operations_total = self.get_tracked_operations_total()
        untracked_time = total_session_duration - tracked_operations_total

        # Create main panel with both metrics
        console.print("\n" + "="*80)
        console.print(Panel.fit(
            f"[bold cyan]🕒 DETAILED TIMING REPORT[/bold cyan]\n"
            f"[green]Total Session Duration: {total_session_duration:.3f}s[/green]\n"
            f"[blue]Tracked Operations: {tracked_operations_total:.3f}s[/blue]\n"
            f"[yellow]Untracked Time (LLM calls, overhead): {untracked_time:.3f}s[/yellow]",
            border_style="cyan"
        ))

        # Create timing tree showing tracked operations
        tree = Tree(f"[bold]Tracked Operations Timeline ({tracked_operations_total:.3f}s of {total_session_duration:.3f}s total)[/bold]")

        for entry in self.entries:
            self._add_entry_to_tree(tree, entry)

        console.print(tree)

        # Create summary table
        self._display_timing_summary(console)

    def _add_entry_to_tree(self, parent, entry: TimingEntry):
        """Recursively add timing entries to the tree"""
        duration_str = f"{entry.duration:.3f}s" if entry.duration else "ongoing"

        # Color code by step type
        color_map = {
            "tool_call": "yellow",
            "analysis": "blue",
            "query": "green",
            "operation": "white"
        }
        color = color_map.get(entry.step_type, "white")

        # Create node text
        node_text = f"[{color}]{entry.name}[/{color}] ({duration_str})"
        if entry.details:
            node_text += f" - {entry.details[:50]}{'...' if len(entry.details) > 50 else ''}"

        node = parent.add(node_text)

        # Add sub-entries
        for sub_entry in entry.sub_entries:
            self._add_entry_to_tree(node, sub_entry)

    def _display_timing_summary(self, console):
        """Display timing summary table"""
        from rich.table import Table

        table = Table(title="Timing Summary by Category", border_style="blue")
        table.add_column("Category", style="cyan", no_wrap=True)
        table.add_column("Count", justify="right", style="magenta")
        table.add_column("Total Time", justify="right", style="green")
        table.add_column("Avg Time", justify="right", style="yellow")
        table.add_column("Min Time", justify="right", style="red")
        table.add_column("Max Time", justify="right", style="red")

        # Collect stats by category
        stats = defaultdict(list)
        self._collect_stats(self.entries, stats)

        for category, durations in stats.items():
            if durations:
                count = len(durations)
                total = sum(durations)
                avg = total / count
                min_time = min(durations)
                max_time = max(durations)

                table.add_row(
                    category.title(),
                    str(count),
                    f"{total:.3f}s",
                    f"{avg:.3f}s",
                    f"{min_time:.3f}s",
                    f"{max_time:.3f}s"
                )

        console.print(table)

    def _collect_stats(self, entries: List[TimingEntry], stats: Dict[str, List[float]]):
        """Recursively collect timing statistics"""
        for entry in entries:
            if entry.duration is not None:
                stats[entry.step_type].append(entry.duration)
            self._collect_stats(entry.sub_entries, stats)

# Global timing tracker instance
timing_tracker = TimingTracker()

def timed_operation(name: str, step_type: str = "operation"):
    """Decorator for timing operations"""
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            timing_tracker.start_operation(name, step_type)
            try:
                result = await func(*args, **kwargs)
                timing_tracker.finish_operation(f"Success: {type(result).__name__}")
                return result
            except Exception as e:
                timing_tracker.finish_operation(f"Error: {str(e)[:50]}")
                raise

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            timing_tracker.start_operation(name, step_type)
            try:
                result = func(*args, **kwargs)
                timing_tracker.finish_operation(f"Success: {type(result).__name__}")
                return result
            except Exception as e:
                timing_tracker.finish_operation(f"Error: {str(e)[:50]}")
                raise

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator

# Enhanced Data Models
@dataclass
class OntologyContext:
    """Context extracted from ontology analysis"""
    class_relationships: Dict[str, List[str]]  # class -> [related_classes]
    property_domains: Dict[str, List[str]]     # property -> [domain_classes]
    property_ranges: Dict[str, List[str]]      # property -> [range_classes]
    class_properties: Dict[str, List[str]]     # class -> [applicable_properties]
    property_constraints: Dict[str, Dict]      # property -> constraint_info

@dataclass
class GuidelinesContext:
    """Context extracted from guidelines analysis"""
    property_definitions: Dict[str, Dict]      # property -> definition_info
    validation_rules: Dict[str, Dict]          # property -> validation_rules
    required_properties: Dict[str, List[str]]  # class -> [required_properties]
    property_types: Dict[str, str]             # property -> data_type
    property_units: Dict[str, str]             # property -> unit_info

@dataclass
class EnhancedTTLContext:
    """Enhanced context containing TTL graph, ontology, and guidelines"""
    graph: Graph
    file_path: str
    basic_analysis: str
    ontology_context: OntologyContext
    guidelines_context: GuidelinesContext

class ContextualInsight(BaseModel):
    """Enhanced insight with contextual information"""
    insight: str = Field(description="The insight discovered from the data")
    confidence: float = Field(description="Confidence in the insight", default=0.8)
    supporting_data: str = Field(description="Summary of data supporting this insight")
    ontology_context: str = Field(description="Relevant ontology constraints and relationships")
    guidelines_context: str = Field(description="Relevant guidelines and validation rules")

class EnhancedSPARQLQuery(BaseModel):
    """Enhanced SPARQL query with contextual validation"""
    query: str = Field(description="The SPARQL query string")
    query_type: str = Field(description="Type of SPARQL query (SELECT, ASK, etc.)")
    description: str = Field(description="What the query does")
    confidence: float = Field(description="Confidence in the query generation", default=0.8)
    validation_passed: bool = Field(description="Whether the query passed syntax validation", default=False)
    ontology_compliance: str = Field(description="How the query aligns with ontology constraints")
    guidelines_compliance: str = Field(description="How the query follows guidelines")

class ContextualStrategy(BaseModel):
    """Enhanced strategy with contextual awareness"""
    approach: str = Field(description="High-level approach to take")
    steps: List[str] = Field(description="Specific steps to execute")
    expected_queries: int = Field(description="Expected number of queries needed")
    reasoning: str = Field(description="Why this strategy was chosen")
    ontology_considerations: str = Field(description="Ontology-based considerations")
    guidelines_considerations: str = Field(description="Guidelines-based considerations")

class ContextRequirements(BaseModel):
    """Analysis of what context is needed for a question"""
    needs_ontology: bool = Field(description="Whether ontology context is needed")
    needs_guidelines: bool = Field(description="Whether guidelines context is needed")
    needs_analysis_only: bool = Field(description="Whether only basic TTL analysis is sufficient")
    reasoning: str = Field(description="Detailed explanation of why these contexts are needed")
    question_type: str = Field(description="Type of question (exploratory, counting, validation, relationship, existence, complex)")
    confidence: float = Field(description="Confidence in the context requirement analysis", default=0.8)
    key_concepts: List[str] = Field(description="Key concepts identified in the question that influenced the decision")
    expected_complexity: str = Field(description="Expected complexity level (low, medium, high)")

# ===== ANALYSIS FUNCTIONS =====

@timed_operation("Analyze Ontology", "analysis")
def analyze_ontology(ontology_path: str) -> OntologyContext:
    """Analyze ontology file to extract relationship and constraint information"""
    try:
        g = Graph()
        g.parse(ontology_path, format='turtle')

        # Define namespaces
        IBPDI = Namespace("https://ibpdi.datacat.org/class/")
        RDFS = Namespace("http://www.w3.org/2000/01/rdf-schema#")
        OWL = Namespace("http://www.w3.org/2002/07/owl#")

        class_relationships = defaultdict(list)
        property_domains = defaultdict(list)
        property_ranges = defaultdict(list)
        class_properties = defaultdict(list)
        property_constraints = {}

        # Extract property domain/range relationships
        for subj, pred, obj in g:
            if pred == RDFS.domain:
                prop_name = str(subj).split('/')[-1]
                domain_class = str(obj).split('/')[-1]
                property_domains[prop_name].append(domain_class)
                class_properties[domain_class].append(prop_name)

            elif pred == RDFS.range:
                prop_name = str(subj).split('/')[-1]
                range_class = str(obj).split('/')[-1]
                property_ranges[prop_name].append(range_class)

                # Build class relationships
                if prop_name in property_domains:
                    for domain_class in property_domains[prop_name]:
                        if range_class not in class_relationships[domain_class]:
                            class_relationships[domain_class].append(range_class)

        # Extract property constraints
        for prop in property_domains.keys():
            property_constraints[prop] = {
                'domains': property_domains[prop],
                'ranges': property_ranges[prop],
                'type': 'ObjectProperty'  # Default from ontology
            }

        return OntologyContext(
            class_relationships=dict(class_relationships),
            property_domains=dict(property_domains),
            property_ranges=dict(property_ranges),
            class_properties=dict(class_properties),
            property_constraints=property_constraints
        )

    except Exception as e:
        print(f"Error analyzing ontology: {e}")
        return OntologyContext({}, {}, {}, {}, {})

@timed_operation("Analyze Guidelines", "analysis")
def analyze_guidelines(guidelines_path: str) -> GuidelinesContext:
    """Analyze guidelines JSON to extract property definitions and validation rules"""
    try:
        with open(guidelines_path, 'r', encoding='utf-8-sig') as f:
            guidelines_data = json.load(f)

        property_definitions = {}
        validation_rules = {}
        required_properties = defaultdict(list)
        property_types = {}
        property_units = {}

        # Navigate the guidelines structure
        domain = guidelines_data.get('Domain', {})
        classifications = domain.get('Classifications', {}).get('$values', [])

        for classification in classifications:
            class_name = classification.get('Name', '')
            class_properties = classification.get('ClassificationProperties', {}).get('$values', [])

            for prop_info in class_properties:
                prop_name = prop_info.get('Name', '')
                prop_desc = prop_info.get('Description', '')
                is_required = prop_info.get('IsRequired', False)

                # Extract property assignment details
                prop_assignment = prop_info.get('PropertyAssignment', {})
                property_details = prop_assignment.get('Property', {})

                storage_type = property_details.get('StorageType', 4)  # 4 = string default
                unit_type = property_details.get('UnitType')
                unit_abbrev = property_details.get('UnitAbbreviation')
                min_val = prop_assignment.get('Min')
                max_val = prop_assignment.get('Max')

                # Store property definition
                property_definitions[prop_name] = {
                    'description': prop_desc,
                    'class': class_name,
                    'identifier': property_details.get('Identifier', ''),
                    'storage_type': storage_type
                }

                # Store validation rules
                validation_rules[prop_name] = {
                    'min_value': min_val,
                    'max_value': max_val,
                    'min_inclusive': prop_assignment.get('MinIsInclusive', False),
                    'max_inclusive': prop_assignment.get('MaxIsInclusive', False),
                    'required': is_required
                }

                # Track required properties per class
                if is_required:
                    required_properties[class_name].append(prop_name)

                # Map storage types to readable types
                type_mapping = {
                    1: 'integer',
                    2: 'float',
                    3: 'boolean',
                    4: 'string',
                    5: 'datetime'
                }
                property_types[prop_name] = type_mapping.get(storage_type, 'string')

                # Store unit information
                if unit_type or unit_abbrev:
                    property_units[prop_name] = f"{unit_type or ''} ({unit_abbrev or ''})"

        return GuidelinesContext(
            property_definitions=property_definitions,
            validation_rules=validation_rules,
            required_properties=dict(required_properties),
            property_types=property_types,
            property_units=property_units
        )

    except Exception as e:
        print(f"Error analyzing guidelines: {e}")
        return GuidelinesContext({}, {}, {}, {}, {})

@timed_operation("Enhanced TTL Analysis", "analysis")
def analyze_ttl_file_enhanced(ttl_file_path: str, ontology_path: str, guidelines_path: str) -> str:
    """Enhanced TTL analysis incorporating ontology and guidelines context"""
    try:
        # Get basic analysis (from phase 5)
        basic_analysis = analyze_ttl_file_basic(ttl_file_path)

        # Get enhanced context
        ontology_context = analyze_ontology(ontology_path)
        guidelines_context = analyze_guidelines(guidelines_path)

        # Build enhanced analysis
        enhanced_analysis = f"""{basic_analysis}

=== ENHANCED ONTOLOGY CONTEXT ===
Class Relationships:
{format_class_relationships(ontology_context.class_relationships)}

Property Domain/Range Constraints:
{format_property_constraints(ontology_context.property_constraints)}

=== GUIDELINES CONTEXT ===
Property Definitions and Validation Rules:
{format_property_definitions(guidelines_context.property_definitions, guidelines_context.validation_rules)}

Required Properties by Class:
{format_required_properties(guidelines_context.required_properties)}

Property Data Types and Units:
{format_property_types_units(guidelines_context.property_types, guidelines_context.property_units)}

=== CONTEXTUAL QUERY GUIDANCE ===
- Use ontology constraints to ensure proper domain/range relationships
- Follow guidelines for property validation and data types
- Consider required properties when querying specific classes
- Respect unit constraints and value ranges from guidelines
- Leverage class relationships for complex queries across entities
"""

        return enhanced_analysis

    except Exception as e:
        return f"Error in enhanced TTL analysis: {e}"

@timed_operation("Basic TTL Analysis", "analysis")
def analyze_ttl_file_basic(ttl_file_path: str) -> str:
    """Basic TTL analysis from phase 5 (simplified version)"""
    try:
        g = Graph()
        g.parse(ttl_file_path, format='turtle')

        total_triples = len(g)

        # Get unique classes
        classes_query = """
        SELECT DISTINCT ?class WHERE {
            ?s a ?class .
        }
        """
        classes = [str(row[0]) for row in g.query(classes_query)]

        # Get unique properties
        props_query = """
        SELECT DISTINCT ?prop WHERE {
            ?s ?prop ?o .
            FILTER(?prop != <http://www.w3.org/1999/02/22-rdf-syntax-ns#type>)
        }
        """
        properties = [str(row[0]) for row in g.query(props_query)]

        # Get prefixes
        prefixes = dict(g.namespaces())

        return f"""BASIC TTL FILE ANALYSIS:
=== STATISTICS ===
- Total triples: {total_triples}
- Classes found: {len(classes)}
- Properties found: {len(properties)}
- Prefixes defined: {len(prefixes)}

=== PREFIX DEFINITIONS ===
{chr(10).join([f"@prefix {prefix}: <{namespace}> ." for prefix, namespace in list(prefixes.items())])}

=== CLASSES ===
{chr(10).join([f"- {cls}" for cls in classes[:10]])}

=== PROPERTIES ===
{chr(10).join([f"- {prop}" for prop in properties[:10]])}
"""

    except Exception as e:
        return f"Error in basic TTL analysis: {e}"

# ===== FORMATTING FUNCTIONS =====

def format_class_relationships(relationships: Dict[str, List[str]]) -> str:
    """Format class relationships for display"""
    if not relationships or relationships is None:
        return "No class relationships found"

    formatted = []
    try:
        for class_name, related_classes in relationships.items():
            if related_classes and isinstance(related_classes, (list, tuple)):
                formatted.append(f"- {class_name} → {', '.join(str(cls) for cls in related_classes)}")
    except (AttributeError, TypeError):
        return "Error formatting class relationships"

    return '\n'.join(formatted[:10]) + ("..." if len(formatted) > 10 else "")

def format_property_constraints(constraints: Dict[str, Dict]) -> str:
    """Format property constraints for display"""
    if not constraints or constraints is None:
        return "No property constraints found"

    formatted = []
    try:
        for prop_name, constraint_info in constraints.items():
            if isinstance(constraint_info, dict):
                domains = ', '.join(str(d) for d in constraint_info.get('domains', []))
                ranges = ', '.join(str(r) for r in constraint_info.get('ranges', []))
                formatted.append(f"- {prop_name}: {domains} → {ranges}")
    except (AttributeError, TypeError):
        return "Error formatting property constraints"

    return '\n'.join(formatted[:10]) + ("..." if len(formatted) > 10 else "")

def format_property_definitions(definitions: Dict[str, Dict], rules: Dict[str, Dict]) -> str:
    """Format property definitions and validation rules"""
    if not definitions or definitions is None:
        return "No property definitions found"

    formatted = []
    try:
        rules = rules or {}  # Handle None rules
        for prop_name, definition in definitions.items():
            if isinstance(definition, dict):
                desc = definition.get('description', 'No description')
                rule = rules.get(prop_name, {}) if isinstance(rules, dict) else {}
                required = "Required" if rule.get('required', False) else "Optional"
                formatted.append(f"- {prop_name}: {desc} ({required})")
    except (AttributeError, TypeError):
        return "Error formatting property definitions"

    return '\n'.join(formatted[:10]) + ("..." if len(formatted) > 10 else "")

def format_required_properties(required_props: Dict[str, List[str]]) -> str:
    """Format required properties by class"""
    if not required_props or required_props is None:
        return "No required properties found"

    formatted = []
    try:
        for class_name, props in required_props.items():
            if props and isinstance(props, (list, tuple)):
                formatted.append(f"- {class_name}: {', '.join(str(p) for p in props)}")
    except (AttributeError, TypeError):
        return "Error formatting required properties"

    return '\n'.join(formatted[:5]) + ("..." if len(formatted) > 5 else "")

def format_property_types_units(types: Dict[str, str], units: Dict[str, str]) -> str:
    """Format property types and units"""
    if not types or types is None:
        return "No property type information found"

    formatted = []
    try:
        units = units or {}  # Handle None units
        for prop_name, prop_type in types.items():
            unit_info = units.get(prop_name, '') if isinstance(units, dict) else ''
            unit_str = f" [{unit_info}]" if unit_info else ""
            formatted.append(f"- {prop_name}: {prop_type}{unit_str}")
    except (AttributeError, TypeError):
        return "Error formatting property types and units"

    return '\n'.join(formatted[:10]) + ("..." if len(formatted) > 10 else "")

# ===== SPARQL VALIDATION =====

@timed_operation("Validate SPARQL Query", "operation")
def validate_sparql_query(query: str) -> str:
    """Validate SPARQL syntax and return query type."""
    if not query or not isinstance(query, str):
        raise ValueError("Query must be a non-empty string")

    try:
        prepareQuery(query)
        query_upper = query.upper().strip()
        if 'SELECT' in query_upper:
            return 'SELECT'
        elif 'ASK' in query_upper:
            return 'ASK'
        elif 'CONSTRUCT' in query_upper:
            return 'CONSTRUCT'
        elif 'DESCRIBE' in query_upper:
            return 'DESCRIBE'
        else:
            return 'UNKNOWN'
    except Exception as e:
        raise ValueError(f"Invalid SPARQL syntax: {e}")

# ===== AI AGENTS =====

# Context Analysis Reasoning Agent
context_analysis_agent = Agent[str, ContextRequirements](
    model,
    deps_type=str,
    output_type=ContextRequirements,
    system_prompt="""You are an intelligent Context Analysis Agent specialized in determining what contextual information is needed to answer TTL/RDF data questions.

Your role is to analyze user questions about TTL/RDF data and determine the minimal set of contexts needed to provide accurate answers.

AVAILABLE CONTEXTS:
1. BASIC TTL ANALYSIS: Always available - includes basic statistics, classes, properties, prefixes
2. ONTOLOGY CONTEXT: Domain/range constraints, class relationships, property hierarchies, structural information
3. GUIDELINES CONTEXT: Property definitions, validation rules, data types, units, constraints

REASONING FRAMEWORK:
Analyze the question to understand:
- What type of information is being requested?
- What level of semantic understanding is required?
- Are relationships between entities important?
- Are validation rules or constraints relevant?
- Is structural/schema information needed?

QUESTION TYPES & CONTEXT REQUIREMENTS:

BASIC ANALYSIS ONLY (needs_analysis_only=True):
- Simple counting questions (How many X?)
- Basic existence checks (Does X exist?)
- Simple listing without relationships (What are the values of X?)
- Basic statistics queries
- Direct property value retrieval

ONTOLOGY CONTEXT NEEDED (needs_ontology=True):
- Questions about relationships between classes/entities
- Structural queries about the data model
- Questions involving domain/range constraints
- Hierarchical or inheritance questions
- Complex entity traversal queries
- Questions about how things are connected

GUIDELINES CONTEXT NEEDED (needs_guidelines=True):
- Questions about validation rules or constraints
- Data type or format questions
- Unit or measurement questions
- Required vs optional property questions
- Value range or boundary questions
- Compliance or specification questions

BOTH CONTEXTS NEEDED:
- Complex analytical questions requiring both structure and rules
- Comparative analysis across different aspects
- Comprehensive data quality or completeness questions
- Questions that need both relationships and validation

CRITICAL REASONING PRINCIPLES:
1. Default to minimal context - only add what's truly necessary
2. Consider the semantic depth required by the question
3. Think about whether the answer requires understanding relationships vs rules
4. Be conservative - don't over-engineer context requirements
5. Explain your reasoning clearly and specifically

OUTPUT REQUIREMENTS:
- Set needs_analysis_only=True ONLY if basic TTL analysis is sufficient
- Set needs_ontology=True if relationships, structure, or domain knowledge is needed
- Set needs_guidelines=True if validation, rules, or constraints are needed
- Provide clear, specific reasoning for your decisions
- Identify key concepts that influenced your analysis
- Assess expected complexity level"""
)

# Enhanced SPARQL Query Generation Agent with Context Awareness
enhanced_sparql_agent = Agent[EnhancedTTLContext, EnhancedSPARQLQuery](
    model,
    deps_type=EnhancedTTLContext,
    output_type=EnhancedSPARQLQuery,
    system_prompt="""You are an expert SPARQL query generator with advanced ontology and guidelines awareness.

    Your task is to:
    1. CAREFULLY analyze the comprehensive TTL file structure, ontology constraints, and guidelines
    2. Understand the user's natural language question
    3. Generate a syntactically correct SPARQL query that answers the question
    4. Ensure the query complies with ontology domain/range constraints
    5. Follow guidelines for property validation and data types

    CRITICAL Context Usage Rules:
    - ALWAYS use the exact PREFIX definitions provided in the TTL analysis
    - Respect ontology domain/range constraints when connecting classes via properties
    - Follow guidelines for property data types and validation rules
    - Consider required properties when querying specific classes
    - Use the class relationships from ontology to build complex queries

    Query Construction Guidelines:
    - Always use proper PREFIX declarations from the TTL analysis
    - Ensure properties are used with correct domain classes (from ontology)
    - Respect range constraints when filtering or connecting to other classes
    - Use appropriate data types as specified in guidelines
    - Consider unit constraints and value ranges from guidelines
    - For counting questions, use COUNT(*) or COUNT(?variable)
    - For listing questions, use SELECT with appropriate variables
    - For existence questions, consider using ASK queries
    - NEVER add LIMIT clauses unless explicitly requested by the user
    - Always return complete results without artificial limitations

    Compliance Reporting:
    - In ontology_compliance, explain how the query respects domain/range constraints
    - In guidelines_compliance, explain how the query follows property validation rules
    - If constraints cannot be satisfied, explain the limitations and suggest alternatives
    """
)

@enhanced_sparql_agent.output_validator
async def validate_enhanced_sparql_output(ctx: RunContext[EnhancedTTLContext], output: EnhancedSPARQLQuery) -> EnhancedSPARQLQuery:
    try:
        query_type = validate_sparql_query(output.query)
        output.query_type = query_type
        output.validation_passed = True
        return output
    except ValueError as e:
        raise ModelRetry(f'SPARQL validation failed: {e}')

# Enhanced TTL QA Agent with Dynamic Context Management
enhanced_ttl_qa_agent = Agent[EnhancedTTLContext, str](
    model,
    deps_type=EnhancedTTLContext,
    output_type=str,
    system_prompt="""You are an intelligent TTL (Turtle RDF) question-answering agent with SMART DYNAMIC CONTEXT MANAGEMENT.

Your role is to help users understand and query TTL/RDF data using natural language questions. You have access to several enhanced tools:

1. analyze_context_requirements: SMART analysis to determine what context is actually needed
2. extract_ontology_context: Get relevant ontology constraints (use only when needed)
3. extract_guidelines_context: Get relevant guidelines and validation rules (use only when needed)
4. develop_contextual_strategy: Plan approach considering available context
5. generate_enhanced_sparql_query: Create context-aware SPARQL queries
6. execute_sparql_query: Run queries against the TTL data
7. analyze_contextual_results: Extract insights with available context

CRITICAL SMART CONTEXT MANAGEMENT:
You have access to enhanced context including:
- TTL file structure and analysis (always available)
- Ontology constraints (load only when needed)
- Guidelines (load only when needed)

SMART METHODOLOGY:
For each user question, you should:
1. FIRST: Use analyze_context_requirements to determine what context is actually needed
2. CONDITIONALLY: Based on the analysis, extract only the required context:
   - If needs_ontology=True: Use extract_ontology_context
   - If needs_guidelines=True: Use extract_guidelines_context
   - If needs_analysis_only=True: Skip context extraction and use basic TTL analysis
3. Develop a strategy using develop_contextual_strategy with available context
4. Generate queries using generate_enhanced_sparql_query with available context
5. Execute queries using execute_sparql_query
6. Analyze results using analyze_contextual_results with available context
7. If needed, generate follow-up queries based on available context
8. Finally, synthesize all findings into a comprehensive answer

IMPORTANT EFFICIENCY RULES:
- ALWAYS start with analyze_context_requirements to avoid unnecessary tool calls
- ONLY extract ontology context if the analysis indicates it's needed
- ONLY extract guidelines context if the analysis indicates it's needed
- For simple questions (counting, basic queries), often only TTL analysis is sufficient
- Explain your context selection reasoning to the user
- Show how the smart context management improved efficiency
- Be thorough but efficient in your analysis

CONTEXT USAGE GUIDELINES:
- Basic questions (simple counts, existence checks): Usually need only TTL analysis
- Relationship questions: Need ontology context
- Validation questions: Need guidelines context
- Complex analysis: May need both ontology and guidelines context

You must provide a comprehensive natural language response explaining your findings and your smart context selection process."""
)

# ===== AGENT TOOLS =====

@enhanced_ttl_qa_agent.tool
@timed_operation("Context Requirements Analysis", "tool_call")
async def analyze_context_requirements(
    ctx: RunContext[EnhancedTTLContext],
    question: str
) -> ContextRequirements:
    """Intelligently analyze the user question to determine what context is actually needed.

    Uses a specialized reasoning agent to make smart decisions about context requirements
    rather than relying on simple keyword matching.

    Args:
        question: The user's natural language question

    Returns:
        Intelligent analysis of what context types are needed for this question
    """
    try:
        # Use the specialized context analysis agent to make intelligent decisions
        result = await context_analysis_agent.run(
            f"Analyze this TTL/RDF question to determine what context is needed: '{question}'",
            deps=question  # Pass the question as dependency
        )

        return result.output

    except Exception as e:
        # Fallback to conservative approach if agent fails
        return ContextRequirements(
            needs_ontology=True,
            needs_guidelines=True,
            needs_analysis_only=False,
            reasoning=f"Error in intelligent analysis ({e}), defaulting to full context for safety",
            question_type="unknown",
            confidence=0.5,
            key_concepts=["error_fallback"],
            expected_complexity="high"
        )

@enhanced_ttl_qa_agent.tool
@timed_operation("Extract Ontology Context", "tool_call")
async def extract_ontology_context(
    ctx: RunContext[EnhancedTTLContext],
    question: str,
    entities_mentioned: Optional[List[str]] = None
) -> str:
    """Extract relevant ontology constraints and relationships for the given question.

    Args:
        question: The user's question
        entities_mentioned: Optional list of specific entities/classes mentioned in the question

    Returns:
        Relevant ontology context including class relationships and property constraints
    """
    ontology = ctx.deps.ontology_context

    # Identify relevant classes and properties from the question
    question_lower = question.lower()
    relevant_classes = []
    relevant_properties = []

    # Extract entities from question or use provided ones
    if entities_mentioned:
        relevant_classes.extend(entities_mentioned)
    else:
        # Simple keyword matching for common entities
        for class_name in ontology.class_relationships.keys():
            if class_name.lower() in question_lower:
                relevant_classes.append(class_name)

        for prop_name in ontology.property_domains.keys():
            if prop_name.lower() in question_lower:
                relevant_properties.append(prop_name)

    # Build contextual information
    context_parts = []

    if relevant_classes:
        context_parts.append("=== RELEVANT CLASS RELATIONSHIPS ===")
        for class_name in relevant_classes:
            if class_name in ontology.class_relationships:
                related = ontology.class_relationships[class_name]
                context_parts.append(f"- {class_name} connects to: {', '.join(related)}")

            if class_name in ontology.class_properties:
                props = ontology.class_properties[class_name]
                context_parts.append(f"- {class_name} has properties: {', '.join(props[:5])}")

    if relevant_properties:
        context_parts.append("\n=== RELEVANT PROPERTY CONSTRAINTS ===")
        for prop_name in relevant_properties:
            if prop_name in ontology.property_constraints:
                constraint = ontology.property_constraints[prop_name]
                domains = ', '.join(constraint.get('domains', []))
                ranges = ', '.join(constraint.get('ranges', []))
                context_parts.append(f"- {prop_name}: domain={domains}, range={ranges}")

    # Add general guidance if no specific matches
    if not context_parts:
        context_parts.append("=== GENERAL ONTOLOGY GUIDANCE ===")
        context_parts.append("- Available class relationships:")
        for class_name, related in list(ontology.class_relationships.items())[:5]:
            context_parts.append(f"  * {class_name} → {', '.join(related)}")

        context_parts.append("- Key property constraints:")
        for prop_name, constraint in list(ontology.property_constraints.items())[:5]:
            domains = ', '.join(constraint.get('domains', []))
            ranges = ', '.join(constraint.get('ranges', []))
            context_parts.append(f"  * {prop_name}: {domains} → {ranges}")

    return '\n'.join(context_parts)

@enhanced_ttl_qa_agent.tool
@timed_operation("Extract Guidelines Context", "tool_call")
async def extract_guidelines_context(
    ctx: RunContext[EnhancedTTLContext],
    question: str,
    properties_mentioned: Optional[List[str]] = None
) -> str:
    """Extract relevant guidelines and validation rules for the given question.

    Args:
        question: The user's question
        properties_mentioned: Optional list of specific properties mentioned in the question

    Returns:
        Relevant guidelines context including property definitions and validation rules
    """
    guidelines = ctx.deps.guidelines_context

    # Identify relevant properties from the question
    question_lower = question.lower()
    relevant_properties = []

    # Extract properties from question or use provided ones
    if properties_mentioned:
        relevant_properties.extend(properties_mentioned)
    else:
        # Simple keyword matching for properties
        for prop_name in guidelines.property_definitions.keys():
            if prop_name.lower() in question_lower:
                relevant_properties.append(prop_name)

    # Build contextual information
    context_parts = []

    if relevant_properties:
        context_parts.append("=== RELEVANT PROPERTY DEFINITIONS ===")
        for prop_name in relevant_properties:
            if prop_name in guidelines.property_definitions:
                definition = guidelines.property_definitions[prop_name]
                desc = definition.get('description', 'No description')
                prop_type = guidelines.property_types.get(prop_name, 'string')
                context_parts.append(f"- {prop_name}: {desc} (type: {prop_type})")

                # Add validation rules
                if prop_name in guidelines.validation_rules:
                    rules = guidelines.validation_rules[prop_name]
                    if rules.get('required'):
                        context_parts.append(f"  * Required property")
                    if rules.get('min_value') is not None:
                        context_parts.append(f"  * Min value: {rules['min_value']}")
                    if rules.get('max_value') is not None:
                        context_parts.append(f"  * Max value: {rules['max_value']}")

                # Add unit information
                if prop_name in guidelines.property_units:
                    unit_info = guidelines.property_units[prop_name]
                    context_parts.append(f"  * Units: {unit_info}")

    # Add general guidance if no specific matches
    if not context_parts:
        context_parts.append("=== GENERAL GUIDELINES GUIDANCE ===")
        context_parts.append("- Available property types:")
        for prop_name, prop_type in list(guidelines.property_types.items())[:5]:
            context_parts.append(f"  * {prop_name}: {prop_type}")

        context_parts.append("- Required properties by class:")
        for class_name, props in list(guidelines.required_properties.items())[:3]:
            if props:
                context_parts.append(f"  * {class_name}: {', '.join(props)}")

    return '\n'.join(context_parts)

@enhanced_ttl_qa_agent.tool
@timed_operation("Develop Contextual Strategy", "tool_call")
async def develop_contextual_strategy(
    ctx: RunContext[EnhancedTTLContext],
    user_question: str,
    ontology_context: Optional[str] = None,
    guidelines_context: Optional[str] = None
) -> ContextualStrategy:
    """Develop a strategic approach considering available context (ontology and/or guidelines).

    Args:
        user_question: The user's natural language question
        ontology_context: Optional ontology constraints from extract_ontology_context
        guidelines_context: Optional guidelines from extract_guidelines_context

    Returns:
        Enhanced strategy with contextual considerations based on available context
    """
    question_lower = user_question.lower()

    # Analyze question type and context
    if any(word in question_lower for word in ["how many", "count", "number"]):
        approach = "Context-aware counting analysis"
        steps = [
            "Identify target entities using ontology class relationships",
            "Verify property constraints from guidelines before filtering",
            "Generate COUNT query respecting domain/range constraints",
            "Execute query and validate results against expected data types",
            "Provide count with context about data completeness and constraints"
        ]
        expected_queries = 1
        reasoning = "Counting question requires precise entity identification and constraint validation"

    elif any(word in question_lower for word in ["what", "which", "list", "show"]):
        approach = "Context-aware exploratory retrieval"
        steps = [
            "Map question entities to ontology classes and relationships",
            "Identify required vs optional properties from guidelines",
            "Generate initial query following domain/range constraints",
            "Execute and analyze results for completeness",
            "Generate follow-up queries if needed based on context",
            "Synthesize findings with constraint explanations"
        ]
        expected_queries = 2
        reasoning = "Exploratory question benefits from multi-step context-aware querying"

    elif any(word in question_lower for word in ["compare", "difference", "versus"]):
        approach = "Context-aware comparative analysis"
        steps = [
            "Identify comparable entities using ontology relationships",
            "Determine comparison criteria from guidelines property definitions",
            "Generate queries for each comparison target with proper constraints",
            "Execute comparative queries respecting data types and units",
            "Analyze differences considering validation rules and constraints",
            "Provide comparative insights with context explanations"
        ]
        expected_queries = 3
        reasoning = "Comparative analysis requires careful constraint matching across entities"

    else:
        approach = "General context-aware inquiry"
        steps = [
            "Analyze question using full ontology and guidelines context",
            "Identify relevant classes, properties, and constraints",
            "Generate context-compliant exploratory queries",
            "Execute queries with constraint validation",
            "Analyze results considering all contextual factors",
            "Provide comprehensive answer with context explanations"
        ]
        expected_queries = 2
        reasoning = "General inquiry requires comprehensive contextual analysis"

    # Extract ontology considerations (handle optional context)
    if ontology_context:
        ontology_considerations = f"Ontology context: {ontology_context[:200]}..." if len(ontology_context) > 200 else ontology_context
    else:
        ontology_considerations = "No ontology context loaded - using basic TTL analysis only"

    # Extract guidelines considerations (handle optional context)
    if guidelines_context:
        guidelines_considerations = f"Guidelines context: {guidelines_context[:200]}..." if len(guidelines_context) > 200 else guidelines_context
    else:
        guidelines_considerations = "No guidelines context loaded - using basic TTL analysis only"

    return ContextualStrategy(
        approach=approach,
        steps=steps,
        expected_queries=expected_queries,
        reasoning=reasoning,
        ontology_considerations=ontology_considerations,
        guidelines_considerations=guidelines_considerations
    )

@enhanced_ttl_qa_agent.tool
@timed_operation("Generate Enhanced SPARQL Query", "tool_call")
async def generate_enhanced_sparql_query(
    ctx: RunContext[EnhancedTTLContext],
    question: str,
    ontology_context: Optional[str] = None,
    guidelines_context: Optional[str] = None,
    strategy_context: Optional[str] = None
) -> EnhancedSPARQLQuery:
    """Generate a context-aware SPARQL query using available context information.

    Args:
        question: The specific question to answer
        ontology_context: Optional ontology constraints
        guidelines_context: Optional guidelines and validation rules
        strategy_context: Additional context from the overall strategy
    """
    try:
        # Prepare enhanced context for the SPARQL agent
        context_sections = []

        if ontology_context:
            context_sections.append(f"ONTOLOGY CONTEXT:\n{ontology_context}")
        else:
            context_sections.append("ONTOLOGY CONTEXT:\nNot loaded - using basic TTL analysis only")

        if guidelines_context:
            context_sections.append(f"GUIDELINES CONTEXT:\n{guidelines_context}")
        else:
            context_sections.append("GUIDELINES CONTEXT:\nNot loaded - using basic TTL analysis only")

        if strategy_context:
            context_sections.append(f"STRATEGY CONTEXT:\n{strategy_context}")
        else:
            context_sections.append("STRATEGY CONTEXT:\nNo additional strategy context")

        enhanced_context_prompt = f"""Generate a SPARQL query to answer this question: "{question}"

{chr(10).join(context_sections)}

TTL FILE ANALYSIS:
{ctx.deps.basic_analysis}

Please generate a SPARQL query that:
1. Uses the exact prefixes and namespaces from the TTL analysis
2. {"Respects ontology domain/range constraints for property usage" if ontology_context else "Uses basic property relationships from TTL analysis"}
3. {"Follows guidelines for property data types and validation rules" if guidelines_context else "Uses basic data types from TTL analysis"}
4. Targets the appropriate classes and properties for this question
5. Is syntactically correct and executable
6. Explains compliance with available context in the response fields

Focus on the question's intent while ensuring compliance with available contextual constraints."""

        # Use the enhanced SPARQL agent to generate the query
        result = await enhanced_sparql_agent.run(enhanced_context_prompt, deps=ctx.deps)

        return result.output

    except Exception as e:
        # Return error query with context information
        return EnhancedSPARQLQuery(
            query=f"# Error generating query: {e}",
            query_type="ERROR",
            description=f"Failed to generate context-aware query: {e}",
            confidence=0.1,
            validation_passed=False,
            ontology_compliance="Error prevented ontology compliance check",
            guidelines_compliance="Error prevented guidelines compliance check"
        )

@enhanced_ttl_qa_agent.tool
@timed_operation("Execute SPARQL Query", "query")
async def execute_sparql_query(
    ctx: RunContext[EnhancedTTLContext],
    sparql_query: str
) -> str:
    """Execute a SPARQL query against the loaded TTL graph.

    Args:
        sparql_query: The SPARQL query to execute
    """
    import json

    try:
        start_time = time.time()

        # Execute the query
        results = list(ctx.deps.graph.query(sparql_query))

        execution_time = time.time() - start_time

        # Convert results to dictionaries
        data = []
        columns = []

        if results:
            # Get column names from the first result
            first_result = results[0]
            if hasattr(first_result, '_fields'):
                columns = list(first_result._fields)
            else:
                columns = [f"col_{i}" for i in range(len(first_result))]

            # Convert all results to dictionaries
            for result in results:
                if hasattr(result, '_asdict'):
                    data.append(result._asdict())
                else:
                    row_dict = {}
                    for i, value in enumerate(result):
                        row_dict[columns[i]] = str(value) if value else None
                    data.append(row_dict)

        result = {
            'success': True,
            'data': data,
            'row_count': len(results),
            'columns': columns,
            'execution_time': execution_time
        }
        return json.dumps(result)

    except Exception as e:
        result = {
            'success': False,
            'error_message': str(e),
            'execution_time': 0.0,
            'data': [],
            'row_count': 0,
            'columns': []
        }
        return json.dumps(result)

@enhanced_ttl_qa_agent.tool
@timed_operation("Analyze Contextual Results", "tool_call")
async def analyze_contextual_results(
    ctx: RunContext[EnhancedTTLContext],
    query_result: str,
    original_question: str,
    ontology_context: Optional[str] = None,
    guidelines_context: Optional[str] = None
) -> ContextualInsight:
    """Analyze query results with available contextual awareness.

    Args:
        query_result: The result from executing a SPARQL query (as JSON string)
        original_question: The original user question for context
        ontology_context: Optional ontology constraints
        guidelines_context: Optional guidelines and validation rules
    """
    import json

    try:
        # Parse the query result based on its type
        if isinstance(query_result, str):
            try:
                result_data = json.loads(query_result)
            except json.JSONDecodeError:
                # If it's not valid JSON, treat as error message
                return ContextualInsight(
                    insight=f"Failed to parse query result: {query_result[:100]}...",
                    confidence=0.1,
                    supporting_data="Query result parsing error - invalid JSON string",
                    ontology_context="Could not analyze ontology compliance due to parsing error",
                    guidelines_context="Could not analyze guidelines compliance due to parsing error"
                )
        elif isinstance(query_result, list):
            # Handle case where agent passes raw results as list
            columns = []
            if query_result and len(query_result) > 0:
                first_item = query_result[0]
                if isinstance(first_item, dict):
                    columns = list(first_item.keys())
                elif hasattr(first_item, '_fields'):
                    columns = list(first_item._fields)
                elif hasattr(first_item, 'keys'):
                    try:
                        columns = list(first_item.keys())
                    except (AttributeError, TypeError):
                        columns = []

            result_data = {
                'success': True,
                'data': query_result,
                'row_count': len(query_result),
                'columns': columns,
                'execution_time': 0.0
            }
        elif isinstance(query_result, dict):
            # Already a dict, use as-is
            result_data = query_result
        else:
            # Unknown type, try to convert to string and handle
            return ContextualInsight(
                insight=f"Unexpected query result type: {type(query_result).__name__}",
                confidence=0.1,
                supporting_data=f"Query result type {type(query_result).__name__} is not supported",
                ontology_context="Could not analyze ontology compliance due to unsupported result type",
                guidelines_context="Could not analyze guidelines compliance due to unsupported result type"
            )
    except (TypeError, AttributeError, KeyError) as e:
        return ContextualInsight(
            insight=f"Error processing query result: {str(e)}",
            confidence=0.1,
            supporting_data="Query result processing error",
            ontology_context="Could not analyze ontology compliance due to processing error",
            guidelines_context="Could not analyze guidelines compliance due to processing error"
        )

    # Safely check if query was successful
    success = False
    if isinstance(result_data, dict):
        success = result_data.get('success', False)

    if not success:
        error_message = "Unknown error"
        if isinstance(result_data, dict):
            error_message = result_data.get('error_message', 'Unknown error')

        return ContextualInsight(
            insight=f"Query failed: {error_message}",
            confidence=0.9,
            supporting_data="Query execution error - may indicate constraint violations",
            ontology_context="Query failure prevented ontology compliance analysis",
            guidelines_context="Query failure prevented guidelines compliance analysis"
        )

    # Safely get row count
    row_count = 0
    if isinstance(result_data, dict):
        row_count = result_data.get('row_count', 0)

    if row_count == 0:
        # Handle optional contexts for empty results
        ontology_msg = f"Ontology constraints may have limited results: {ontology_context[:100]}..." if ontology_context else "No ontology constraints applied"
        guidelines_msg = f"Guidelines constraints may have affected filtering: {guidelines_context[:100]}..." if guidelines_context else "No guidelines constraints applied"

        return ContextualInsight(
            insight="No data found matching the query criteria",
            confidence=0.8,
            supporting_data="Empty result set - may indicate overly restrictive constraints or missing data",
            ontology_context=ontology_msg,
            guidelines_context=guidelines_msg
        )

    # Enhanced analysis based on result structure and context
    question_lower = original_question.lower()
    insight = f"Found {row_count} results"

    # Safely get columns
    columns = []
    if isinstance(result_data, dict):
        columns = result_data.get('columns', [])
    if columns:
        insight += f" with columns: {', '.join(columns)}"

    # Add context about dataset coverage and constraint compliance
    if any(word in question_lower for word in ["how many", "count", "number"]) and row_count == 1:
        # This is likely a count query result
        data = []
        if isinstance(result_data, dict):
            data = result_data.get('data', [])
        if data and len(data) > 0 and data[0]:
            if isinstance(data[0], dict):
                count_value = list(data[0].values())[0] if data[0].values() else "unknown"
                insight += f" (count: {count_value})"

    # Enhanced sample data with context
    data = []
    if isinstance(result_data, dict):
        data = result_data.get('data', [])
    sample_data = f"Sample results (showing up to 3): "
    for i, row in enumerate(data[:3]):
        sample_data += f"Row {i+1}: {row}; "

    # Determine confidence based on result quality and context compliance
    confidence = 0.8  # Default
    if row_count > 0:
        confidence = 0.9  # Good results
        if "count" in question_lower and row_count == 1:
            confidence = 0.95  # Count queries are typically very accurate

    # Analyze ontology compliance (handle optional context)
    if ontology_context:
        ontology_analysis = "Results appear to comply with ontology constraints"
        if "domain" in ontology_context.lower() and "range" in ontology_context.lower():
            ontology_analysis += " - domain/range relationships were respected in query"
    else:
        ontology_analysis = "No ontology constraints applied - used basic TTL analysis only"

    # Analyze guidelines compliance (handle optional context)
    if guidelines_context:
        guidelines_analysis = "Results follow guidelines specifications"
        if "required" in guidelines_context.lower():
            guidelines_analysis += " - required properties were considered"
        if "type" in guidelines_context.lower():
            guidelines_analysis += " - data types were validated"
    else:
        guidelines_analysis = "No guidelines constraints applied - used basic TTL analysis only"

    return ContextualInsight(
        insight=insight,
        confidence=confidence,
        supporting_data=sample_data.strip(),
        ontology_context=ontology_analysis,
        guidelines_context=guidelines_analysis
    )
