@prefix ibpdi: <https://ibpdi.datacat.org/class/> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:AreaMeasurement ;
    rdfs:range ibpdi:Building .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:AreaMeasurement ;
    rdfs:range ibpdi:Floor .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:AreaMeasurement ;
    rdfs:range ibpdi:Land .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:AreaMeasurement ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:AreaMeasurement ;
    rdfs:range ibpdi:Site .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:AreaMeasurement ;
    rdfs:range ibpdi:Space .

ibpdi:hasUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:AreaMeasurement ;
    rdfs:range ibpdi:Unit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Certificate ;
    rdfs:range ibpdi:Building .

ibpdi:hasComponent a owl:ObjectProperty ;
    rdfs:domain ibpdi:Certificate ;
    rdfs:range ibpdi:Component .

ibpdi:hasSystem a owl:ObjectProperty ;
    rdfs:domain ibpdi:Certificate ;
    rdfs:range ibpdi:System .

ibpdi:hasValuation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Certificate ;
    rdfs:range ibpdi:Valuation .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Component ;
    rdfs:range ibpdi:Building .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:Component ;
    rdfs:range ibpdi:Floor .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Component ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Component ;
    rdfs:range ibpdi:Site .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:Component ;
    rdfs:range ibpdi:Space .

ibpdi:hasSystem a owl:ObjectProperty ;
    rdfs:domain ibpdi:Component ;
    rdfs:range ibpdi:System .

ibpdi:hasUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Component ;
    rdfs:range ibpdi:Unit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:Building .

ibpdi:hasComponent a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:Component .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:Floor .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:Land .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:Site .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:Space .

ibpdi:hasUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Sensor ;
    rdfs:range ibpdi:Unit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:System ;
    rdfs:range ibpdi:Building .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:System ;
    rdfs:range ibpdi:Floor .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:System ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:System ;
    rdfs:range ibpdi:Site .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:System ;
    rdfs:range ibpdi:Space .

ibpdi:hasUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:System ;
    rdfs:range ibpdi:Unit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Unit ;
    rdfs:range ibpdi:Building .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:Unit ;
    rdfs:range ibpdi:Floor .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:Unit ;
    rdfs:range ibpdi:Land .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Unit ;
    rdfs:range ibpdi:Site .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:Unit ;
    rdfs:range ibpdi:Space .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Climate ;
    rdfs:range ibpdi:Building .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Climate ;
    rdfs:range ibpdi:Site .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:EmissionFactor ;
    rdfs:range ibpdi:Building .

ibpdi:hasOperationalMeasurement a owl:ObjectProperty ;
    rdfs:domain ibpdi:EmissionFactor ;
    rdfs:range ibpdi:OperationalMeasurement .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:EmissionFactor ;
    rdfs:range ibpdi:Site .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:GhgEmission ;
    rdfs:range ibpdi:Building .

ibpdi:hasEmissionFactor a owl:ObjectProperty ;
    rdfs:domain ibpdi:GhgEmission ;
    rdfs:range ibpdi:EmissionFactor .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:GhgEmission ;
    rdfs:range ibpdi:Site .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:OperationalMeasurement ;
    rdfs:range ibpdi:Building .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:OperationalMeasurement ;
    rdfs:range ibpdi:Floor .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:OperationalMeasurement ;
    rdfs:range ibpdi:Land .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:OperationalMeasurement ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:OperationalMeasurement ;
    rdfs:range ibpdi:Site .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:OperationalMeasurement ;
    rdfs:range ibpdi:Space .

ibpdi:hasUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:OperationalMeasurement ;
    rdfs:range ibpdi:Unit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:CostCenter ;
    rdfs:range ibpdi:Building .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:CostCenter ;
    rdfs:range ibpdi:Land .

ibpdi:hasPortfolio a owl:ObjectProperty ;
    rdfs:domain ibpdi:CostCenter ;
    rdfs:range ibpdi:Portfolio .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:CostCenter ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:CostCenter ;
    rdfs:range ibpdi:Site .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:IndividualAccount ;
    rdfs:range ibpdi:Building .

ibpdi:hasPortfolio a owl:ObjectProperty ;
    rdfs:domain ibpdi:IndividualAccount ;
    rdfs:range ibpdi:Portfolio .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:IndividualAccount ;
    rdfs:range ibpdi:Site .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Portfolio ;
    rdfs:range ibpdi:Building .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:Portfolio ;
    rdfs:range ibpdi:Land .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Portfolio ;
    rdfs:range ibpdi:Site .

ibpdi:hasUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Portfolio ;
    rdfs:range ibpdi:Unit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Valuation ;
    rdfs:range ibpdi:Building .

ibpdi:hasIndividualAccount a owl:ObjectProperty ;
    rdfs:domain ibpdi:Valuation ;
    rdfs:range ibpdi:IndividualAccount .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:Valuation ;
    rdfs:range ibpdi:Land .

ibpdi:hasOperationalMeasurement a owl:ObjectProperty ;
    rdfs:domain ibpdi:Valuation ;
    rdfs:range ibpdi:OperationalMeasurement .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:RentalContract ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:RentalUnit ;
    rdfs:range ibpdi:Building .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:RentalUnit ;
    rdfs:range ibpdi:Floor .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:RentalUnit ;
    rdfs:range ibpdi:Land .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:RentalUnit ;
    rdfs:range ibpdi:Site .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:RentalUnit ;
    rdfs:range ibpdi:Space .

ibpdi:hasUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:RentalUnit ;
    rdfs:range ibpdi:Unit .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Address ;
    rdfs:range ibpdi:Building .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Address ;
    rdfs:range ibpdi:Contact .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:Address ;
    rdfs:range ibpdi:Land .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Address ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Address ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Address ;
    rdfs:range ibpdi:Site .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Building .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasBuilding a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Building .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasComponent a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Component .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasComponent a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Component .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasComponentType a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:ComponentType .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasComponentType a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:ComponentType .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasEmissionFactor a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:EmissionFactor .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasEmissionFactor a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:EmissionFactor .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Land .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasLand a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Land .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasOperationalMeasurement a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:OperationalMeasurement .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasOperationalMeasurement a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:OperationalMeasurement .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasPortfolio a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Portfolio .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasPortfolio a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Portfolio .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasPortfolioStrategy a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:PortfolioStrategy .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasPortfolioStrategy a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:PortfolioStrategy .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasRentalContract a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:RentalContract .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasRentalContract a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:RentalContract .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasRentalUnit a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:RentalUnit .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Site .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasSite a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Site .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasSystem a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:System .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasSystem a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:System .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasTenantCommunication a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:TenantCommunication .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasTenantCommunication a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:TenantCommunication .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasValuation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Valuation .

ibpdi:hasContact a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Contact .

ibpdi:hasValuation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Valuation .

ibpdi:hasOrganisation a owl:ObjectProperty ;
    rdfs:domain ibpdi:Role ;
    rdfs:range ibpdi:Organisation .

ibpdi:hasEmissionFactor a owl:ObjectProperty ;
    rdfs:domain ibpdi:SustainabilityIndicator ;
    rdfs:range ibpdi:EmissionFactor .

ibpdi:hasWorkspace a owl:ObjectProperty ;
    rdfs:domain ibpdi:AvailableResource ;
    rdfs:range ibpdi:Workspace .

ibpdi:hasWorkspace a owl:ObjectProperty ;
    rdfs:domain ibpdi:Booking ;
    rdfs:range ibpdi:Workspace .

ibpdi:hasNeighbourhood a owl:ObjectProperty ;
    rdfs:domain ibpdi:Characteristic ;
    rdfs:range ibpdi:Neighbourhood .

ibpdi:hasWorkspace a owl:ObjectProperty ;
    rdfs:domain ibpdi:Characteristic ;
    rdfs:range ibpdi:Workspace .

ibpdi:hasWorkArea a owl:ObjectProperty ;
    rdfs:domain ibpdi:CustomerFile ;
    rdfs:range ibpdi:WorkArea .

ibpdi:hasWorkspace a owl:ObjectProperty ;
    rdfs:domain ibpdi:CustomerFile ;
    rdfs:range ibpdi:Workspace .

ibpdi:hasWorkspace a owl:ObjectProperty ;
    rdfs:domain ibpdi:Neighbourhood ;
    rdfs:range ibpdi:Workspace .

ibpdi:hasNeighbourhood a owl:ObjectProperty ;
    rdfs:domain ibpdi:Tag ;
    rdfs:range ibpdi:Neighbourhood .

ibpdi:hasFloor a owl:ObjectProperty ;
    rdfs:domain ibpdi:WorkArea ;
    rdfs:range ibpdi:Floor .

ibpdi:hasSpace a owl:ObjectProperty ;
    rdfs:domain ibpdi:WorkArea ;
    rdfs:range ibpdi:Space .

ibpdi:hasSensor a owl:ObjectProperty ;
    rdfs:domain ibpdi:Workspace ;
    rdfs:range ibpdi:Sensor .