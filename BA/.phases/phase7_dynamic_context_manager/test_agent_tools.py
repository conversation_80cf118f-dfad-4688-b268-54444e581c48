"""
Comprehensive unit tests for agent tools
Tests all agent tool functions with mocking and edge case handling
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from rdflib import Graph

# Import the agent tools and related components
from core_logic import (
    enhanced_ttl_qa_agent,
    analyze_context_requirements,
    extract_ontology_context,
    extract_guidelines_context,
    develop_contextual_strategy,
    generate_enhanced_sparql_query,
    execute_sparql_query,
    analyze_contextual_results,
    EnhancedTTLContext,
    OntologyContext,
    GuidelinesContext,
    ContextRequirements,
    ContextualStrategy,
    EnhancedSPARQLQuery,
    ContextualInsight
)


class TestAnalyzeContextRequirements:
    """Test cases for analyze_context_requirements tool"""

    @pytest.mark.asyncio
    async def test_analyze_context_requirements_basic(self):
        """Test basic context requirements analysis"""
        # Mock the context analysis agent
        mock_result = Mock()
        mock_result.output = ContextRequirements(
            needs_ontology=True,
            needs_guidelines=False,
            needs_analysis_only=False,
            reasoning="Question about relationships requires ontology",
            question_type="relationship",
            confidence=0.9,
            key_concepts=["relationship", "class"],
            expected_complexity="medium"
        )
        
        with patch('core_logic.context_analysis_agent.run', new_callable=AsyncMock) as mock_agent:
            mock_agent.return_value = mock_result
            
            # Create mock context
            mock_context = Mock()
            mock_context.deps = Mock()
            
            result = await analyze_context_requirements(mock_context, "What are the relationships between classes?")
            
            assert isinstance(result, ContextRequirements)
            assert result.needs_ontology is True
            assert result.needs_guidelines is False
            assert result.question_type == "relationship"
            mock_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_analyze_context_requirements_agent_error(self):
        """Test context requirements analysis when agent fails"""
        with patch('core_logic.context_analysis_agent.run', new_callable=AsyncMock) as mock_agent:
            mock_agent.side_effect = Exception("Agent failed")
            
            mock_context = Mock()
            mock_context.deps = Mock()
            
            result = await analyze_context_requirements(mock_context, "Test question")
            
            # Should return fallback conservative approach
            assert isinstance(result, ContextRequirements)
            assert result.needs_ontology is True
            assert result.needs_guidelines is True
            assert result.confidence == 0.5
            assert "error_fallback" in result.key_concepts

    @pytest.mark.asyncio
    async def test_analyze_context_requirements_empty_question(self):
        """Test context requirements analysis with empty question"""
        mock_result = Mock()
        mock_result.output = ContextRequirements(
            needs_ontology=False,
            needs_guidelines=False,
            needs_analysis_only=True,
            reasoning="Empty question requires basic analysis",
            question_type="unknown",
            confidence=0.3,
            key_concepts=[],
            expected_complexity="low"
        )
        
        with patch('core_logic.context_analysis_agent.run', new_callable=AsyncMock) as mock_agent:
            mock_agent.return_value = mock_result
            
            mock_context = Mock()
            result = await analyze_context_requirements(mock_context, "")
            
            assert isinstance(result, ContextRequirements)
            assert result.needs_analysis_only is True


class TestExtractOntologyContext:
    """Test cases for extract_ontology_context tool"""

    @pytest.mark.asyncio
    async def test_extract_ontology_context_basic(self):
        """Test basic ontology context extraction"""
        # Create mock ontology context
        ontology_context = OntologyContext(
            class_relationships={"ClassA": ["ClassB", "ClassC"]},
            property_domains={"hasProperty": ["ClassA"]},
            property_ranges={"hasProperty": ["ClassB"]},
            class_properties={"ClassA": ["hasProperty", "otherProp"]},
            property_constraints={"hasProperty": {"domains": ["ClassA"], "ranges": ["ClassB"]}}
        )
        
        # Create mock enhanced TTL context
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.ontology_context = ontology_context
        
        result = await extract_ontology_context(
            mock_context, 
            "What properties does ClassA have?",
            entities_mentioned=["ClassA"]
        )
        
        assert isinstance(result, str)
        assert "RELEVANT CLASS RELATIONSHIPS" in result
        assert "ClassA connects to: ClassB, ClassC" in result
        assert "ClassA has properties: hasProperty, otherProp" in result

    @pytest.mark.asyncio
    async def test_extract_ontology_context_no_entities_mentioned(self):
        """Test ontology context extraction without specific entities"""
        ontology_context = OntologyContext(
            class_relationships={"TestClass": ["RelatedClass"]},
            property_domains={"testProperty": ["TestClass"]},
            property_ranges={"testProperty": ["RelatedClass"]},
            class_properties={"TestClass": ["testProperty"]},
            property_constraints={"testProperty": {"domains": ["TestClass"], "ranges": ["RelatedClass"]}}
        )
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.ontology_context = ontology_context
        
        result = await extract_ontology_context(
            mock_context, 
            "What is the relationship between TestClass and other classes?"
        )
        
        assert isinstance(result, str)
        # Should find TestClass mentioned in the question
        assert "TestClass" in result

    @pytest.mark.asyncio
    async def test_extract_ontology_context_no_matches(self):
        """Test ontology context extraction with no relevant matches"""
        ontology_context = OntologyContext(
            class_relationships={"ClassA": ["ClassB"]},
            property_domains={"prop1": ["ClassA"]},
            property_ranges={"prop1": ["ClassB"]},
            class_properties={"ClassA": ["prop1"]},
            property_constraints={"prop1": {"domains": ["ClassA"], "ranges": ["ClassB"]}}
        )
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.ontology_context = ontology_context
        
        result = await extract_ontology_context(
            mock_context, 
            "Tell me about completely unrelated things"
        )
        
        assert isinstance(result, str)
        assert "GENERAL ONTOLOGY GUIDANCE" in result
        assert "Available class relationships:" in result

    @pytest.mark.asyncio
    async def test_extract_ontology_context_empty_ontology(self):
        """Test ontology context extraction with empty ontology"""
        ontology_context = OntologyContext({}, {}, {}, {}, {})
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.ontology_context = ontology_context
        
        result = await extract_ontology_context(mock_context, "Any question")
        
        assert isinstance(result, str)
        assert "GENERAL ONTOLOGY GUIDANCE" in result

    @pytest.mark.asyncio
    async def test_extract_ontology_context_malformed_data(self):
        """Test ontology context extraction with malformed data"""
        # Create ontology context with None values and malformed data
        ontology_context = OntologyContext(
            class_relationships={"ClassA": None},  # None instead of list
            property_domains=None,  # None instead of dict
            property_ranges={"prop1": ["Range1"]},
            class_properties={"ClassA": ["prop1"]},
            property_constraints={"prop1": None}  # None constraint
        )
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.ontology_context = ontology_context
        
        result = await extract_ontology_context(mock_context, "Question about ClassA")
        
        # Should handle malformed data gracefully
        assert isinstance(result, str)


class TestExtractGuidelinesContext:
    """Test cases for extract_guidelines_context tool"""

    @pytest.mark.asyncio
    async def test_extract_guidelines_context_basic(self):
        """Test basic guidelines context extraction"""
        guidelines_context = GuidelinesContext(
            property_definitions={
                "testProperty": {
                    "description": "A test property",
                    "class": "TestClass"
                }
            },
            validation_rules={
                "testProperty": {
                    "required": True,
                    "min_value": 0,
                    "max_value": 100
                }
            },
            required_properties={"TestClass": ["testProperty"]},
            property_types={"testProperty": "integer"},
            property_units={"testProperty": "meters (m)"}
        )
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.guidelines_context = guidelines_context
        
        result = await extract_guidelines_context(
            mock_context,
            "What are the validation rules for testProperty?",
            properties_mentioned=["testProperty"]
        )
        
        assert isinstance(result, str)
        assert "RELEVANT PROPERTY DEFINITIONS" in result
        assert "testProperty: A test property (type: integer)" in result
        assert "Required property" in result
        assert "Min value: 0" in result
        assert "Max value: 100" in result
        assert "Units: meters (m)" in result

    @pytest.mark.asyncio
    async def test_extract_guidelines_context_no_properties_mentioned(self):
        """Test guidelines context extraction without specific properties"""
        guidelines_context = GuidelinesContext(
            property_definitions={"height": {"description": "Height measurement"}},
            validation_rules={"height": {"required": False}},
            required_properties={},
            property_types={"height": "float"},
            property_units={"height": "meters"}
        )
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.guidelines_context = guidelines_context
        
        result = await extract_guidelines_context(
            mock_context,
            "What are the validation rules for height measurements?"
        )
        
        assert isinstance(result, str)
        # Should find "height" mentioned in the question
        assert "height" in result.lower()

    @pytest.mark.asyncio
    async def test_extract_guidelines_context_no_matches(self):
        """Test guidelines context extraction with no relevant matches"""
        guidelines_context = GuidelinesContext(
            property_definitions={"prop1": {"description": "Property 1"}},
            validation_rules={},
            required_properties={"Class1": ["prop1"]},
            property_types={"prop1": "string"},
            property_units={}
        )
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.guidelines_context = guidelines_context
        
        result = await extract_guidelines_context(
            mock_context,
            "Tell me about completely unrelated validation"
        )
        
        assert isinstance(result, str)
        assert "GENERAL GUIDELINES GUIDANCE" in result

    @pytest.mark.asyncio
    async def test_extract_guidelines_context_empty_guidelines(self):
        """Test guidelines context extraction with empty guidelines"""
        guidelines_context = GuidelinesContext({}, {}, {}, {}, {})
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.guidelines_context = guidelines_context
        
        result = await extract_guidelines_context(mock_context, "Any question")
        
        assert isinstance(result, str)
        assert "GENERAL GUIDELINES GUIDANCE" in result

    @pytest.mark.asyncio
    async def test_extract_guidelines_context_partial_data(self):
        """Test guidelines context extraction with partial data"""
        guidelines_context = GuidelinesContext(
            property_definitions={"prop1": {"description": "Test prop"}},
            validation_rules={},  # No validation rules
            required_properties={},  # No required properties
            property_types={},  # No type info
            property_units={}  # No unit info
        )
        
        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.guidelines_context = guidelines_context
        
        result = await extract_guidelines_context(
            mock_context,
            "What about prop1?",
            properties_mentioned=["prop1"]
        )
        
        assert isinstance(result, str)
        assert "prop1: Test prop" in result


class TestDevelopContextualStrategy:
    """Test cases for develop_contextual_strategy tool"""

    @pytest.mark.asyncio
    async def test_develop_contextual_strategy_counting_question(self):
        """Test strategy development for counting questions"""
        mock_context = Mock()
        mock_context.deps = Mock()
        
        result = await develop_contextual_strategy(
            mock_context,
            "How many instances of ClassA are there?",
            ontology_context="Ontology info about ClassA",
            guidelines_context="Guidelines for ClassA"
        )
        
        assert isinstance(result, ContextualStrategy)
        assert "counting" in result.approach.lower()
        assert result.expected_queries == 1
        assert "COUNT" in result.steps[2]  # Should mention COUNT query
        assert "Ontology info about ClassA" in result.ontology_considerations

    @pytest.mark.asyncio
    async def test_develop_contextual_strategy_exploratory_question(self):
        """Test strategy development for exploratory questions"""
        mock_context = Mock()
        mock_context.deps = Mock()
        
        result = await develop_contextual_strategy(
            mock_context,
            "What properties does ClassA have?",
            ontology_context="Ontology relationships",
            guidelines_context=None  # No guidelines context
        )
        
        assert isinstance(result, ContextualStrategy)
        assert "exploratory" in result.approach.lower()
        assert result.expected_queries == 2
        assert "No guidelines context loaded" in result.guidelines_considerations

    @pytest.mark.asyncio
    async def test_develop_contextual_strategy_comparative_question(self):
        """Test strategy development for comparative questions"""
        mock_context = Mock()
        mock_context.deps = Mock()
        
        result = await develop_contextual_strategy(
            mock_context,
            "What is the difference between ClassA and ClassB?",
            ontology_context=None,  # No ontology context
            guidelines_context="Guidelines for comparison"
        )
        
        assert isinstance(result, ContextualStrategy)
        assert "comparative" in result.approach.lower()
        assert result.expected_queries == 3
        assert "No ontology context loaded" in result.ontology_considerations

    @pytest.mark.asyncio
    async def test_develop_contextual_strategy_general_question(self):
        """Test strategy development for general questions"""
        mock_context = Mock()
        mock_context.deps = Mock()
        
        result = await develop_contextual_strategy(
            mock_context,
            "Tell me about the data structure",
            ontology_context="Full ontology context",
            guidelines_context="Full guidelines context"
        )
        
        assert isinstance(result, ContextualStrategy)
        assert "general" in result.approach.lower()
        assert result.expected_queries == 2
        assert len(result.steps) > 0

    @pytest.mark.asyncio
    async def test_develop_contextual_strategy_no_context(self):
        """Test strategy development with no additional context"""
        mock_context = Mock()
        mock_context.deps = Mock()
        
        result = await develop_contextual_strategy(
            mock_context,
            "Any question"
        )
        
        assert isinstance(result, ContextualStrategy)
        assert "No ontology context loaded" in result.ontology_considerations
        assert "No guidelines context loaded" in result.guidelines_considerations

    @pytest.mark.asyncio
    async def test_develop_contextual_strategy_long_context(self):
        """Test strategy development with very long context strings"""
        long_context = "A" * 500  # Very long context string
        
        mock_context = Mock()
        mock_context.deps = Mock()
        
        result = await develop_contextual_strategy(
            mock_context,
            "Test question",
            ontology_context=long_context,
            guidelines_context=long_context
        )
        
        assert isinstance(result, ContextualStrategy)
        # Should truncate long context
        assert len(result.ontology_considerations) < 250
        assert len(result.guidelines_considerations) < 250
        assert "..." in result.ontology_considerations
        assert "..." in result.guidelines_considerations


class TestGenerateEnhancedSPARQLQuery:
    """Test cases for generate_enhanced_sparql_query tool"""

    @pytest.mark.asyncio
    async def test_generate_enhanced_sparql_query_basic(self):
        """Test basic enhanced SPARQL query generation"""
        mock_result = Mock()
        mock_result.output = EnhancedSPARQLQuery(
            query="SELECT ?s WHERE { ?s a ?type }",
            query_type="SELECT",
            description="Test query",
            confidence=0.9,
            validation_passed=True,
            ontology_compliance="Compliant",
            guidelines_compliance="Follows guidelines"
        )

        with patch('core_logic.enhanced_sparql_agent.run', new_callable=AsyncMock) as mock_agent:
            mock_agent.return_value = mock_result

            mock_context = Mock()
            mock_context.deps = Mock()
            mock_context.deps.basic_analysis = "Basic TTL analysis"

            result = await generate_enhanced_sparql_query(
                mock_context,
                "What types are available?",
                ontology_context="Ontology info",
                guidelines_context="Guidelines info"
            )

            assert isinstance(result, EnhancedSPARQLQuery)
            assert result.query == "SELECT ?s WHERE { ?s a ?type }"
            assert result.query_type == "SELECT"
            mock_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_enhanced_sparql_query_no_context(self):
        """Test SPARQL query generation without additional context"""
        mock_result = Mock()
        mock_result.output = EnhancedSPARQLQuery(
            query="SELECT * WHERE { ?s ?p ?o }",
            query_type="SELECT",
            description="Basic query",
            confidence=0.7,
            validation_passed=True,
            ontology_compliance="Basic compliance",
            guidelines_compliance="Basic compliance"
        )

        with patch('core_logic.enhanced_sparql_agent.run', new_callable=AsyncMock) as mock_agent:
            mock_agent.return_value = mock_result

            mock_context = Mock()
            mock_context.deps = Mock()
            mock_context.deps.basic_analysis = "Basic analysis"

            result = await generate_enhanced_sparql_query(
                mock_context,
                "Show me all data"
            )

            assert isinstance(result, EnhancedSPARQLQuery)
            mock_agent.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_enhanced_sparql_query_agent_error(self):
        """Test SPARQL query generation when agent fails"""
        with patch('core_logic.enhanced_sparql_agent.run', new_callable=AsyncMock) as mock_agent:
            mock_agent.side_effect = Exception("Agent failed")

            mock_context = Mock()
            mock_context.deps = Mock()
            mock_context.deps.basic_analysis = "Basic analysis"

            result = await generate_enhanced_sparql_query(
                mock_context,
                "Test question"
            )

            # Should return error query
            assert isinstance(result, EnhancedSPARQLQuery)
            assert result.query_type == "ERROR"
            assert "Agent failed" in result.description
            assert result.confidence == 0.1
            assert result.validation_passed is False


class TestExecuteSPARQLQuery:
    """Test cases for execute_sparql_query tool"""

    @pytest.mark.asyncio
    async def test_execute_sparql_query_success(self):
        """Test successful SPARQL query execution"""
        # Create mock graph with query results
        mock_graph = Mock()
        mock_result1 = Mock()
        mock_result1._fields = ['s', 'p', 'o']
        mock_result1._asdict.return_value = {'s': 'subject1', 'p': 'property1', 'o': 'object1'}

        mock_result2 = Mock()
        mock_result2._fields = ['s', 'p', 'o']
        mock_result2._asdict.return_value = {'s': 'subject2', 'p': 'property2', 'o': 'object2'}

        mock_graph.query.return_value = [mock_result1, mock_result2]

        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.graph = mock_graph

        with patch('time.time', side_effect=[1000.0, 1001.5]):
            result_str = await execute_sparql_query(
                mock_context,
                "SELECT ?s ?p ?o WHERE { ?s ?p ?o }"
            )

        result = json.loads(result_str)

        assert result['success'] is True
        assert result['row_count'] == 2
        assert result['columns'] == ['s', 'p', 'o']
        assert len(result['data']) == 2
        assert result['data'][0] == {'s': 'subject1', 'p': 'property1', 'o': 'object1'}
        assert result['execution_time'] == 1.5

    @pytest.mark.asyncio
    async def test_execute_sparql_query_empty_results(self):
        """Test SPARQL query execution with empty results"""
        mock_graph = Mock()
        mock_graph.query.return_value = []

        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.graph = mock_graph

        result_str = await execute_sparql_query(
            mock_context,
            "SELECT ?s WHERE { ?s a <http://example.org/NonExistentClass> }"
        )

        result = json.loads(result_str)

        assert result['success'] is True
        assert result['row_count'] == 0
        assert result['data'] == []
        assert result['columns'] == []

    @pytest.mark.asyncio
    async def test_execute_sparql_query_no_fields(self):
        """Test SPARQL query execution with results that have no _fields"""
        mock_graph = Mock()
        mock_result = ('subject1', 'property1', 'object1')  # Tuple without _fields
        mock_graph.query.return_value = [mock_result]

        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.graph = mock_graph

        result_str = await execute_sparql_query(
            mock_context,
            "SELECT ?s ?p ?o WHERE { ?s ?p ?o }"
        )

        result = json.loads(result_str)

        assert result['success'] is True
        assert result['row_count'] == 1
        assert result['columns'] == ['col_0', 'col_1', 'col_2']
        assert result['data'][0] == {'col_0': 'subject1', 'col_1': 'property1', 'col_2': 'object1'}

    @pytest.mark.asyncio
    async def test_execute_sparql_query_error(self):
        """Test SPARQL query execution with error"""
        mock_graph = Mock()
        mock_graph.query.side_effect = Exception("Query execution failed")

        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.graph = mock_graph

        result_str = await execute_sparql_query(
            mock_context,
            "INVALID SPARQL QUERY"
        )

        result = json.loads(result_str)

        assert result['success'] is False
        assert "Query execution failed" in result['error_message']
        assert result['row_count'] == 0
        assert result['data'] == []

    @pytest.mark.asyncio
    async def test_execute_sparql_query_none_values(self):
        """Test SPARQL query execution with None values in results"""
        mock_graph = Mock()
        mock_result = Mock()
        mock_result._fields = ['s', 'p', 'o']
        mock_result._asdict.return_value = {'s': 'subject1', 'p': None, 'o': 'object1'}

        mock_graph.query.return_value = [mock_result]

        mock_context = Mock()
        mock_context.deps = Mock()
        mock_context.deps.graph = mock_graph

        result_str = await execute_sparql_query(
            mock_context,
            "SELECT ?s ?p ?o WHERE { ?s ?p ?o }"
        )

        result = json.loads(result_str)

        assert result['success'] is True
        assert result['data'][0] == {'s': 'subject1', 'p': None, 'o': 'object1'}


class TestAnalyzeContextualResults:
    """Test cases for analyze_contextual_results tool"""

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_success_with_data(self):
        """Test contextual results analysis with successful query results"""
        query_result = json.dumps({
            'success': True,
            'data': [
                {'name': 'John', 'age': 30},
                {'name': 'Jane', 'age': 25}
            ],
            'row_count': 2,
            'columns': ['name', 'age'],
            'execution_time': 0.5
        })

        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            query_result,
            "What are the names and ages?",
            ontology_context="Ontology constraints applied",
            guidelines_context="Guidelines followed"
        )

        assert isinstance(result, ContextualInsight)
        assert "Found 2 results" in result.insight
        assert "name, age" in result.insight
        assert result.confidence >= 0.8
        assert "Ontology constraints applied" in result.ontology_context
        assert "Guidelines followed" in result.guidelines_context

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_empty_results(self):
        """Test contextual results analysis with empty results"""
        query_result = json.dumps({
            'success': True,
            'data': [],
            'row_count': 0,
            'columns': [],
            'execution_time': 0.1
        })

        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            query_result,
            "Find non-existent data",
            ontology_context="Ontology applied",
            guidelines_context=None
        )

        assert isinstance(result, ContextualInsight)
        assert "No data found" in result.insight
        assert result.confidence == 0.8
        assert "Ontology applied" in result.ontology_context
        assert "No guidelines constraints applied" in result.guidelines_context

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_count_query(self):
        """Test contextual results analysis for count queries"""
        query_result = json.dumps({
            'success': True,
            'data': [{'count': 42}],
            'row_count': 1,
            'columns': ['count'],
            'execution_time': 0.2
        })

        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            query_result,
            "How many items are there?",
            ontology_context="Ontology context",
            guidelines_context="Guidelines context"
        )

        assert isinstance(result, ContextualInsight)
        assert "Found 1 results" in result.insight
        assert "(count: 42)" in result.insight
        assert result.confidence == 0.95  # Count queries are highly accurate

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_query_failure(self):
        """Test contextual results analysis with query failure"""
        query_result = json.dumps({
            'success': False,
            'error_message': 'Syntax error in query',
            'row_count': 0,
            'data': [],
            'execution_time': 0.0
        })

        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            query_result,
            "Invalid query test"
        )

        assert isinstance(result, ContextualInsight)
        assert "Query failed: Syntax error in query" in result.insight
        assert result.confidence == 0.9

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_invalid_json(self):
        """Test contextual results analysis with invalid JSON"""
        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            "Invalid JSON string",
            "Test question"
        )

        assert isinstance(result, ContextualInsight)
        assert "Failed to parse query result" in result.insight
        assert result.confidence == 0.1

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_list_input(self):
        """Test contextual results analysis with list input"""
        query_result = [
            {'name': 'Alice', 'age': 28},
            {'name': 'Bob', 'age': 32}
        ]

        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            query_result,
            "Get user data"
        )

        assert isinstance(result, ContextualInsight)
        assert "Found 2 results" in result.insight

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_dict_input(self):
        """Test contextual results analysis with dict input"""
        query_result = {
            'success': True,
            'data': [{'item': 'test'}],
            'row_count': 1
        }

        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            query_result,
            "Test query"
        )

        assert isinstance(result, ContextualInsight)
        assert "Found 1 results" in result.insight

    @pytest.mark.asyncio
    async def test_analyze_contextual_results_unsupported_type(self):
        """Test contextual results analysis with unsupported input type"""
        mock_context = Mock()
        mock_context.deps = Mock()

        result = await analyze_contextual_results(
            mock_context,
            123,  # Unsupported type
            "Test question"
        )

        assert isinstance(result, ContextualInsight)
        assert "Unexpected query result type" in result.insight
        assert result.confidence == 0.1
