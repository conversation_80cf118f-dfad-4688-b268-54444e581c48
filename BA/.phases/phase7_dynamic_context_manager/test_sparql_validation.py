"""
Comprehensive unit tests for SPARQL validation
Tests validate_sparql_query function with various query types and malformed queries
"""

import pytest
from unittest.mock import patch, Mock

# Import the SPARQL validation function
from core_logic import validate_sparql_query


class TestValidateSPARQLQuery:
    """Test cases for validate_sparql_query function"""

    def test_validate_sparql_query_select(self):
        """Test validation of SELECT queries"""
        valid_select_queries = [
            "SELECT * WHERE { ?s ?p ?o }",
            "SELECT ?s ?p WHERE { ?s ?p ?o }",
            "select ?subject where { ?subject a ?type }",  # lowercase
            "SELECT DISTINCT ?s WHERE { ?s ?p ?o }",
            "SELECT ?s WHERE { ?s ?p ?o } LIMIT 10",
            "SELECT ?s WHERE { ?s ?p ?o } ORDER BY ?s",
            """
            PREFIX ex: <http://example.org/>
            SELECT ?s WHERE { 
                ?s ex:property ?value 
            }
            """,
        ]
        
        for query in valid_select_queries:
            result = validate_sparql_query(query)
            assert result == "SELECT", f"Failed for query: {query}"

    def test_validate_sparql_query_ask(self):
        """Test validation of ASK queries"""
        valid_ask_queries = [
            "ASK { ?s ?p ?o }",
            "ask { ?s a ?type }",  # lowercase
            "ASK WHERE { ?s ?p ?o }",
            """
            PREFIX ex: <http://example.org/>
            ASK { 
                ?s ex:property ?value 
            }
            """,
        ]
        
        for query in valid_ask_queries:
            result = validate_sparql_query(query)
            assert result == "ASK", f"Failed for query: {query}"

    def test_validate_sparql_query_construct(self):
        """Test validation of CONSTRUCT queries"""
        valid_construct_queries = [
            "CONSTRUCT { ?s ?p ?o } WHERE { ?s ?p ?o }",
            "construct { ?s a ?type } where { ?s ?p ?o }",  # lowercase
            """
            PREFIX ex: <http://example.org/>
            CONSTRUCT { 
                ?s ex:newProperty ?value 
            } WHERE { 
                ?s ex:oldProperty ?value 
            }
            """,
        ]
        
        for query in valid_construct_queries:
            result = validate_sparql_query(query)
            assert result == "CONSTRUCT", f"Failed for query: {query}"

    def test_validate_sparql_query_describe(self):
        """Test validation of DESCRIBE queries"""
        valid_describe_queries = [
            "DESCRIBE ?s WHERE { ?s ?p ?o }",
            "describe <http://example.org/resource>",  # lowercase
            "DESCRIBE * WHERE { ?s ?p ?o }",
            """
            PREFIX ex: <http://example.org/>
            DESCRIBE ?s WHERE { 
                ?s ex:property ?value 
            }
            """,
        ]
        
        for query in valid_describe_queries:
            result = validate_sparql_query(query)
            assert result == "DESCRIBE", f"Failed for query: {query}"

    def test_validate_sparql_query_unknown_type(self):
        """Test validation of queries with unknown/custom types"""
        # These are syntactically valid but don't match standard SPARQL query types
        unknown_queries = [
            "# This is just a comment",
            "PREFIX ex: <http://example.org/>",  # Just a prefix declaration
        ]
        
        for query in unknown_queries:
            with patch('rdflib.plugins.sparql.prepareQuery') as mock_prepare:
                mock_prepare.return_value = Mock()  # Simulate successful parsing
                result = validate_sparql_query(query)
                assert result == "UNKNOWN", f"Failed for query: {query}"

    def test_validate_sparql_query_empty_string(self):
        """Test validation with empty string"""
        with pytest.raises(ValueError, match="Query must be a non-empty string"):
            validate_sparql_query("")

    def test_validate_sparql_query_none(self):
        """Test validation with None input"""
        with pytest.raises(ValueError, match="Query must be a non-empty string"):
            validate_sparql_query(None)

    def test_validate_sparql_query_non_string(self):
        """Test validation with non-string input"""
        non_string_inputs = [123, [], {}, True, False]
        
        for input_val in non_string_inputs:
            with pytest.raises(ValueError, match="Query must be a non-empty string"):
                validate_sparql_query(input_val)

    def test_validate_sparql_query_whitespace_only(self):
        """Test validation with whitespace-only string"""
        whitespace_queries = ["   ", "\t", "\n", "\r\n", "   \t\n   "]
        
        for query in whitespace_queries:
            with pytest.raises(ValueError, match="Query must be a non-empty string"):
                validate_sparql_query(query)

    def test_validate_sparql_query_invalid_syntax(self):
        """Test validation with invalid SPARQL syntax"""
        invalid_queries = [
            "INVALID SPARQL QUERY",
            "SELECT WHERE",  # Missing variables and graph pattern
            "SELECT ?s WHERE",  # Missing graph pattern
            "SELECT ?s WHERE { }",  # Empty graph pattern
            "{ ?s ?p ?o }",  # Missing query type
            "SELECT ?s WHERE { ?s ?p }",  # Incomplete triple pattern
            "SELECT ?s WHERE { ?s ?p ?o",  # Missing closing brace
            "SELECT ?s WHERE ?s ?p ?o }",  # Missing opening brace
            "SELCT ?s WHERE { ?s ?p ?o }",  # Typo in SELECT
            "SELECT ?s WERE { ?s ?p ?o }",  # Typo in WHERE
        ]
        
        for query in invalid_queries:
            with pytest.raises(ValueError, match="Invalid SPARQL syntax"):
                validate_sparql_query(query)

    def test_validate_sparql_query_complex_valid_queries(self):
        """Test validation with complex but valid SPARQL queries"""
        complex_queries = [
            """
            PREFIX foaf: <http://xmlns.com/foaf/0.1/>
            PREFIX ex: <http://example.org/>
            
            SELECT ?name ?email WHERE {
                ?person foaf:name ?name .
                ?person foaf:mbox ?email .
                FILTER(CONTAINS(?name, "John"))
            }
            ORDER BY ?name
            LIMIT 10
            """,
            """
            PREFIX ex: <http://example.org/>
            
            CONSTRUCT {
                ?person ex:hasFullName ?fullName
            } WHERE {
                ?person ex:firstName ?first .
                ?person ex:lastName ?last .
                BIND(CONCAT(?first, " ", ?last) AS ?fullName)
            }
            """,
            """
            PREFIX ex: <http://example.org/>
            
            ASK {
                ?person ex:age ?age .
                FILTER(?age > 18)
            }
            """,
        ]
        
        for query in complex_queries:
            # These should not raise exceptions
            result = validate_sparql_query(query)
            assert result in ["SELECT", "CONSTRUCT", "ASK"]

    def test_validate_sparql_query_case_insensitive(self):
        """Test that query type detection is case insensitive"""
        case_variations = [
            ("select ?s where { ?s ?p ?o }", "SELECT"),
            ("Select ?s Where { ?s ?p ?o }", "SELECT"),
            ("SELECT ?s WHERE { ?s ?p ?o }", "SELECT"),
            ("ask { ?s ?p ?o }", "ASK"),
            ("Ask { ?s ?p ?o }", "ASK"),
            ("ASK { ?s ?p ?o }", "ASK"),
            ("construct { ?s ?p ?o } where { ?s ?p ?o }", "CONSTRUCT"),
            ("Construct { ?s ?p ?o } Where { ?s ?p ?o }", "CONSTRUCT"),
            ("CONSTRUCT { ?s ?p ?o } WHERE { ?s ?p ?o }", "CONSTRUCT"),
            ("describe ?s where { ?s ?p ?o }", "DESCRIBE"),
            ("Describe ?s Where { ?s ?p ?o }", "DESCRIBE"),
            ("DESCRIBE ?s WHERE { ?s ?p ?o }", "DESCRIBE"),
        ]
        
        for query, expected_type in case_variations:
            result = validate_sparql_query(query)
            assert result == expected_type, f"Failed for query: {query}"

    def test_validate_sparql_query_multiple_keywords(self):
        """Test queries that contain multiple query type keywords"""
        # These queries contain multiple keywords but should be classified by the first/primary one
        multi_keyword_queries = [
            ("SELECT ?s WHERE { ?s ?p ?o } # This also mentions ASK", "SELECT"),
            ("CONSTRUCT { ?s ?p ?o } WHERE { ?s ?p ?o } # SELECT mentioned in comment", "CONSTRUCT"),
            # Note: These are edge cases and behavior may depend on implementation
        ]
        
        for query, expected_type in multi_keyword_queries:
            result = validate_sparql_query(query)
            assert result == expected_type, f"Failed for query: {query}"

    def test_validate_sparql_query_with_comments(self):
        """Test validation of queries with comments"""
        queries_with_comments = [
            """
            # This is a comment
            SELECT ?s WHERE { ?s ?p ?o }
            """,
            """
            SELECT ?s WHERE { 
                # Another comment
                ?s ?p ?o 
            }
            """,
        ]
        
        for query in queries_with_comments:
            result = validate_sparql_query(query)
            assert result == "SELECT"

    def test_validate_sparql_query_rdflib_exception(self):
        """Test handling of rdflib parsing exceptions"""
        with patch('rdflib.plugins.sparql.prepareQuery') as mock_prepare:
            mock_prepare.side_effect = Exception("RDFLib parsing error")
            
            with pytest.raises(ValueError, match="Invalid SPARQL syntax: RDFLib parsing error"):
                validate_sparql_query("SELECT ?s WHERE { ?s ?p ?o }")

    def test_validate_sparql_query_edge_case_whitespace(self):
        """Test queries with unusual whitespace patterns"""
        whitespace_queries = [
            "   SELECT   ?s   WHERE   {   ?s   ?p   ?o   }   ",
            "\t\nSELECT\t?s\nWHERE\t{\n?s\t?p\t?o\n}\t",
            "SELECT\r\n?s\r\nWHERE\r\n{\r\n?s\r\n?p\r\n?o\r\n}",
        ]
        
        for query in whitespace_queries:
            result = validate_sparql_query(query)
            assert result == "SELECT"

    def test_validate_sparql_query_unicode_content(self):
        """Test queries with Unicode content"""
        unicode_queries = [
            'SELECT ?s WHERE { ?s <http://example.org/名前> "田中" }',
            'SELECT ?s WHERE { ?s <http://example.org/título> "José" }',
            'ASK { ?s <http://example.org/property> "测试" }',
        ]
        
        for query in unicode_queries:
            # Should handle Unicode content without issues
            result = validate_sparql_query(query)
            assert result in ["SELECT", "ASK"]

    def test_validate_sparql_query_very_long_query(self):
        """Test validation with very long queries"""
        # Create a very long but valid query
        long_query = "SELECT ?s WHERE {\n"
        for i in range(1000):
            long_query += f"  ?s <http://example.org/prop{i}> ?val{i} .\n"
        long_query += "}"
        
        result = validate_sparql_query(long_query)
        assert result == "SELECT"

    def test_validate_sparql_query_minimal_valid_queries(self):
        """Test minimal valid queries for each type"""
        minimal_queries = [
            ("SELECT * WHERE { ?s ?p ?o }", "SELECT"),
            ("ASK { ?s ?p ?o }", "ASK"),
            ("CONSTRUCT { ?s ?p ?o } WHERE { ?s ?p ?o }", "CONSTRUCT"),
            ("DESCRIBE ?s WHERE { ?s ?p ?o }", "DESCRIBE"),
        ]
        
        for query, expected_type in minimal_queries:
            result = validate_sparql_query(query)
            assert result == expected_type
