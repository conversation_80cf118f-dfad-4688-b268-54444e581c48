"""
Comprehensive unit tests for analysis functions
Tests analyze_ontology, analyze_guidelines, and TTL analysis functions with edge cases
"""

import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch, mock_open
from rdflib import Graph, Namespace, URIRef, Literal
from rdflib.exceptions import ParserError

# Import the analysis functions
from core_logic import (
    analyze_ontology, analyze_guidelines, analyze_ttl_file_basic, 
    analyze_ttl_file_enhanced, OntologyContext, GuidelinesContext
)


class TestAnalyzeOntology:
    """Test cases for analyze_ontology function"""

    def create_test_ontology_file(self, content):
        """Helper to create temporary ontology file"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        temp_file.write(content)
        temp_file.close()
        return temp_file.name

    def test_analyze_ontology_basic(self):
        """Test basic ontology analysis"""
        ontology_content = """
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        @prefix ibpdi: <https://ibpdi.datacat.org/class/> .
        
        ibpdi:hasProperty rdfs:domain ibpdi:ClassA .
        ibpdi:hasProperty rdfs:range ibpdi:ClassB .
        """
        
        temp_file = self.create_test_ontology_file(ontology_content)
        
        try:
            result = analyze_ontology(temp_file)
            
            assert isinstance(result, OntologyContext)
            assert "hasProperty" in result.property_domains
            assert "ClassA" in result.property_domains["hasProperty"]
            assert "hasProperty" in result.property_ranges
            assert "ClassB" in result.property_ranges["hasProperty"]
            assert "ClassA" in result.class_relationships
            assert "ClassB" in result.class_relationships["ClassA"]
            assert "ClassA" in result.class_properties
            assert "hasProperty" in result.class_properties["ClassA"]
            
        finally:
            os.unlink(temp_file)

    def test_analyze_ontology_multiple_properties(self):
        """Test ontology analysis with multiple properties"""
        ontology_content = """
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        @prefix ibpdi: <https://ibpdi.datacat.org/class/> .
        
        ibpdi:prop1 rdfs:domain ibpdi:ClassA .
        ibpdi:prop1 rdfs:range ibpdi:ClassB .
        ibpdi:prop2 rdfs:domain ibpdi:ClassA .
        ibpdi:prop2 rdfs:range ibpdi:ClassC .
        ibpdi:prop3 rdfs:domain ibpdi:ClassB .
        ibpdi:prop3 rdfs:range ibpdi:ClassC .
        """
        
        temp_file = self.create_test_ontology_file(ontology_content)
        
        try:
            result = analyze_ontology(temp_file)
            
            # Check multiple properties for ClassA
            assert "ClassA" in result.class_properties
            assert "prop1" in result.class_properties["ClassA"]
            assert "prop2" in result.class_properties["ClassA"]
            
            # Check class relationships
            assert "ClassA" in result.class_relationships
            assert "ClassB" in result.class_relationships["ClassA"]
            assert "ClassC" in result.class_relationships["ClassA"]
            
            # Check property constraints
            assert "prop1" in result.property_constraints
            assert result.property_constraints["prop1"]["domains"] == ["ClassA"]
            assert result.property_constraints["prop1"]["ranges"] == ["ClassB"]
            
        finally:
            os.unlink(temp_file)

    def test_analyze_ontology_empty_file(self):
        """Test ontology analysis with empty file"""
        temp_file = self.create_test_ontology_file("")
        
        try:
            result = analyze_ontology(temp_file)
            
            assert isinstance(result, OntologyContext)
            assert result.class_relationships == {}
            assert result.property_domains == {}
            assert result.property_ranges == {}
            assert result.class_properties == {}
            assert result.property_constraints == {}
            
        finally:
            os.unlink(temp_file)

    def test_analyze_ontology_invalid_file(self):
        """Test ontology analysis with invalid TTL file"""
        ontology_content = "This is not valid TTL content"
        temp_file = self.create_test_ontology_file(ontology_content)
        
        try:
            result = analyze_ontology(temp_file)
            
            # Should return empty context on error
            assert isinstance(result, OntologyContext)
            assert result.class_relationships == {}
            assert result.property_domains == {}
            assert result.property_ranges == {}
            assert result.class_properties == {}
            assert result.property_constraints == {}
            
        finally:
            os.unlink(temp_file)

    def test_analyze_ontology_nonexistent_file(self):
        """Test ontology analysis with nonexistent file"""
        result = analyze_ontology("/nonexistent/file.ttl")
        
        # Should return empty context on error
        assert isinstance(result, OntologyContext)
        assert result.class_relationships == {}

    def test_analyze_ontology_complex_namespaces(self):
        """Test ontology analysis with complex namespace URIs"""
        ontology_content = """
        @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
        @prefix test: <http://example.org/test/very/long/namespace/path/> .
        
        test:complexProperty rdfs:domain test:ComplexClass .
        test:complexProperty rdfs:range test:AnotherComplexClass .
        """
        
        temp_file = self.create_test_ontology_file(ontology_content)
        
        try:
            result = analyze_ontology(temp_file)
            
            assert "complexProperty" in result.property_domains
            assert "ComplexClass" in result.property_domains["complexProperty"]
            assert "AnotherComplexClass" in result.property_ranges["complexProperty"]
            
        finally:
            os.unlink(temp_file)


class TestAnalyzeGuidelines:
    """Test cases for analyze_guidelines function"""

    def create_test_guidelines_file(self, content):
        """Helper to create temporary guidelines JSON file"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(content, temp_file)
        temp_file.close()
        return temp_file.name

    def test_analyze_guidelines_basic(self):
        """Test basic guidelines analysis"""
        guidelines_data = {
            "Domain": {
                "Classifications": {
                    "$values": [
                        {
                            "Name": "TestClass",
                            "ClassificationProperties": {
                                "$values": [
                                    {
                                        "Name": "TestProperty",
                                        "Description": "A test property",
                                        "IsRequired": True,
                                        "PropertyAssignment": {
                                            "Property": {
                                                "StorageType": 1,
                                                "UnitType": "Length",
                                                "UnitAbbreviation": "m",
                                                "Identifier": "test_prop_id"
                                            },
                                            "Min": 0,
                                            "Max": 100,
                                            "MinIsInclusive": True,
                                            "MaxIsInclusive": False
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
        
        temp_file = self.create_test_guidelines_file(guidelines_data)
        
        try:
            result = analyze_guidelines(temp_file)
            
            assert isinstance(result, GuidelinesContext)
            assert "TestProperty" in result.property_definitions
            assert result.property_definitions["TestProperty"]["description"] == "A test property"
            assert result.property_definitions["TestProperty"]["class"] == "TestClass"
            
            assert "TestProperty" in result.validation_rules
            assert result.validation_rules["TestProperty"]["required"] is True
            assert result.validation_rules["TestProperty"]["min_value"] == 0
            assert result.validation_rules["TestProperty"]["max_value"] == 100
            
            assert "TestClass" in result.required_properties
            assert "TestProperty" in result.required_properties["TestClass"]
            
            assert "TestProperty" in result.property_types
            assert result.property_types["TestProperty"] == "integer"
            
            assert "TestProperty" in result.property_units
            assert result.property_units["TestProperty"] == "Length (m)"
            
        finally:
            os.unlink(temp_file)

    def test_analyze_guidelines_multiple_properties(self):
        """Test guidelines analysis with multiple properties"""
        guidelines_data = {
            "Domain": {
                "Classifications": {
                    "$values": [
                        {
                            "Name": "TestClass",
                            "ClassificationProperties": {
                                "$values": [
                                    {
                                        "Name": "Property1",
                                        "Description": "First property",
                                        "IsRequired": True,
                                        "PropertyAssignment": {
                                            "Property": {"StorageType": 1}
                                        }
                                    },
                                    {
                                        "Name": "Property2",
                                        "Description": "Second property",
                                        "IsRequired": False,
                                        "PropertyAssignment": {
                                            "Property": {"StorageType": 4}
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
        
        temp_file = self.create_test_guidelines_file(guidelines_data)
        
        try:
            result = analyze_guidelines(temp_file)
            
            assert len(result.property_definitions) == 2
            assert "Property1" in result.property_definitions
            assert "Property2" in result.property_definitions
            
            assert result.property_types["Property1"] == "integer"
            assert result.property_types["Property2"] == "string"
            
            assert len(result.required_properties["TestClass"]) == 1
            assert "Property1" in result.required_properties["TestClass"]
            assert "Property2" not in result.required_properties["TestClass"]
            
        finally:
            os.unlink(temp_file)

    def test_analyze_guidelines_storage_type_mapping(self):
        """Test guidelines analysis with different storage types"""
        storage_types = [
            (1, "integer"),
            (2, "float"),
            (3, "boolean"),
            (4, "string"),
            (5, "datetime"),
            (999, "string")  # Unknown type should default to string
        ]
        
        for storage_type, expected_type in storage_types:
            guidelines_data = {
                "Domain": {
                    "Classifications": {
                        "$values": [
                            {
                                "Name": "TestClass",
                                "ClassificationProperties": {
                                    "$values": [
                                        {
                                            "Name": "TestProperty",
                                            "Description": "Test property",
                                            "IsRequired": False,
                                            "PropertyAssignment": {
                                                "Property": {"StorageType": storage_type}
                                            }
                                        }
                                    ]
                                }
                            }
                        ]
                    }
                }
            }
            
            temp_file = self.create_test_guidelines_file(guidelines_data)
            
            try:
                result = analyze_guidelines(temp_file)
                assert result.property_types["TestProperty"] == expected_type
            finally:
                os.unlink(temp_file)

    def test_analyze_guidelines_empty_file(self):
        """Test guidelines analysis with empty JSON"""
        temp_file = self.create_test_guidelines_file({})
        
        try:
            result = analyze_guidelines(temp_file)
            
            assert isinstance(result, GuidelinesContext)
            assert result.property_definitions == {}
            assert result.validation_rules == {}
            assert result.required_properties == {}
            assert result.property_types == {}
            assert result.property_units == {}
            
        finally:
            os.unlink(temp_file)

    def test_analyze_guidelines_malformed_json(self):
        """Test guidelines analysis with malformed JSON"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        temp_file.write("{ invalid json content")
        temp_file.close()
        
        try:
            result = analyze_guidelines(temp_file.name)
            
            # Should return empty context on error
            assert isinstance(result, GuidelinesContext)
            assert result.property_definitions == {}
            
        finally:
            os.unlink(temp_file.name)

    def test_analyze_guidelines_nonexistent_file(self):
        """Test guidelines analysis with nonexistent file"""
        result = analyze_guidelines("/nonexistent/file.json")
        
        # Should return empty context on error
        assert isinstance(result, GuidelinesContext)
        assert result.property_definitions == {}

    def test_analyze_guidelines_missing_fields(self):
        """Test guidelines analysis with missing fields"""
        guidelines_data = {
            "Domain": {
                "Classifications": {
                    "$values": [
                        {
                            "Name": "TestClass",
                            "ClassificationProperties": {
                                "$values": [
                                    {
                                        # Missing Name, Description, IsRequired
                                        "PropertyAssignment": {
                                            "Property": {}  # Missing StorageType
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
        
        temp_file = self.create_test_guidelines_file(guidelines_data)
        
        try:
            result = analyze_guidelines(temp_file)
            
            # Should handle missing fields gracefully
            assert isinstance(result, GuidelinesContext)
            assert "" in result.property_definitions  # Empty name
            assert result.property_definitions[""]["description"] == ""
            assert result.property_types[""] == "string"  # Default storage type
            
        finally:
            os.unlink(temp_file)

    def test_analyze_guidelines_utf8_bom(self):
        """Test guidelines analysis with UTF-8 BOM"""
        guidelines_data = {"Domain": {"Classifications": {"$values": []}}}
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8-sig')
        json.dump(guidelines_data, temp_file)
        temp_file.close()
        
        try:
            result = analyze_guidelines(temp_file.name)
            
            # Should handle UTF-8 BOM correctly
            assert isinstance(result, GuidelinesContext)
            
        finally:
            os.unlink(temp_file.name)


class TestAnalyzeTTLFileBasic:
    """Test cases for analyze_ttl_file_basic function"""

    def create_test_ttl_file(self, content):
        """Helper to create temporary TTL file"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.ttl', delete=False)
        temp_file.write(content)
        temp_file.close()
        return temp_file.name

    def test_analyze_ttl_file_basic_simple(self):
        """Test basic TTL file analysis"""
        ttl_content = """
        @prefix ex: <http://example.org/> .
        @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
        
        ex:subject1 rdf:type ex:Class1 .
        ex:subject1 ex:property1 "value1" .
        ex:subject2 rdf:type ex:Class2 .
        """
        
        temp_file = self.create_test_ttl_file(ttl_content)
        
        try:
            result = analyze_ttl_file_basic(temp_file)
            
            assert isinstance(result, str)
            assert "Total triples: 3" in result
            assert "Classes found: 2" in result
            assert "Properties found: 1" in result  # Excluding rdf:type
            assert "ex:Class1" in result
            assert "ex:Class2" in result
            assert "ex:property1" in result
            
        finally:
            os.unlink(temp_file)

    def test_analyze_ttl_file_basic_empty(self):
        """Test TTL file analysis with empty file"""
        temp_file = self.create_test_ttl_file("")
        
        try:
            result = analyze_ttl_file_basic(temp_file)
            
            assert isinstance(result, str)
            assert "Total triples: 0" in result
            assert "Classes found: 0" in result
            assert "Properties found: 0" in result
            
        finally:
            os.unlink(temp_file)

    def test_analyze_ttl_file_basic_invalid(self):
        """Test TTL file analysis with invalid TTL"""
        temp_file = self.create_test_ttl_file("Invalid TTL content")
        
        try:
            result = analyze_ttl_file_basic(temp_file)
            
            assert isinstance(result, str)
            assert "Error in basic TTL analysis:" in result
            
        finally:
            os.unlink(temp_file)

    def test_analyze_ttl_file_basic_nonexistent(self):
        """Test TTL file analysis with nonexistent file"""
        result = analyze_ttl_file_basic("/nonexistent/file.ttl")
        
        assert isinstance(result, str)
        assert "Error in basic TTL analysis:" in result

    def test_analyze_ttl_file_basic_large_file(self):
        """Test TTL file analysis with many triples"""
        ttl_content = "@prefix ex: <http://example.org/> .\n"
        ttl_content += "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .\n"
        
        # Add many triples
        for i in range(100):
            ttl_content += f"ex:subject{i} rdf:type ex:Class{i % 5} .\n"
            ttl_content += f"ex:subject{i} ex:property{i % 3} \"value{i}\" .\n"
        
        temp_file = self.create_test_ttl_file(ttl_content)
        
        try:
            result = analyze_ttl_file_basic(temp_file)
            
            assert "Total triples: 200" in result
            assert "Classes found: 5" in result
            assert "Properties found: 3" in result
            
        finally:
            os.unlink(temp_file)


class TestAnalyzeTTLFileEnhanced:
    """Test cases for analyze_ttl_file_enhanced function"""

    def test_analyze_ttl_file_enhanced_mock(self):
        """Test enhanced TTL file analysis with mocked dependencies"""
        with patch('core_logic.analyze_ttl_file_basic') as mock_basic, \
             patch('core_logic.analyze_ontology') as mock_ontology, \
             patch('core_logic.analyze_guidelines') as mock_guidelines, \
             patch('core_logic.format_class_relationships') as mock_format_class, \
             patch('core_logic.format_property_constraints') as mock_format_prop, \
             patch('core_logic.format_property_definitions') as mock_format_def, \
             patch('core_logic.format_required_properties') as mock_format_req, \
             patch('core_logic.format_property_types_units') as mock_format_types:
            
            # Set up mocks
            mock_basic.return_value = "Basic analysis"
            mock_ontology.return_value = Mock()
            mock_guidelines.return_value = Mock()
            mock_format_class.return_value = "Class relationships"
            mock_format_prop.return_value = "Property constraints"
            mock_format_def.return_value = "Property definitions"
            mock_format_req.return_value = "Required properties"
            mock_format_types.return_value = "Property types"
            
            result = analyze_ttl_file_enhanced("test.ttl", "ontology.ttl", "guidelines.json")
            
            assert isinstance(result, str)
            assert "Basic analysis" in result
            assert "ENHANCED ONTOLOGY CONTEXT" in result
            assert "GUIDELINES CONTEXT" in result
            assert "CONTEXTUAL QUERY GUIDANCE" in result
            
            # Verify all functions were called
            mock_basic.assert_called_once_with("test.ttl")
            mock_ontology.assert_called_once_with("ontology.ttl")
            mock_guidelines.assert_called_once_with("guidelines.json")

    def test_analyze_ttl_file_enhanced_error(self):
        """Test enhanced TTL file analysis with error"""
        with patch('core_logic.analyze_ttl_file_basic', side_effect=Exception("Test error")):
            result = analyze_ttl_file_enhanced("test.ttl", "ontology.ttl", "guidelines.json")
            
            assert isinstance(result, str)
            assert "Error in enhanced TTL analysis: Test error" in result
