#!/usr/bin/env python3
"""
Master test runner for all components
Executes all unit tests and integration tests
"""

import asyncio
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def run_comprehensive_tests():
    """Run all tests comprehensively"""
    print("🚀 COMPREHENSIVE TEST SUITE")
    print("=" * 80)
    print("Testing separated TTL Question-Answering System")
    print("Core Logic + CLI + Agent Tools + Edge Cases")
    print("=" * 80)
    
    total_passed = 0
    total_failed = 0
    
    # Test 1: Run comprehensive unit tests
    print("\n📋 PHASE 1: COMPREHENSIVE UNIT TESTS")
    print("-" * 50)
    
    try:
        from run_all_tests import main as run_unit_tests
        unit_success = await run_unit_tests()
        if unit_success:
            print("✅ Unit tests: ALL PASSED")
            total_passed += 1
        else:
            print("❌ Unit tests: SOME FAILED")
            total_failed += 1
    except Exception as e:
        print(f"❌ Unit tests: ERROR - {e}")
        total_failed += 1
    
    # Test 2: Run integration tests
    print("\n📋 PHASE 2: INTEGRATION TESTS")
    print("-" * 50)
    
    try:
        from test_integration import main as run_integration_tests
        integration_success = await run_integration_tests()
        if integration_success:
            print("✅ Integration tests: ALL PASSED")
            total_passed += 1
        else:
            print("❌ Integration tests: SOME FAILED")
            total_failed += 1
    except Exception as e:
        print(f"❌ Integration tests: ERROR - {e}")
        total_failed += 1
    
    # Test 3: Test the specific fix
    print("\n📋 PHASE 3: SPECIFIC BUG FIX VERIFICATION")
    print("-" * 50)
    
    try:
        from test_fix import test_list_fix
        fix_success = await test_list_fix()
        if fix_success:
            print("✅ Bug fix verification: PASSED")
            total_passed += 1
        else:
            print("❌ Bug fix verification: FAILED")
            total_failed += 1
    except Exception as e:
        print(f"❌ Bug fix verification: ERROR - {e}")
        total_failed += 1
    
    # Test 4: Import verification
    print("\n📋 PHASE 4: IMPORT AND MODULE VERIFICATION")
    print("-" * 50)
    
    try:
        # Test core_logic imports
        import core_logic
        print("✅ core_logic module imports successfully")
        
        # Test CLI imports
        from cli import EnhancedTTLQACLI
        print("✅ CLI module imports successfully")
        
        # Test main module imports
        import main
        print("✅ main module imports successfully")
        
        # Test that all key components are available
        required_components = [
            'TimingEntry', 'TimingTracker', 'timed_operation',
            'OntologyContext', 'GuidelinesContext', 'EnhancedTTLContext',
            'ContextualInsight', 'EnhancedSPARQLQuery', 'ContextualStrategy', 'ContextRequirements',
            'analyze_ontology', 'analyze_guidelines', 'validate_sparql_query',
            'enhanced_ttl_qa_agent'
        ]
        
        missing_components = []
        for component in required_components:
            if not hasattr(core_logic, component):
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ Missing components: {missing_components}")
            total_failed += 1
        else:
            print("✅ All required components are available")
            total_passed += 1
            
    except Exception as e:
        print(f"❌ Import verification: ERROR - {e}")
        total_failed += 1
    
    # Test 5: Basic functionality test
    print("\n📋 PHASE 5: BASIC FUNCTIONALITY TEST")
    print("-" * 50)
    
    try:
        # Test timing system
        from core_logic import TimingEntry, TimingTracker
        entry = TimingEntry("test", 1000.0)
        tracker = TimingTracker()
        print("✅ Timing system works")
        
        # Test data models
        from core_logic import OntologyContext, GuidelinesContext
        ontology = OntologyContext({}, {}, {}, {}, {})
        guidelines = GuidelinesContext({}, {}, {}, {}, {})
        print("✅ Data models work")
        
        # Test SPARQL validation
        from core_logic import validate_sparql_query
        result = validate_sparql_query("SELECT * WHERE { ?s ?p ?o }")
        assert result == "SELECT"
        print("✅ SPARQL validation works")
        
        # Test formatting functions
        from core_logic import format_class_relationships
        result = format_class_relationships(None)
        assert "No class relationships found" in result
        print("✅ Formatting functions work")
        
        total_passed += 1
        
    except Exception as e:
        print(f"❌ Basic functionality test: ERROR - {e}")
        total_failed += 1
    
    # Final summary
    print("\n" + "=" * 80)
    print("🏁 FINAL TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Test Phases Passed: {total_passed}")
    print(f"❌ Test Phases Failed: {total_failed}")
    print(f"📈 Overall Success Rate: {total_passed / (total_passed + total_failed) * 100:.1f}%")
    
    if total_failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Code separation successful")
        print("✅ All edge cases handled")
        print("✅ 'list' object has no attribute 'get' error fixed")
        print("✅ Comprehensive unit tests pass")
        print("✅ Integration tests pass")
        print("✅ All modules import correctly")
        print("✅ System is ready for production")
    else:
        print(f"\n⚠️  {total_failed} test phases failed")
        print("Please review the errors above and fix any issues")
    
    return total_failed == 0

def test_imports_only():
    """Quick test to verify imports work"""
    print("🔍 Quick import test...")
    
    try:
        import core_logic
        print("✅ core_logic imported")
        
        from cli import EnhancedTTLQACLI
        print("✅ CLI imported")
        
        import main
        print("✅ main imported")
        
        print("✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting master test suite...")
    
    # First, test imports
    if not test_imports_only():
        print("❌ Import test failed, cannot proceed with full tests")
        sys.exit(1)
    
    # Run comprehensive tests
    try:
        success = asyncio.run(run_comprehensive_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        sys.exit(1)
