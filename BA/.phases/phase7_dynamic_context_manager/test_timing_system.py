"""
Comprehensive unit tests for the timing system
Tests <PERSON>ing<PERSON><PERSON><PERSON>, TimingTracker, and timed_operation decorator with all edge cases
"""

import pytest
import time
import asyncio
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict

# Import the timing system components
from core_logic import TimingEntry, TimingTracker, timed_operation


class TestTimingEntry:
    """Test cases for TimingEntry class"""

    def test_timing_entry_creation(self):
        """Test basic TimingEntry creation"""
        entry = TimingEntry(
            name="test_operation",
            start_time=1000.0,
            step_type="operation",
            details="test details"
        )
        
        assert entry.name == "test_operation"
        assert entry.start_time == 1000.0
        assert entry.end_time is None
        assert entry.duration is None
        assert entry.step_type == "operation"
        assert entry.details == "test details"
        assert entry.sub_entries == []

    def test_timing_entry_creation_with_defaults(self):
        """Test TimingEntry creation with default values"""
        entry = TimingEntry(name="test", start_time=1000.0)
        
        assert entry.name == "test"
        assert entry.start_time == 1000.0
        assert entry.end_time is None
        assert entry.duration is None
        assert entry.step_type == "operation"
        assert entry.details is None
        assert entry.sub_entries == []

    def test_timing_entry_finish(self):
        """Test finishing a timing entry"""
        with patch('time.time', return_value=1005.0):
            entry = TimingEntry(name="test", start_time=1000.0)
            entry.finish("completed successfully")
            
            assert entry.end_time == 1005.0
            assert entry.duration == 5.0
            assert entry.details == "completed successfully"

    def test_timing_entry_finish_without_details(self):
        """Test finishing a timing entry without details"""
        with patch('time.time', return_value=1003.5):
            entry = TimingEntry(name="test", start_time=1000.0, details="initial")
            entry.finish()
            
            assert entry.end_time == 1003.5
            assert entry.duration == 3.5
            assert entry.details == "initial"  # Should keep original details

    def test_timing_entry_finish_with_details_override(self):
        """Test finishing a timing entry with details override"""
        with patch('time.time', return_value=1002.0):
            entry = TimingEntry(name="test", start_time=1000.0, details="initial")
            entry.finish("final details")
            
            assert entry.end_time == 1002.0
            assert entry.duration == 2.0
            assert entry.details == "final details"

    def test_add_sub_entry(self):
        """Test adding sub-entries to a timing entry"""
        parent = TimingEntry(name="parent", start_time=1000.0)
        child1 = TimingEntry(name="child1", start_time=1001.0)
        child2 = TimingEntry(name="child2", start_time=1002.0)
        
        parent.add_sub_entry(child1)
        parent.add_sub_entry(child2)
        
        assert len(parent.sub_entries) == 2
        assert parent.sub_entries[0] == child1
        assert parent.sub_entries[1] == child2

    def test_add_sub_entry_none_handling(self):
        """Test adding None as sub-entry (edge case)"""
        parent = TimingEntry(name="parent", start_time=1000.0)
        
        # This should not raise an error
        parent.add_sub_entry(None)
        assert len(parent.sub_entries) == 1
        assert parent.sub_entries[0] is None

    def test_timing_entry_with_negative_duration(self):
        """Test edge case where end_time is before start_time"""
        with patch('time.time', return_value=999.0):  # Earlier than start_time
            entry = TimingEntry(name="test", start_time=1000.0)
            entry.finish()
            
            assert entry.end_time == 999.0
            assert entry.duration == -1.0  # Negative duration


class TestTimingTracker:
    """Test cases for TimingTracker class"""

    def setup_method(self):
        """Set up fresh TimingTracker for each test"""
        self.tracker = TimingTracker()

    def test_timing_tracker_creation(self):
        """Test basic TimingTracker creation"""
        with patch('time.time', return_value=1000.0):
            tracker = TimingTracker()
            
            assert tracker.session_start == 1000.0
            assert tracker.entries == []
            assert tracker.current_stack == []

    def test_start_operation_basic(self):
        """Test starting a basic operation"""
        with patch('time.time', return_value=1000.0):
            entry = self.tracker.start_operation("test_op", "operation", "test details")
            
            assert entry.name == "test_op"
            assert entry.start_time == 1000.0
            assert entry.step_type == "operation"
            assert entry.details == "test details"
            assert len(self.tracker.entries) == 1
            assert len(self.tracker.current_stack) == 1
            assert self.tracker.current_stack[0] == entry

    def test_start_operation_with_defaults(self):
        """Test starting operation with default parameters"""
        with patch('time.time', return_value=1000.0):
            entry = self.tracker.start_operation("test_op")
            
            assert entry.name == "test_op"
            assert entry.step_type == "operation"
            assert entry.details is None

    def test_start_nested_operations(self):
        """Test starting nested operations"""
        with patch('time.time', side_effect=[1000.0, 1001.0]):
            parent_entry = self.tracker.start_operation("parent", "operation")
            child_entry = self.tracker.start_operation("child", "tool_call")
            
            # Parent should be in entries, child should be sub-entry of parent
            assert len(self.tracker.entries) == 1
            assert len(self.tracker.current_stack) == 2
            assert self.tracker.entries[0] == parent_entry
            assert len(parent_entry.sub_entries) == 1
            assert parent_entry.sub_entries[0] == child_entry

    def test_finish_operation_basic(self):
        """Test finishing a basic operation"""
        with patch('time.time', side_effect=[1000.0, 1005.0]):
            self.tracker.start_operation("test_op")
            finished_entry = self.tracker.finish_operation("completed")
            
            assert finished_entry is not None
            assert finished_entry.name == "test_op"
            assert finished_entry.duration == 5.0
            assert finished_entry.details == "completed"
            assert len(self.tracker.current_stack) == 0

    def test_finish_operation_empty_stack(self):
        """Test finishing operation when stack is empty"""
        result = self.tracker.finish_operation("should be None")
        assert result is None

    def test_finish_nested_operations(self):
        """Test finishing nested operations in correct order"""
        with patch('time.time', side_effect=[1000.0, 1001.0, 1003.0, 1005.0]):
            parent_entry = self.tracker.start_operation("parent")
            child_entry = self.tracker.start_operation("child")
            
            # Finish child first
            finished_child = self.tracker.finish_operation("child done")
            assert finished_child == child_entry
            assert finished_child.duration == 2.0
            assert len(self.tracker.current_stack) == 1
            
            # Finish parent
            finished_parent = self.tracker.finish_operation("parent done")
            assert finished_parent == parent_entry
            assert finished_parent.duration == 5.0
            assert len(self.tracker.current_stack) == 0

    def test_get_total_duration(self):
        """Test getting total session duration"""
        with patch('time.time', side_effect=[1000.0, 1010.0]):
            tracker = TimingTracker()  # session_start = 1000.0
            duration = tracker.get_total_duration()  # current time = 1010.0
            
            assert duration == 10.0

    def test_get_tracked_operations_total(self):
        """Test getting total tracked operations time"""
        with patch('time.time', side_effect=[1000.0, 1001.0, 1003.0, 1005.0, 1008.0]):
            # Start and finish two operations
            self.tracker.start_operation("op1")
            self.tracker.finish_operation()  # duration = 2.0
            
            self.tracker.start_operation("op2")
            self.tracker.finish_operation()  # duration = 3.0
            
            total = self.tracker.get_tracked_operations_total()
            assert total == 5.0

    def test_get_tracked_operations_total_with_unfinished(self):
        """Test tracked operations total with unfinished operations"""
        with patch('time.time', side_effect=[1000.0, 1001.0, 1003.0]):
            # Finish one operation, leave one unfinished
            self.tracker.start_operation("op1")
            self.tracker.finish_operation()  # duration = 2.0
            
            self.tracker.start_operation("op2")  # Not finished
            
            total = self.tracker.get_tracked_operations_total()
            assert total == 2.0  # Only finished operations count

    def test_get_tracked_operations_total_empty(self):
        """Test tracked operations total with no operations"""
        total = self.tracker.get_tracked_operations_total()
        assert total == 0.0

    def test_display_timing_report_mock(self):
        """Test display_timing_report with mocked console"""
        mock_console = Mock()
        
        # Add some test data
        with patch('time.time', side_effect=[1000.0, 1001.0, 1003.0]):
            self.tracker.start_operation("test_op", "operation")
            self.tracker.finish_operation("completed")
        
        # This should not raise an error
        self.tracker.display_timing_report(mock_console)
        
        # Verify console.print was called
        assert mock_console.print.called

    def test_collect_stats_recursive(self):
        """Test _collect_stats method with nested entries"""
        # Create nested structure manually
        parent = TimingEntry("parent", 1000.0, step_type="operation")
        parent.finish = Mock()
        parent.duration = 5.0
        
        child1 = TimingEntry("child1", 1001.0, step_type="tool_call")
        child1.duration = 2.0
        
        child2 = TimingEntry("child2", 1003.0, step_type="analysis")
        child2.duration = 1.5
        
        parent.add_sub_entry(child1)
        parent.add_sub_entry(child2)
        self.tracker.entries = [parent]
        
        stats = {}
        self.tracker._collect_stats(self.tracker.entries, stats)
        
        assert "operation" in stats
        assert "tool_call" in stats
        assert "analysis" in stats
        assert stats["operation"] == [5.0]
        assert stats["tool_call"] == [2.0]
        assert stats["analysis"] == [1.5]


class TestTimedOperationDecorator:
    """Test cases for timed_operation decorator"""

    def setup_method(self):
        """Reset global timing tracker for each test"""
        global timing_tracker
        from core_logic import TimingTracker
        timing_tracker = TimingTracker()

    def test_timed_operation_sync_function(self):
        """Test timed_operation decorator on synchronous function"""
        @timed_operation("test_sync", "operation")
        def sync_function(x, y):
            time.sleep(0.01)  # Small delay
            return x + y
        
        with patch('time.time', side_effect=[1000.0, 1001.0, 1002.0]):
            result = sync_function(2, 3)
            
            assert result == 5
            # Check timing was recorded
            from core_logic import timing_tracker
            assert len(timing_tracker.entries) == 1
            assert timing_tracker.entries[0].name == "test_sync"

    @pytest.mark.asyncio
    async def test_timed_operation_async_function(self):
        """Test timed_operation decorator on asynchronous function"""
        @timed_operation("test_async", "tool_call")
        async def async_function(x, y):
            await asyncio.sleep(0.01)  # Small delay
            return x * y
        
        with patch('time.time', side_effect=[1000.0, 1001.0, 1002.0]):
            result = await async_function(3, 4)
            
            assert result == 12
            # Check timing was recorded
            from core_logic import timing_tracker
            assert len(timing_tracker.entries) == 1
            assert timing_tracker.entries[0].name == "test_async"
            assert timing_tracker.entries[0].step_type == "tool_call"

    def test_timed_operation_sync_exception(self):
        """Test timed_operation decorator with synchronous function that raises exception"""
        @timed_operation("test_sync_error", "operation")
        def sync_function_error():
            raise ValueError("Test error")
        
        with patch('time.time', side_effect=[1000.0, 1001.0]):
            with pytest.raises(ValueError, match="Test error"):
                sync_function_error()
            
            # Check timing was still recorded
            from core_logic import timing_tracker
            assert len(timing_tracker.entries) == 1
            assert timing_tracker.entries[0].name == "test_sync_error"
            assert "Error: Test error" in timing_tracker.entries[0].details

    @pytest.mark.asyncio
    async def test_timed_operation_async_exception(self):
        """Test timed_operation decorator with asynchronous function that raises exception"""
        @timed_operation("test_async_error", "analysis")
        async def async_function_error():
            raise RuntimeError("Async test error")
        
        with patch('time.time', side_effect=[1000.0, 1001.0]):
            with pytest.raises(RuntimeError, match="Async test error"):
                await async_function_error()
            
            # Check timing was still recorded
            from core_logic import timing_tracker
            assert len(timing_tracker.entries) == 1
            assert timing_tracker.entries[0].name == "test_async_error"
            assert "Error: Async test error" in timing_tracker.entries[0].details

    def test_timed_operation_with_args_kwargs(self):
        """Test timed_operation decorator preserves function arguments"""
        @timed_operation("test_args", "operation")
        def function_with_args(a, b, c=None, d="default"):
            return f"{a}-{b}-{c}-{d}"
        
        result = function_with_args("x", "y", c="z", d="custom")
        assert result == "x-y-z-custom"

    def test_timed_operation_nested_calls(self):
        """Test nested timed operations"""
        @timed_operation("outer", "operation")
        def outer_function():
            return inner_function() + 10
        
        @timed_operation("inner", "tool_call")
        def inner_function():
            return 5
        
        with patch('time.time', side_effect=[1000.0, 1001.0, 1002.0, 1003.0]):
            result = outer_function()
            
            assert result == 15
            # Check nested structure
            from core_logic import timing_tracker
            assert len(timing_tracker.entries) == 1
            outer_entry = timing_tracker.entries[0]
            assert outer_entry.name == "outer"
            assert len(outer_entry.sub_entries) == 1
            assert outer_entry.sub_entries[0].name == "inner"

    def test_timed_operation_default_step_type(self):
        """Test timed_operation decorator with default step_type"""
        @timed_operation("test_default")
        def test_function():
            return "test"
        
        test_function()
        
        from core_logic import timing_tracker
        assert len(timing_tracker.entries) == 1
        assert timing_tracker.entries[0].step_type == "operation"

    def test_timed_operation_preserves_function_metadata(self):
        """Test that decorator preserves function metadata"""
        @timed_operation("test_metadata", "operation")
        def original_function():
            """Original docstring"""
            return "test"
        
        assert original_function.__name__ == "original_function"
        assert original_function.__doc__ == "Original docstring"
